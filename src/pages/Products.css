.products {
  width: 100%;
}

.products-header {
  text-align: center;
  margin-bottom: 3rem;
}

.products-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 1rem;
}

.products-subtitle {
  font-size: 1.1rem;
  color: #333;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(182, 167, 167, 0.15);
  border-color: #b8c4dd;
}

.product-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 1rem;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  /* transform: scale(1.05); */
}

.product-info {
  padding: 1.5rem;
  text-align: center;
}

.product-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  transition: color 0.3s ease;
}

.product-card:hover .product-name {
  color: #2563eb;
}

.manufacturing-info {
  margin-top: 4rem;
}

.info-card {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 3rem;
  border-radius: 16px;
  text-align: center;
}

.info-card h2 {
  font-size: 2.2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.info-card > p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: left;
}

.feature h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.feature p {
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .info-card {
    padding: 2.5rem;
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .products-title {
    font-size: 2.2rem;
  }
  
  .products-subtitle {
    font-size: 1rem;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .product-image {
    height: 200px;
  }
  
  .product-info {
    padding: 1.2rem;
  }
  
  .info-card {
    padding: 2rem;
  }
  
  .info-card h2 {
    font-size: 1.8rem;
  }
  
  .info-card > p {
    font-size: 1rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .feature {
    padding: 1.2rem;
  }
}

@media (max-width: 480px) {
  .products-title {
    font-size: 1.8rem;
  }
  
  .products-subtitle {
    font-size: 0.9rem;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .product-image {
    height: 160px;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-size: 1.1rem;
  }
  
  .info-card {
    padding: 1.5rem;
  }
  
  .info-card h2 {
    font-size: 1.5rem;
  }
  
  .info-card > p {
    font-size: 0.9rem;
  }
  
  .feature {
    padding: 1rem;
  }
  
  .feature h4 {
    font-size: 1rem;
  }
  
  .feature p {
    font-size: 0.85rem;
  }
}

/* Loading and Error States */
.products-loading,
.products-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.products-error p {
  color: #dc2626;
  font-size: 1.1rem;
  margin: 0;
}

.retry-button {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.retry-button:hover {
  background: #1d4ed8;
}

.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
  font-family: 'Plus Jakarta Sans', sans-serif;
}
