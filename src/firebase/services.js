import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy,
  serverTimestamp 
} from 'firebase/firestore'
import { db } from './config'

// Products collection functions
export const productsCollection = collection(db, 'products')

export const addProduct = async (productData) => {
  try {
    const docRef = await addDoc(productsCollection, {
      ...productData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })
    return { id: docRef.id, ...productData }
  } catch (error) {
    console.error('Error adding product:', error)
    throw error
  }
}

export const getProducts = async () => {
  try {
    const q = query(productsCollection, orderBy('createdAt', 'desc'))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
  } catch (error) {
    console.error('Error getting products:', error)
    throw error
  }
}

export const updateProduct = async (id, productData) => {
  try {
    const productRef = doc(db, 'products', id)
    await updateDoc(productRef, {
      ...productData,
      updatedAt: serverTimestamp()
    })
    return { id, ...productData }
  } catch (error) {
    console.error('Error updating product:', error)
    throw error
  }
}

export const deleteProduct = async (id) => {
  try {
    await deleteDoc(doc(db, 'products', id))
    return id
  } catch (error) {
    console.error('Error deleting product:', error)
    throw error
  }
}

// Contact form submissions collection functions
export const contactCollection = collection(db, 'contacts')

export const addContactSubmission = async (contactData) => {
  try {
    const docRef = await addDoc(contactCollection, {
      ...contactData,
      createdAt: serverTimestamp(),
      status: 'unread'
    })
    return { id: docRef.id, ...contactData }
  } catch (error) {
    console.error('Error adding contact submission:', error)
    throw error
  }
}

export const getContactSubmissions = async () => {
  try {
    const q = query(contactCollection, orderBy('createdAt', 'desc'))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
  } catch (error) {
    console.error('Error getting contact submissions:', error)
    throw error
  }
}

export const updateContactStatus = async (id, status) => {
  try {
    const contactRef = doc(db, 'contacts', id)
    await updateDoc(contactRef, {
      status,
      updatedAt: serverTimestamp()
    })
    return { id, status }
  } catch (error) {
    console.error('Error updating contact status:', error)
    throw error
  }
}

export const deleteContactSubmission = async (id) => {
  try {
    await deleteDoc(doc(db, 'contacts', id))
    return id
  } catch (error) {
    console.error('Error deleting contact submission:', error)
    throw error
  }
}
