.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Loading states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading::after {
  content: '';
  width: 40px;
  height: 40px;
  border: 4px solid var(--border);
  border-top: 4px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Print styles */
@media print {
  .header,
  .footer,
  .mobile-menu-btn,
  .mobile-nav {
    display: none !important;
  }

  .container {
    margin: 0;
    padding: 0;
  }
}
