import { useState, useEffect } from 'react'
import { getProducts, addProduct, updateProduct, deleteProduct } from '../firebase/services'
import { onSnapshot, query, orderBy } from 'firebase/firestore'
import { productsCollection } from '../firebase/services'

const CACHE_KEY = 'navkala_products_cache'
const CACHE_TIMESTAMP_KEY = 'navkala_products_timestamp'
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export const useProducts = () => {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Check if cached data is still valid
  const isCacheValid = () => {
    const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY)
    if (!timestamp) return false
    
    const now = Date.now()
    const cacheTime = parseInt(timestamp)
    return (now - cacheTime) < CACHE_DURATION
  }

  // Get products from cache
  const getCachedProducts = () => {
    try {
      const cached = localStorage.getItem(CACHE_KEY)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      console.error('Error reading from cache:', error)
      return null
    }
  }

  // Save products to cache
  const setCachedProducts = (productsData) => {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(productsData))
      localStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString())
    } catch (error) {
      console.error('Error saving to cache:', error)
    }
  }

  // Clear cache
  const clearCache = () => {
    localStorage.removeItem(CACHE_KEY)
    localStorage.removeItem(CACHE_TIMESTAMP_KEY)
  }

  // Load products with smart caching
  const loadProducts = async (forceRefresh = false) => {
    try {
      setLoading(true)
      setError(null)

      // Check cache first if not forcing refresh
      if (!forceRefresh && isCacheValid()) {
        const cachedProducts = getCachedProducts()
        if (cachedProducts) {
          setProducts(cachedProducts)
          setLoading(false)
          return cachedProducts
        }
      }

      // Fetch from Firebase
      const productsData = await getProducts()
      setProducts(productsData)
      setCachedProducts(productsData)
      
      return productsData
    } catch (err) {
      setError(err.message)
      console.error('Error loading products:', err)
      
      // Try to use cached data as fallback
      const cachedProducts = getCachedProducts()
      if (cachedProducts) {
        setProducts(cachedProducts)
      }
    } finally {
      setLoading(false)
    }
  }

  // Add new product
  const addNewProduct = async (productData) => {
    try {
      const newProduct = await addProduct(productData)
      const updatedProducts = [newProduct, ...products]
      setProducts(updatedProducts)
      setCachedProducts(updatedProducts)
      return newProduct
    } catch (err) {
      setError(err.message)
      throw err
    }
  }

  // Update existing product
  const updateExistingProduct = async (id, productData) => {
    try {
      const updatedProduct = await updateProduct(id, productData)
      const updatedProducts = products.map(product => 
        product.id === id ? { ...product, ...productData } : product
      )
      setProducts(updatedProducts)
      setCachedProducts(updatedProducts)
      return updatedProduct
    } catch (err) {
      setError(err.message)
      throw err
    }
  }

  // Delete product
  const deleteExistingProduct = async (id) => {
    try {
      await deleteProduct(id)
      const updatedProducts = products.filter(product => product.id !== id)
      setProducts(updatedProducts)
      setCachedProducts(updatedProducts)
      return id
    } catch (err) {
      setError(err.message)
      throw err
    }
  }

  // Set up real-time listener for changes
  useEffect(() => {
    let unsubscribe = null

    const setupRealtimeListener = () => {
      const q = query(productsCollection, orderBy('createdAt', 'desc'))
      
      unsubscribe = onSnapshot(q, (snapshot) => {
        const productsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        
        // Only update if data has actually changed
        const currentProductsString = JSON.stringify(products)
        const newProductsString = JSON.stringify(productsData)
        
        if (currentProductsString !== newProductsString) {
          setProducts(productsData)
          setCachedProducts(productsData)
          clearCache() // Clear old cache when real-time update occurs
        }
      }, (error) => {
        console.error('Real-time listener error:', error)
        setError(error.message)
      })
    }

    // Initial load
    loadProducts().then(() => {
      // Set up real-time listener after initial load
      setupRealtimeListener()
    })

    // Cleanup
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [])

  return {
    products,
    loading,
    error,
    loadProducts,
    addProduct: addNewProduct,
    updateProduct: updateExistingProduct,
    deleteProduct: deleteExistingProduct,
    clearCache
  }
}
