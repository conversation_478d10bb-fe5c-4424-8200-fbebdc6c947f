.footer {
  background: #f8fafc;
  color: #333;
  padding: 2rem 0 1rem;
  margin-top: 4rem;
  border-top: 1px solid #e2e8f0;
}

.footer-content {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
}

@media (min-width: 640px) {
  .footer-content {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .footer-content {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .footer-content {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .footer-content {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .footer-content {
    max-width: calc(100vw - 100px);
  }
}

.footer-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1e293b;
}

.footer-section p {
  color: #333;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.footer-section p:last-child {
  margin-bottom: 0;
}

.map-container {
  margin-top: 1rem;
}

.map-wrapper {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.map-container iframe {
  width: 400px;
  height: 200px;
  border: 0;
  display: block;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.map-wrapper:hover .map-overlay {
  opacity: 1;
}

.map-overlay-content {
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(10px);
  transition: transform 0.3s ease;
}

.map-wrapper:hover .map-overlay-content {
  transform: translateY(0);
}

.map-icon {
  font-size: 1.2rem;
}

.map-text {
  color: #374151;
  font-weight: 600;
  font-size: 0.9rem;
}

.map-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.map-button {
  flex: 1;
  min-width: 140px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.map-button.primary {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
}

.map-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.map-button.secondary {
  background: #f1f5f9;
  color: #374151;
  border: 2px solid #e2e8f0;
}

.map-button.secondary:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.footer-bottom {
  border-top: 1px solid #e2e8f0;
  margin-top: 2rem;
  padding-top: 1rem;
  text-align: center;
}

.footer-bottom p {
  color: #94a3b8;
  font-size: 0.85rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .footer-section:last-child {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 1.5rem 0 1rem;
    margin-top: 3rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }

  .map-container iframe {
    height: 150px;
  }

  .map-actions {
    flex-direction: column;
  }

  .map-button {
    min-width: auto;
    font-size: 0.8rem;
    padding: 0.6rem 0.8rem;
  }
  
  .footer-section h3 {
    font-size: 1.1rem;
  }
  
  .footer-section p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 1rem 0 0.5rem;
    margin-top: 2rem;
  }
  
  .footer-content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    gap: 1rem;
  }

  .map-container iframe {
    height: 120px;
  }

  .map-text {
    font-size: 0.8rem;
  }

  .map-button {
    font-size: 0.75rem;
    padding: 0.5rem 0.6rem;
  }
  
  .footer-section h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
  
  .footer-section p {
    font-size: 0.8rem;
  }
  
  .footer-bottom {
    margin-top: 1.5rem;
    padding-top: 0.75rem;
  }
  
  .footer-bottom p {
    font-size: 0.8rem;
  }
}
