import React, { useState, useEffect } from 'react'

const ProductForm = ({ product, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image: '',
    availableTypes: '',
    availableDiameters: '',
    flipTop: '',
    applications: [],
    features: []
  })

  const [applicationInput, setApplicationInput] = useState('')
  const [featureInput, setFeatureInput] = useState('')

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        image: product.image || '',
        availableTypes: product.availableTypes || '',
        availableDiameters: product.availableDiameters || '',
        flipTop: product.flipTop || '',
        applications: product.applications || [],
        features: product.features || []
      })
    }
  }, [product])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const addApplication = () => {
    if (applicationInput.trim()) {
      setFormData(prev => ({
        ...prev,
        applications: [...prev.applications, applicationInput.trim()]
      }))
      setApplicationInput('')
    }
  }

  const removeApplication = (index) => {
    setFormData(prev => ({
      ...prev,
      applications: prev.applications.filter((_, i) => i !== index)
    }))
  }

  const addFeature = () => {
    if (featureInput.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, featureInput.trim()]
      }))
      setFeatureInput('')
    }
  }

  const removeFeature = (index) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="max-h-screen overflow-y-auto">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          {product ? 'Edit Product' : 'Add New Product'}
        </h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600"
        >
          <span className="sr-only">Close</span>
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Product Name *
            </label>
            <input
              type="text"
              name="name"
              id="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Description *
            </label>
            <textarea
              name="description"
              id="description"
              rows={3}
              required
              value={formData.description}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Image URL
            </label>
            <input
              type="url"
              name="image"
              id="image"
              value={formData.image}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>
        </div>

        {/* Specifications */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="availableTypes" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Available Types
            </label>
            <input
              type="text"
              name="availableTypes"
              id="availableTypes"
              value={formData.availableTypes}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>

          <div>
            <label htmlFor="availableDiameters" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Available Diameters
            </label>
            <input
              type="text"
              name="availableDiameters"
              id="availableDiameters"
              value={formData.availableDiameters}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>

          <div>
            <label htmlFor="flipTop" className="block text-sm font-medium text-gray-700" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Flip Top
            </label>
            <input
              type="text"
              name="flipTop"
              id="flipTop"
              value={formData.flipTop}
              onChange={handleInputChange}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
          </div>
        </div>

        {/* Applications */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
            Applications
          </label>
          <div className="flex space-x-2 mb-2">
            <input
              type="text"
              value={applicationInput}
              onChange={(e) => setApplicationInput(e.target.value)}
              placeholder="Add application"
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
            <button
              type="button"
              onClick={addApplication}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            >
              Add
            </button>
          </div>
          <div className="space-y-1">
            {formData.applications.map((app, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                <span className="text-sm" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>{app}</span>
                <button
                  type="button"
                  onClick={() => removeApplication(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Features */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
            Features
          </label>
          <div className="flex space-x-2 mb-2">
            <input
              type="text"
              value={featureInput}
              onChange={(e) => setFeatureInput(e.target.value)}
              placeholder="Add feature"
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            />
            <button
              type="button"
              onClick={addFeature}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm"
              style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
            >
              Add
            </button>
          </div>
          <div className="space-y-1">
            {formData.features.map((feature, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                <span className="text-sm" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>{feature}</span>
                <button
                  type="button"
                  onClick={() => removeFeature(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
          >
            {product ? 'Update Product' : 'Add Product'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default ProductForm
