.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.admin-login-card {
  background: white;
  padding: 3rem;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.admin-login-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.form-group input {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.form-group input:focus {
  outline: none;
  border-color: #2563eb;
}

.error-message {
  color: #dc2626;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: #fef2f2;
  border-radius: 6px;
  border: 1px solid #fecaca;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.login-button {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.875rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.login-button:active {
  transform: translateY(0);
}
