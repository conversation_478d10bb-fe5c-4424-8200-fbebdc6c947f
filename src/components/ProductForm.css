.product-form-container {
  padding: 2rem;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.product-form-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.product-form-card h2 {
  margin: 0 0 2rem 0;
  color: #1e293b;
  font-size: 1.75rem;
  font-weight: 700;
}

.product-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2563eb;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-button:hover {
  background: #4b5563;
}

.submit-button {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Image Upload Styles */
.image-upload-container {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  background: #f8fafc;
}

.upload-methods {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.method-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.method-section h4 {
  margin: 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.method-note {
  color: #6b7280;
  font-size: 0.85rem;
  line-height: 1.4;
}

.method-divider {
  text-align: center;
  color: #9ca3af;
  font-weight: 600;
  position: relative;
  margin: 0.5rem 0;
}

.method-divider::before,
.method-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background: #d1d5db;
}

.method-divider::before {
  left: 0;
}

.method-divider::after {
  right: 0;
}

.image-input {
  display: none;
}

.image-upload-button {
  display: inline-block;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 0.95rem;
}

.image-upload-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.image-url-input {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.image-url-input:focus {
  outline: none;
  border-color: #2563eb;
}

.image-preview {
  margin-top: 1.5rem;
  text-align: center;
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.image-preview h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.image-preview img {
  max-width: 250px;
  max-height: 250px;
  object-fit: contain;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  padding: 0.5rem;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-message {
  font-size: 0.875rem;
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  line-height: 1.4;
}

.upload-message.error {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.upload-message.info {
  color: #1d4ed8;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

/* Custom Fields Styles */
.custom-fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.custom-fields-header label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.add-field-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.add-field-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.no-custom-fields {
  background: #f8fafc;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.no-custom-fields p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

.custom-fields-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.custom-field-row {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.custom-field-inputs {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 0.75rem;
}

.field-label-input,
.field-value-input {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.field-label-input:focus,
.field-value-input:focus {
  outline: none;
  border-color: #2563eb;
}

.field-label-input {
  font-weight: 600;
}

.remove-field-button {
  background: #dc2626;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-field-button:hover {
  background: #b91c1c;
}

@media (max-width: 768px) {
  .product-form-container {
    padding: 1rem;
  }
  
  .product-form-card {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-button,
  .submit-button {
    width: 100%;
  }
}
