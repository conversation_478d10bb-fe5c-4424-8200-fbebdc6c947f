.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 1.8rem;
  right: 2rem;
  background: #f1f5f9;
  border: none;
  border-radius:30%;
  width: 42px;
  height: 41px;
  display: flex;
  justify-content: center;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e2e8f0;
  color: #1e293b;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
}

.modal-image {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 2rem;
  min-height: 250px;
  max-height: 300px;
}

.modal-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.modal-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  padding-right: 3rem;
}

.modal-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2563eb;
  margin: 0 0 0.75rem 0;
}

.modal-section p {
  color: #333;
  line-height: 1.6;
  margin: 0;
}

.product-description {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.specifications-table {
  display: grid;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.spec-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  align-items: start;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #2563eb;
}

.spec-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.spec-value {
  color: #1e293b;
  font-size: 0.9rem;
  line-height: 1.4;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.applications-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.application-tag {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #f0fdf4;
  border-radius: 6px;
  border-left: 3px solid #10b981;
}

.feature-bullet {
  color: #10b981;
  font-weight: bold;
  font-size: 1.2rem;
  line-height: 1;
  margin-top: 0.1rem;
}

.feature-text {
  color: #374151;
  font-size: 0.9rem;
  line-height: 1.4;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

/* Removed unused styles for simplified modal */

/* Responsive Design */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: 0.5rem;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-body {
    gap: 1rem;
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
    padding-right: 2.5rem;
  }

  .modal-image {
    min-height: 200px;
    padding: 1.5rem;
  }

  .specifications-table {
    margin-top: 0.5rem;
  }

  .spec-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .spec-label,
  .spec-value {
    font-size: 0.85rem;
  }

  .application-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .feature-text {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .modal-backdrop {
    padding: 0.25rem;
  }
  
  .modal-body {
    padding: 1rem;
    gap: 1rem;
  }
  
  .modal-title {
    font-size: 1.3rem;
  }
  
  .modal-close {
    width: 35px;
    height: 35px;
    font-size: 1.3rem;
  }
  
  .modal-image {
    min-height: 150px;
    padding: 1rem;
  }
  
  .modal-specifications {
    padding: 0.75rem;
  }
}
