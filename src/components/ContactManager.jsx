import React, { useState, useEffect } from 'react'
import { getContactSubmissions, updateContactStatus, deleteContactSubmission } from '../firebase/services'

const ContactManager = () => {
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    loadContacts()
  }, [])

  const loadContacts = async () => {
    try {
      setLoading(true)
      const contactsData = await getContactSubmissions()
      setContacts(contactsData)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (id, status) => {
    try {
      await updateContactStatus(id, status)
      setContacts(contacts.map(contact => 
        contact.id === id ? { ...contact, status } : contact
      ))
    } catch (err) {
      alert('Error updating status: ' + err.message)
    }
  }

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this contact submission?')) {
      try {
        await deleteContactSubmission(id)
        setContacts(contacts.filter(contact => contact.id !== id))
        alert('Contact submission deleted successfully!')
      } catch (err) {
        alert('Error deleting contact: ' + err.message)
      }
    }
  }

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A'
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg font-semibold text-gray-600" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Loading contacts...
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Error: {error}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Contact Submissions
        </h2>
        <button
          onClick={loadContacts}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
        >
          Refresh
        </button>
      </div>

      {/* Contacts List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {contacts.length === 0 ? (
          <div className="px-6 py-4 text-center text-gray-500" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
            No contact submissions found.
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {contacts.map((contact) => (
              <li key={contact.id} className="px-6 py-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900 truncate" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        {contact.name}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        contact.status === 'read' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`} style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        {contact.status || 'unread'}
                      </span>
                    </div>
                    
                    <div className="mt-2 space-y-1">
                      <p className="text-sm text-gray-600" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        <span className="font-medium">Email:</span> {contact.email}
                      </p>
                      <p className="text-sm text-gray-600" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        <span className="font-medium">Phone:</span> {contact.phone}
                      </p>
                      <p className="text-sm text-gray-600" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        <span className="font-medium">Message:</span> {contact.message}
                      </p>
                      <p className="text-xs text-gray-400" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        <span className="font-medium">Submitted:</span> {formatDate(contact.createdAt)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-2 ml-4">
                    <select
                      value={contact.status || 'unread'}
                      onChange={(e) => handleStatusUpdate(contact.id, e.target.value)}
                      className="text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
                    >
                      <option value="unread">Unread</option>
                      <option value="read">Read</option>
                      <option value="replied">Replied</option>
                      <option value="resolved">Resolved</option>
                    </select>
                    
                    <button
                      onClick={() => handleDelete(contact.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
                      style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  )
}

export default ContactManager
