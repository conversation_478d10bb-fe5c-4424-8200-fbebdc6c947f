import React, { useState } from 'react'
import { useProducts } from '../hooks/useProducts'
import ProductForm from './ProductForm'

const ProductManager = () => {
  const { products, loading, error, addProduct, updateProduct, deleteProduct } = useProducts()
  const [showForm, setShowForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)

  const handleAddProduct = async (productData) => {
    try {
      await addProduct(productData)
      setShowForm(false)
      alert('Product added successfully!')
    } catch (error) {
      alert('Error adding product: ' + error.message)
    }
  }

  const handleUpdateProduct = async (productData) => {
    try {
      await updateProduct(editingProduct.id, productData)
      setEditingProduct(null)
      setShowForm(false)
      alert('Product updated successfully!')
    } catch (error) {
      alert('Error updating product: ' + error.message)
    }
  }

  const handleDeleteProduct = async (id) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(id)
        alert('Product deleted successfully!')
      } catch (error) {
        alert('Error deleting product: ' + error.message)
      }
    }
  }

  const handleEdit = (product) => {
    setEditingProduct(product)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingProduct(null)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg font-semibold text-gray-600" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Loading products...
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Error: {error}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          Products Management
        </h2>
        <button
          onClick={() => setShowForm(true)}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
        >
          Add New Product
        </button>
      </div>

      {/* Product Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <ProductForm
              product={editingProduct}
              onSubmit={editingProduct ? handleUpdateProduct : handleAddProduct}
              onCancel={handleCancelForm}
            />
          </div>
        </div>
      )}

      {/* Products List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {products.length === 0 ? (
            <li className="px-6 py-4">
              <div className="text-center text-gray-500" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                No products found. Add your first product!
              </div>
            </li>
          ) : (
            products.map((product) => (
              <li key={product.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <img
                        className="h-16 w-16 object-cover rounded-md"
                        src={product.image || '/placeholder-image.jpg'}
                        alt={product.name}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        {product.name}
                      </h3>
                      <p className="text-sm text-gray-500" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        {product.description?.substring(0, 100)}...
                      </p>
                      <div className="mt-1 text-xs text-gray-400" style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
                        Types: {product.availableTypes} | Diameters: {product.availableDiameters}
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(product)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                      style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteProduct(product.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors"
                      style={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  )
}

export default ProductManager
