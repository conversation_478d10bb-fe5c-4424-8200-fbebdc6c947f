import { useEffect } from "react";
import "./ProductModal.css";

const ProductModal = ({ product, onClose }) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    document.body.style.overflow = "hidden";

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [onClose]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content">
        <button className="modal-close" onClick={onClose}>
          ×
        </button>

        <div className="modal-body">
          <h2 className="modal-title">{product.name}</h2>
          <div className="modal-image">
            <img src={product.image} alt={product.name} />
          </div>

          <div className="modal-details">
            {/* Product Description */}
            {product.description && (
              <div className="modal-section">
                <h3>Description</h3>
                <p className="product-description">{product.description}</p>
              </div>
            )}

            {/* Custom Fields */}
            {product.customFields && product.customFields.length > 0 && (
              <div className="modal-section">
                <h3>Product Details</h3>
                <div className="specifications-table">
                  {product.customFields.map((field, index) => (
                    field.label && field.value && (
                      <div key={index} className="spec-row">
                        <span className="spec-label">{field.label}</span>
                        <span className="spec-value">{field.value}</span>
                      </div>
                    )
                  ))}
                </div>
              </div>
            )}

            {/* Legacy fields for backward compatibility */}
            {(product.availableTypes || product.availableDiameters || product.flipTop) && (
              <div className="modal-section">
                <h3>Specifications</h3>
                <div className="specifications-table">
                  {product.availableTypes && (
                    <div className="spec-row">
                      <span className="spec-label">Available Types</span>
                      <span className="spec-value">{product.availableTypes}</span>
                    </div>
                  )}
                  {product.availableDiameters && (
                    <div className="spec-row">
                      <span className="spec-label">Available Diameters</span>
                      <span className="spec-value">{product.availableDiameters}</span>
                    </div>
                  )}
                  {product.flipTop && (
                    <div className="spec-row">
                      <span className="spec-label">Flip Top</span>
                      <span className="spec-value">{product.flipTop}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Applications */}
            {product.applications && product.applications.length > 0 && (
              <div className="modal-section">
                <h3>Applications</h3>
                <div className="applications-list">
                  {product.applications.map((app, index) => (
                    <span key={index} className="application-tag">{app}</span>
                  ))}
                </div>
              </div>
            )}

            {/* Features */}
            {product.features && product.features.length > 0 && (
              <div className="modal-section">
                <h3>Features</h3>
                <div className="features-list">
                  {product.features.map((feature, index) => (
                    <div key={index} className="feature-item">
                      <span className="feature-bullet">•</span>
                      <span className="feature-text">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductModal;
