import { addProduct } from '../services/productService';
import productsData from '../data/products.json';

export const migrateJsonToFirebase = async () => {
  try {
    console.log('Starting data migration...');

    for (const product of productsData) {
      // Remove the id field since Firebase will generate its own
      const { id, availableTypes, availableDiameters, flipTop, ...productData } = product;

      // Convert old fixed fields to custom fields
      const customFields = [];

      if (availableTypes) {
        customFields.push({ label: 'Available Types', value: availableTypes });
      }

      if (availableDiameters) {
        customFields.push({ label: 'Available Diameters', value: availableDiameters });
      }

      if (flipTop) {
        customFields.push({ label: 'Flip Top', value: flipTop });
      }

      // Add custom fields to product data
      const migratedProduct = {
        ...productData,
        customFields: customFields
      };

      await addProduct(migratedProduct);
      console.log(`Migrated: ${product.name}`);
    }

    console.log('Data migration completed successfully!');
    return true;
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  }
};
