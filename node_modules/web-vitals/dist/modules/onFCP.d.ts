import { FCPMetric, MetricRatingThresholds, ReportOpts } from './types.js';
/** Thresholds for FCP. See https://web.dev/articles/fcp#what_is_a_good_fcp_score */
export declare const FCPThresholds: MetricRatingThresholds;
/**
 * Calculates the [FCP](https://web.dev/articles/fcp) value for the current page and
 * calls the `callback` function once the value is ready, along with the
 * relevant `paint` performance entry used to determine the value. The reported
 * value is a `DOMHighResTimeStamp`.
 */
export declare const onFCP: (onReport: (metric: FCPMetric) => void, opts?: ReportOpts) => void;
