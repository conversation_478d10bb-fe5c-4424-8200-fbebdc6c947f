((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(wt,yt){try{!(function(){function M(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t,r,e=M(wt);(j=t=t||{})[j.DEBUG=0]="DEBUG",j[j.VERBOSE=1]="VERBOSE",j[j.INFO=2]="INFO",j[j.WARN=3]="WARN",j[j.ERROR=4]="ERROR",j[j.SILENT=5]="SILENT";let B={debug:t.DEBUG,verbose:t.VERBOSE,info:t.INFO,warn:t.WARN,error:t.ERROR,silent:t.SILENT},F=t.INFO,H={[t.DEBUG]:"log",[t.VERBOSE]:"log",[t.INFO]:"info",[t.WARN]:"warn",[t.ERROR]:"error"},x=(e,t,...a)=>{if(!(t<e.logLevel)){var r=(new Date).toISOString(),n=H[t];if(!n)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[n](`[${r}]  ${e.name}:`,...a)}};function V(){var e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function $(){try{return"object"==typeof indexedDB}catch(e){}}function q(){return new Promise((r,n)=>{try{let e=!0,t="validate-browser-context-for-indexeddb-analytics-module",a=self.indexedDB.open(t);a.onsuccess=()=>{a.result.close(),e||self.indexedDB.deleteDatabase(t),r(!0)},a.onupgradeneeded=()=>{e=!1},a.onerror=()=>{var e;n((null==(e=a.error)?void 0:e.message)||"")}}catch(e){n(e)}})}function U(){return!("undefined"==typeof navigator||!navigator.cookieEnabled)}class o extends Error{constructor(e,t,a){super(t),this.code=e,this.customData=a,this.name="FirebaseError",Object.setPrototypeOf(this,o.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,n.prototype.create)}}class n{constructor(e,t,a){this.service=e,this.serviceName=t,this.errors=a}create(e,...t){var r,a=t[0]||{},n=this.service+"/"+e,i=this.errors[e],i=i?(r=a,i.replace(W,(e,t)=>{var a=r[t];return null!=a?String(a):`<${t}?>`})):"Error",i=this.serviceName+`: ${i} (${n}).`;return new o(n,i,a)}}let W=/\{\$([^}]+)}/g,G=1e3,K=2,z=144e5,J=.5;function Y(e,t=G,a=K){var r=t*Math.pow(a,e),n=Math.round(J*r*(Math.random()-.5)*2);return Math.min(z,r+n)}function i(e){return e&&e._delegate?e._delegate:e}class a{constructor(e,t,a){this.name=e,this.instanceFactory=t,this.type=a,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let X=(t,e)=>e.some(e=>t instanceof e),Z,Q;let ee=new WeakMap,s=new WeakMap,te=new WeakMap,l=new WeakMap,c=new WeakMap;let d={get(e,t,a){if(e instanceof IDBTransaction){if("done"===t)return s.get(e);if("objectStoreNames"===t)return e.objectStoreNames||te.get(e);if("store"===t)return a.objectStoreNames[1]?void 0:a.objectStore(a.objectStoreNames[0])}return u(e[t])},set(e,t,a){return e[t]=a,!0},has(e,t){return e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e}};function ae(r){return r!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(Q=Q||[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey]).includes(r)?function(...e){return r.apply(p(this),e),u(ee.get(this))}:function(...e){return u(r.apply(p(this),e))}:function(e,...t){var a=r.call(p(this),e,...t);return te.set(a,e.sort?e.sort():[e]),u(a)}}function re(e){var i,t;return"function"==typeof e?ae(e):(e instanceof IDBTransaction&&(i=e,s.has(i)||(t=new Promise((e,t)=>{let a=()=>{i.removeEventListener("complete",r),i.removeEventListener("error",n),i.removeEventListener("abort",n)},r=()=>{e(),a()},n=()=>{t(i.error||new DOMException("AbortError","AbortError")),a()};i.addEventListener("complete",r),i.addEventListener("error",n),i.addEventListener("abort",n)}),s.set(i,t))),X(e,Z=Z||[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])?new Proxy(e,d):e)}function u(e){var i,t;return e instanceof IDBRequest?(i=e,(t=new Promise((e,t)=>{let a=()=>{i.removeEventListener("success",r),i.removeEventListener("error",n)},r=()=>{e(u(i.result)),a()},n=()=>{t(i.error),a()};i.addEventListener("success",r),i.addEventListener("error",n)})).then(e=>{e instanceof IDBCursor&&ee.set(e,i)}).catch(()=>{}),c.set(t,i),t):l.has(e)?l.get(e):((t=re(e))!==e&&(l.set(e,t),c.set(t,e)),t)}let p=e=>c.get(e);let ne=["get","getKey","getAll","getAllKeys","count"],ie=["put","add","delete","clear"],f=new Map;function oe(e,t){if(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t){if(f.get(t))return f.get(t);let n=t.replace(/FromIndex$/,""),i=t!==n,o=ie.includes(n);var a;return n in(i?IDBIndex:IDBObjectStore).prototype&&(o||ne.includes(n))?(a=async function(e,...t){var a=this.transaction(e,o?"readwrite":"readonly");let r=a.store;return i&&(r=r.index(t.shift())),(await Promise.all([r[n](...t),o&&a.done]))[0]},f.set(t,a),a):void 0}}d={...r=d,get:(e,t,a)=>oe(e,t)||r.get(e,t,a),has:(e,t)=>!!oe(e,t)||r.has(e,t)};var g="@firebase/installations",h="0.6.18";let se=1e4,le="w:"+h,ce="FIS_v2",de="https://firebaseinstallations.googleapis.com/v1",ue=36e5;let m=new n("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function pe(e){return e instanceof o&&e.code.includes("request-failed")}function fe({projectId:e}){return de+`/projects/${e}/installations`}function ge(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function he(e,t){var a=(await t.json()).error;return m.create("request-failed",{requestName:e,serverCode:a.code,serverMessage:a.message,serverStatus:a.status})}function me({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function ve(e,{refreshToken:t}){var a=me(e);return a.append("Authorization",(e=t,ce+" "+e)),a}async function we(e){var t=await e();return 500<=t.status&&t.status<600?e():t}function ye(t){return new Promise(e=>{setTimeout(e,t)})}let Ie=/^[cdef][\w-]{21}$/,v="";function be(){try{var e=new Uint8Array(17),t=((self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16,(e=>btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_"))(e).substr(0,22));return Ie.test(t)?t:v}catch(e){return v}}function w(e){return e.appName+"!"+e.appId}let Ee=new Map;function _e(e,t){var a=w(e),e=(Te(a,t),a),a=(()=>(!y&&"BroadcastChannel"in self&&((y=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{Te(e.data.key,e.data.fid)}),y))();a&&a.postMessage({key:e,fid:t}),0===Ee.size&&y&&(y.close(),y=null)}function Te(e,t){var a=Ee.get(e);if(a)for(var r of a)r(t)}let y=null;let Se="firebase-installations-database",Ce=1,I="firebase-installations-store",De=null;function b(){return De=De||((e,t,{blocked:a,upgrade:r,blocking:n,terminated:i})=>{let o=indexedDB.open(e,t);var s=u(o);return r&&o.addEventListener("upgradeneeded",e=>{r(u(o.result),e.oldVersion,e.newVersion,u(o.transaction),e)}),a&&o.addEventListener("blocked",e=>a(e.oldVersion,e.newVersion,e)),s.then(e=>{i&&e.addEventListener("close",()=>i()),n&&e.addEventListener("versionchange",e=>n(e.oldVersion,e.newVersion,e))}).catch(()=>{}),s})(Se,Ce,{upgrade:(e,t)=>{0===t&&e.createObjectStore(I)}})}async function E(e,t){var a=w(e),r=(await b()).transaction(I,"readwrite"),n=r.objectStore(I),i=await n.get(a);return await n.put(t,a),await r.done,i&&i.fid===t.fid||_e(e,t.fid),t}async function Le(e){var t=w(e),a=(await b()).transaction(I,"readwrite");await a.objectStore(I).delete(t),await a.done}async function _(e,t){var a=w(e),r=(await b()).transaction(I,"readwrite"),n=r.objectStore(I),i=await n.get(a),o=t(i);return void 0===o?await n.delete(a):await n.put(o,a),await r.done,!o||i&&i.fid===o.fid||_e(e,o.fid),o}async function T(a){let r;var e=await _(a.appConfig,e=>{var t=Pe(e||{fid:be(),registrationStatus:0}),t=((e,t)=>{var a,r;return 0===t.registrationStatus?navigator.onLine?(a={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=(async(t,a)=>{try{var e=await(async({appConfig:e,heartbeatServiceProvider:t},{fid:a})=>{let r=fe(e);var n=me(e),i=((i=t.getImmediate({optional:!0}))&&(i=await i.getHeartbeatsHeader())&&n.append("x-firebase-client",i),{fid:a,authVersion:ce,appId:e.appId,sdkVersion:le});let o={method:"POST",headers:n,body:JSON.stringify(i)};if((n=await we(()=>fetch(r,o))).ok)return{fid:(i=await n.json()).fid||a,registrationStatus:2,refreshToken:i.refreshToken,authToken:ge(i.authToken)};throw await he("Create Installation",n)})(t,a);return E(t.appConfig,e)}catch(e){throw pe(e)&&409===e.customData.serverCode?await Le(t.appConfig):await E(t.appConfig,{fid:a.fid,registrationStatus:0}),e}})(e,a),{installationEntry:a,registrationPromise:r}):(a=Promise.reject(m.create("app-offline")),{installationEntry:t,registrationPromise:a}):1===t.registrationStatus?{installationEntry:t,registrationPromise:(async e=>{let t=await Oe(e.appConfig);for(;1===t.registrationStatus;)await ye(100),t=await Oe(e.appConfig);var a,r;return 0!==t.registrationStatus?t:({installationEntry:a,registrationPromise:r}=await T(e),r||a)})(e)}:{installationEntry:t}})(a,t);return r=t.registrationPromise,t.installationEntry});return e.fid===v?{installationEntry:await r}:{installationEntry:e,registrationPromise:r}}function Oe(e){return _(e,e=>{if(e)return Pe(e);throw m.create("installation-not-found")})}function Pe(e){var t;return 1===(t=e).registrationStatus&&t.registrationTime+se<Date.now()?{fid:e.fid,registrationStatus:0}:e}async function Ne({appConfig:e,heartbeatServiceProvider:t},a){[n,i]=[e,a.fid];let r=fe(n)+`/${i}/authTokens:generate`;var n,i,o=ve(e,a),s=t.getImmediate({optional:!0}),s=(s&&(s=await s.getHeartbeatsHeader())&&o.append("x-firebase-client",s),{installation:{sdkVersion:le,appId:e.appId}});let l={method:"POST",headers:o,body:JSON.stringify(s)};o=await we(()=>fetch(r,l));if(o.ok)return ge(await o.json());throw await he("Generate Auth Token",o)}async function S(r,n=!1){let i;var e=await _(r.appConfig,e=>{if(!ke(e))throw m.create("not-registered");var t,a=e.authToken;if(n||2!==(t=a).requestStatus||(e=>{var t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+ue})(t)){if(1===a.requestStatus)return i=(async(e,t)=>{let a=await Ae(e.appConfig);for(;1===a.authToken.requestStatus;)await ye(100),a=await Ae(e.appConfig);var r=a.authToken;return 0===r.requestStatus?S(e,t):r})(r,n),e;if(navigator.onLine)return t=e,a={requestStatus:1,requestTime:Date.now()},a=Object.assign(Object.assign({},t),{authToken:a}),i=(async(t,a)=>{try{var e=await Ne(t,a),r=Object.assign(Object.assign({},a),{authToken:e});return await E(t.appConfig,r),e}catch(e){var n;throw!pe(e)||401!==e.customData.serverCode&&404!==e.customData.serverCode?(n=Object.assign(Object.assign({},a),{authToken:{requestStatus:0}}),await E(t.appConfig,n)):await Le(t.appConfig),e}})(r,a),a;throw m.create("app-offline")}return e});return i?await i:e.authToken}function Ae(e){return _(e,e=>{var t,a;if(ke(e))return t=e.authToken,1===(a=t).requestStatus&&a.requestTime+se<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e;throw m.create("not-registered")})}function ke(e){return void 0!==e&&2===e.registrationStatus}async function Re(e,t=!1){var a=e,r=(await(!(r=(await T(a)).registrationPromise)||!await r),await S(a,t));return r.token}function C(e){return m.create("missing-app-config-values",{valueName:e})}let je="installations",Me=e=>{var t=e.getProvider("app").getImmediate();return{app:t,appConfig:(e=>{if(!e||!e.options)throw C("App Configuration");if(!e.name)throw C("App Name");var t;for(t of["projectId","apiKey","appId"])if(!e.options[t])throw C(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}})(t),heartbeatServiceProvider:yt._getProvider(t,"heartbeat"),_delete:()=>Promise.resolve()}},Be=e=>{var t=e.getProvider("app").getImmediate();let a=yt._getProvider(t,je).getImmediate();return{getId:()=>(async e=>{var t=e,{installationEntry:a,registrationPromise:r}=await T(t);return(r||S(t)).catch(console.error),a.fid})(a),getToken:e=>Re(a,e)}};yt._registerComponent(new a(je,Me,"PUBLIC")),yt._registerComponent(new a("installations-internal",Be,"PRIVATE")),yt.registerVersion(g,h),yt.registerVersion(g,h,"esm2017");let Fe="analytics",He="firebase_id",xe="origin",Ve=6e4,$e="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig",D="https://www.googletagmanager.com/gtag/js",N=new class{constructor(e){this.name=e,this._logLevel=F,this._logHandler=x,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in t))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?B[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,t.DEBUG,...e),this._logHandler(this,t.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,t.VERBOSE,...e),this._logHandler(this,t.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,t.INFO,...e),this._logHandler(this,t.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,t.WARN,...e),this._logHandler(this,t.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,t.ERROR,...e),this._logHandler(this,t.ERROR,...e)}}("@firebase/analytics"),L=new n("analytics","Analytics",{"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."});function qe(e){var t;return e.startsWith(D)?e:(t=L.create("invalid-gtag-resource",{gtagURL:e}),N.warn(t.message),"")}function Ue(e){return Promise.all(e.map(e=>e.catch(e=>e)))}function We(e,t){var a=((e,t)=>{let a;return a=window.trustedTypes?window.trustedTypes.createPolicy(e,t):a})("firebase-js-sdk-policy",{createScriptURL:qe}),r=document.createElement("script"),n=D+`?l=${e}&id=`+t;r.src=a?null==a?void 0:a.createScriptURL(n):n,r.async=!0,document.head.appendChild(r)}function Ge(D,L,O,P){return async function(e,...t){try{if("event"===e){var[a,r]=t,n=D,i=L,o=O,s=a,l=r;try{let a=[];if(l&&l.send_to){let e=l.send_to;Array.isArray(e)||(e=[e]);var c=await Ue(o);for(let t of e){var d=c.find(e=>e.measurementId===t),u=d&&i[d.appId];if(!u){a=[];break}a.push(u)}}0===a.length&&(a=Object.values(i)),await Promise.all(a),n("event",s,l||{})}catch(e){N.error(e)}}else if("config"===e){var p,[f,g]=t,h=D,m=L,v=O,w=f,y=g,I=P[w];try{I?await m[I]:(p=(await Ue(v)).find(e=>e.measurementId===w))&&await m[p.appId]}catch(e){N.error(e)}h("config",w,y)}else{var b,E,_,T,S,C;"consent"===e?([b,E]=t,D("consent",b,E)):"get"===e?([_,T,S]=t,D("get",_,T,S)):"set"===e?([C]=t,D("set",C)):D(e,...t)}}catch(e){N.error(e)}}}let Ke=30;let ze=new class{constructor(e={},t=1e3){this.throttleMetadata=e,this.intervalMillis=t}getThrottleMetadata(e){return this.throttleMetadata[e]}setThrottleMetadata(e,t){this.throttleMetadata[e]=t}deleteThrottleMetadata(e){delete this.throttleMetadata[e]}};async function Je(e){var t,{appId:a,apiKey:r}=e,r={method:"GET",headers:new Headers({Accept:"application/json","x-goog-api-key":r})},a=$e.replace("{app-id}",a),a=await fetch(a,r);if(200===a.status||304===a.status)return a.json();{let e="";try{var n=await a.json();null!=(t=n.error)&&t.message&&(e=n.error.message)}catch(e){}throw L.create("config-fetch-failed",{httpStatus:a.status,responseMessage:e})}}async function Ye(e,t=ze,a){var{appId:r,apiKey:n,measurementId:i}=e.options;if(!r)throw L.create("no-app-id");if(!n){if(i)return{measurementId:i,appId:r};throw L.create("no-api-key")}var o=t.getThrottleMetadata(r)||{backoffCount:0,throttleEndTimeMillis:Date.now()};let s=new Qe;return setTimeout(async()=>{s.abort()},void 0!==a?a:Ve),async function n(i,{throttleEndTimeMillis:e,backoffCount:o},s,l=ze){var c;let{appId:d,measurementId:u}=i;try{await Xe(s,e)}catch(e){if(u)return N.warn("Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID "+u+` provided in the "measurementId" field in the local Firebase config. [${null==e?void 0:e.message}]`),{appId:d,measurementId:u};throw e}try{let e=await Je(i);return l.deleteThrottleMetadata(d),e}catch(e){let t=e;if(!Ze(t)){if(l.deleteThrottleMetadata(d),u)return N.warn("Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID "+u+` provided in the "measurementId" field in the local Firebase config. [${null==t?void 0:t.message}]`),{appId:d,measurementId:u};throw e}let a=503===Number(null==(c=null==t?void 0:t.customData)?void 0:c.httpStatus)?Y(o,l.intervalMillis,Ke):Y(o,l.intervalMillis),r={throttleEndTimeMillis:Date.now()+a,backoffCount:o+1};return l.setThrottleMetadata(d,r),N.debug(`Calling attemptFetch again in ${a} millis`),n(i,r,s,l)}}({appId:r,apiKey:n,measurementId:i},o,s,t)}function Xe(n,i){return new Promise((e,t)=>{var a=Math.max(i-Date.now(),0);let r=setTimeout(e,a);n.addEventListener(()=>{clearTimeout(r),t(L.create("fetch-throttle",{throttleEndTimeMillis:i}))})})}function Ze(e){var t;return!!(e instanceof o&&e.customData)&&(429===(t=Number(e.customData.httpStatus))||500===t||503===t||504===t)}class Qe{constructor(){this.listeners=[]}addEventListener(e){this.listeners.push(e)}abort(){this.listeners.forEach(e=>e())}}async function et(t,e,a,r,n,i,o){var s=Ye(t),l=(s.then(e=>{a[e.measurementId]=e.appId,t.options.measurementId&&e.measurementId!==t.options.measurementId&&N.warn(`The measurement ID in the local Firebase config (${t.options.measurementId})`+` does not match the measurement ID fetched from the server (${e.measurementId}).`+" To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.")}).catch(e=>N.error(e)),e.push(s),(async()=>{if(!$())return N.warn(L.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;try{await q()}catch(e){return N.warn(L.create("indexeddb-unavailable",{errorInfo:null==e?void 0:e.toString()}).message),!1}return!0})().then(e=>{if(e)return r.getId()})),[s,l]=await Promise.all([s,l]),c=((e=>{var t,a=window.document.getElementsByTagName("script");for(t of Object.values(a))if(t.src&&t.src.includes(D)&&t.src.includes(e))return t})(i)||We(i,s.measurementId),n("js",new Date),null!=(c=null==o?void 0:o.config)?c:{});return c[xe]="firebase",c.update=!0,null!=l&&(c[He]=l),n("config",s.measurementId,c),s.measurementId}class tt{constructor(e){this.app=e}_delete(){return delete O[this.app.options.appId],Promise.resolve()}}let O={},at=[],rt={},P="dataLayer",nt="gtag",it,A,k=!1;function ot(e){if(k)throw L.create("already-initialized");e.dataLayerName&&(P=e.dataLayerName),e.gtagName&&(nt=e.gtagName)}function st(e,t,a){r=[],V()&&r.push("This is a browser extension environment."),U()||r.push("Cookies are not available."),0<r.length&&(r=r.map((e,t)=>`(${t+1}) `+e).join(" "),r=L.create("invalid-analytics-context",{errorInfo:r}),N.warn(r.message));var r=e.options.appId;if(!r)throw L.create("no-app-id");if(!e.options.apiKey){if(!e.options.measurementId)throw L.create("no-api-key");N.warn('The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID '+e.options.measurementId+' provided in the "measurementId" field in the local Firebase config.')}if(null!=O[r])throw L.create("already-exists",{id:r});if(!k){{var n=P;let e=[];Array.isArray(window[n])?e=window[n]:window[n]=e,e}var{wrappedGtag:i,gtagCore:o}=((e,t,a,r,n)=>{let i=function(){window[r].push(arguments)};return window[n]&&"function"==typeof window[n]&&(i=window[n]),window[n]=Ge(i,e,t,a),{gtagCore:i,wrappedGtag:window[n]}})(O,at,rt,P,nt);A=i,it=o,k=!0}return O[r]=et(e,at,rt,t,it,P,a),new tt(e)}async function lt(){if(V())return!1;if(!U())return!1;if(!$())return!1;try{return await q()}catch(e){return!1}}function ct(e,t,a){e=i(e),(async(e,t,a,r)=>{if(r&&r.global)return e("set",{screen_name:a}),Promise.resolve();e("config",await t,{update:!0,screen_name:a})})(A,O[e.app.options.appId],t,a).catch(e=>N.error(e))}function dt(e,t,a){e=i(e),(async(e,t,a,r)=>{if(r&&r.global)return e("set",{user_id:a}),Promise.resolve();e("config",await t,{update:!0,user_id:a})})(A,O[e.app.options.appId],t,a).catch(e=>N.error(e))}function ut(e,t,a){e=i(e),(async(e,t,a,r)=>{if(r&&r.global){var n,i={};for(n of Object.keys(a))i["user_properties."+n]=a[n];return e("set",i),Promise.resolve()}e("config",await t,{update:!0,user_properties:a})})(A,O[e.app.options.appId],t,a).catch(e=>N.error(e))}function pt(e,t){e=i(e),(async(e,t)=>{var a=await e;window["ga-disable-"+a]=!t})(O[e.app.options.appId],t).catch(e=>N.error(e))}function ft(e,t,a,r){e=i(e),(async(e,t,a,r,n)=>{var i;n&&n.global?e("event",a,r):(i=await t,e("event",a,Object.assign(Object.assign({},r),{send_to:i})))})(A,O[e.app.options.appId],t,a,r).catch(e=>N.error(e))}let gt="@firebase/analytics",ht="0.10.17";yt._registerComponent(new a(Fe,(e,{options:t})=>st(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),t),"PUBLIC")),yt._registerComponent(new a("analytics-internal",function(e){try{let r=e.getProvider(Fe).getImmediate();return{logEvent:(e,t,a)=>ft(r,e,t,a)}}catch(e){throw L.create("interop-component-reg-failed",{reason:e})}},"PRIVATE")),yt.registerVersion(gt,ht),yt.registerVersion(gt,ht,"esm2017");var R,j;class mt{constructor(e,t){this.app=e,this._delegate=t}logEvent(e,t,a){ft(this._delegate,e,t,a)}setCurrentScreen(e,t){ct(this._delegate,e,t)}setUserId(e,t){dt(this._delegate,e,t)}setUserProperties(e,t){ut(this._delegate,e,t)}setAnalyticsCollectionEnabled(e){pt(this._delegate,e)}}(j=R=R||{}).ADD_SHIPPING_INFO="add_shipping_info",j.ADD_PAYMENT_INFO="add_payment_info",j.ADD_TO_CART="add_to_cart",j.ADD_TO_WISHLIST="add_to_wishlist",j.BEGIN_CHECKOUT="begin_checkout",j.CHECKOUT_PROGRESS="checkout_progress",j.EXCEPTION="exception",j.GENERATE_LEAD="generate_lead",j.LOGIN="login",j.PAGE_VIEW="page_view",j.PURCHASE="purchase",j.REFUND="refund",j.REMOVE_FROM_CART="remove_from_cart",j.SCREEN_VIEW="screen_view",j.SEARCH="search",j.SELECT_CONTENT="select_content",j.SELECT_ITEM="select_item",j.SELECT_PROMOTION="select_promotion",j.SET_CHECKOUT_OPTION="set_checkout_option",j.SHARE="share",j.SIGN_UP="sign_up",j.TIMING_COMPLETE="timing_complete",j.VIEW_CART="view_cart",j.VIEW_ITEM="view_item",j.VIEW_ITEM_LIST="view_item_list",j.VIEW_PROMOTION="view_promotion",j.VIEW_SEARCH_RESULTS="view_search_results";let vt=e=>{var t=e.getProvider("app-compat").getImmediate(),a=e.getProvider("analytics").getImmediate();return new mt(t,a)};g={Analytics:mt,settings:ot,isSupported:lt,EventName:R},e.default.INTERNAL.registerComponent(new a("analytics-compat",vt,"PUBLIC").setServiceProps(g).setMultipleInstances(!0)),e.default.registerVersion("@firebase/analytics-compat","0.2.23")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-analytics-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-analytics-compat.js.map
