{"version": 3, "file": "firebase-performance.js", "sources": ["../util/src/errors.ts", "../util/src/obj.ts", "../util/src/compat.ts", "../logger/src/logger.ts", "../../node_modules/web-vitals/dist/web-vitals.attribution.js", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../../node_modules/idb/build/index.js", "../installations/src/util/constants.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../performance/src/constants.ts", "../performance/src/utils/errors.ts", "../performance/src/utils/console_logger.ts", "../performance/src/services/api_service.ts", "../performance/src/services/iid_service.ts", "../performance/src/services/settings_service.ts", "../util/src/environment.ts", "../performance/src/utils/string_merger.ts", "../performance/src/utils/attributes_utils.ts", "../performance/src/utils/app_utils.ts", "../performance/src/services/remote_config_service.ts", "../performance/src/services/initialization_service.ts", "../performance/src/services/transport_service.ts", "../performance/src/services/perf_logger.ts", "../performance/src/resources/network_request.ts", "../performance/src/utils/metric_utils.ts", "../performance/src/resources/trace.ts", "../performance/src/services/oob_resources_service.ts", "../performance/src/controllers/perf.ts", "../performance/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},r=function(t){if(\"loading\"===document.readyState)return\"loading\";var e=n();if(e){if(t<e.domInteractive)return\"loading\";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return\"dom-interactive\";if(0===e.domComplete||t<e.domComplete)return\"dom-content-loaded\"}return\"complete\"},i=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,\"\")},a=function(t,e){var n=\"\";try{for(;t&&9!==t.nodeType;){var r=t,a=r.id?\"#\"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?\".\"+r.classList.value.trim().replace(/\\s+/g,\".\"):\"\");if(n.length+a.length>(e||100)-1)return n||a;if(n=n?a+\">\"+n:a,r.id)break;t=r.parentNode}}catch(t){}return n},o=-1,c=function(){return o},u=function(t){addEventListener(\"pageshow\",(function(e){e.persisted&&(o=e.timeStamp,t(e))}),!0)},s=function(){var t=n();return t&&t.activationStart||0},f=function(t,e){var r=n(),i=\"navigate\";c()>=0?i=\"back-forward-cache\":r&&(document.prerendering||s()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":r.type&&(i=r.type.replace(/_/g,\"-\")));return{name:t,value:void 0===e?-1:e,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},d=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},l=function(t,e,n,r){var i,a;return function(o){e.value>=0&&(o||r)&&((a=e.value-(i||0))||void 0===i)&&(i=e.value,e.delta=a,e.rating=function(t,e){return t>e[1]?\"poor\":t>e[0]?\"needs-improvement\":\"good\"}(e.value,n),t(e))}},m=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&t()}))},v=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},T=function(t){\"hidden\"===document.visibilityState&&g>-1&&(g=\"visibilitychange\"===t.type?t.timeStamp:0,E())},y=function(){addEventListener(\"visibilitychange\",T,!0),addEventListener(\"prerenderingchange\",T,!0)},E=function(){removeEventListener(\"visibilitychange\",T,!0),removeEventListener(\"prerenderingchange\",T,!0)},S=function(){return g<0&&(g=h(),y(),u((function(){setTimeout((function(){g=h(),y()}),0)}))),{get firstHiddenTime(){return g}}},b=function(t){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return t()}),!0):t()},L=[1800,3e3],C=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FCP\"),a=d(\"paint\",(function(t){t.forEach((function(t){\"first-contentful-paint\"===t.name&&(a.disconnect(),t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries.push(t),n(!0)))}))}));a&&(n=l(t,i,L,e.reportAllChanges),u((function(r){i=f(\"FCP\"),n=l(t,i,L,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},M=[.1,.25],D=function(t,e){!function(t,e){e=e||{},C(v((function(){var n,r=f(\"CLS\",0),i=0,a=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=a[0],n=a[a.length-1];i&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,a.push(t)):(i=t.value,a=[t])}})),i>r.value&&(r.value=i,r.entries=a,n())},c=d(\"layout-shift\",o);c&&(n=l(t,r,M,e.reportAllChanges),p((function(){o(c.takeRecords()),n(!0)})),u((function(){i=0,r=f(\"CLS\",0),n=l(t,r,M,e.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var i=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(i&&i.sources&&i.sources.length){var o=(e=i.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];o&&(n={largestShiftTarget:a(o.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:o,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)},w=function(t,e){C((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:r(c())};if(t.entries.length){var i=n(),a=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,u=Math.max(0,i.responseStart-o);e={timeToFirstByte:u,firstByteToFCP:t.value-u,loadState:r(t.entries[0].startTime),navigationEntry:i,fcpEntry:a}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},x=0,I=1/0,k=0,A=function(t){t.forEach((function(t){t.interactionId&&(I=Math.min(I,t.interactionId),k=Math.max(k,t.interactionId),x=k?(k-I)/7+1:0)}))},F=function(){return t?x:performance.interactionCount||0},P=function(){\"interactionCount\"in performance||t||(t=d(\"event\",A,{type:\"event\",buffered:!0,durationThreshold:0}))},B=[],O=new Map,R=0,j=function(){var t=Math.min(B.length-1,Math.floor((F()-R)/50));return B[t]},q=[],H=function(t){if(q.forEach((function(e){return e(t)})),t.interactionId||\"first-input\"===t.entryType){var e=B[B.length-1],n=O.get(t.interactionId);if(n||B.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};O.set(r.id,r),B.push(r)}B.sort((function(t,e){return e.latency-t.latency})),B.length>10&&B.splice(10).forEach((function(t){return O.delete(t.id)}))}}},N=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=v(t),\"hidden\"===document.visibilityState?t():(n=e(t),p(t)),n},W=[200,500],z=function(t,e){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(e=e||{},b((function(){var n;P();var r,i=f(\"INP\"),a=function(t){N((function(){t.forEach(H);var e=j();e&&e.latency!==i.value&&(i.value=e.latency,i.entries=e.entries,r())}))},o=d(\"event\",a,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});r=l(t,i,W,e.reportAllChanges),o&&(o.observe({type:\"first-input\",buffered:!0}),p((function(){a(o.takeRecords()),r(!0)})),u((function(){R=F(),B.length=0,O.clear(),i=f(\"INP\"),r=l(t,i,W,e.reportAllChanges)})))})))},U=[],V=[],_=0,G=new WeakMap,J=new Map,K=-1,Q=function(t){U=U.concat(t),X()},X=function(){K<0&&(K=N(Y))},Y=function(){J.size>10&&J.forEach((function(t,e){O.has(e)||J.delete(e)}));var t=B.map((function(t){return G.get(t.entries[0])})),e=V.length-50;V=V.filter((function(n,r){return r>=e||t.includes(n)}));for(var n=new Set,r=0;r<V.length;r++){var i=V[r];nt(i.startTime,i.processingEnd).forEach((function(t){n.add(t)}))}var a=U.length-1-50;U=U.filter((function(t,e){return t.startTime>_&&e>a||n.has(t)})),K=-1};q.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;_=Math.max(_,t.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(n-i.renderTime)<=8){(e=i).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},V.push(e)),(t.interactionId||\"first-input\"===t.entryType)&&G.set(t,e),X()}));var Z,$,tt,et,nt=function(t,e){for(var n,r=[],i=0;n=U[i];i++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;r.push(n)}return r},rt=function(t,n){e||(e=d(\"long-animation-frame\",Q)),z((function(e){var n=function(t){var e=t.entries[0],n=G.get(e),i=e.processingStart,o=n.processingEnd,c=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=nt(e.startTime,o),s=t.entries.find((function(t){return t.target})),f=s&&s.target||J.get(e.interactionId),d=[e.startTime+e.duration,o].concat(u.map((function(t){return t.startTime+t.duration}))),l=Math.max.apply(Math,d),m={interactionTarget:a(f),interactionTargetElement:f,interactionType:e.name.startsWith(\"key\")?\"keyboard\":\"pointer\",interactionTime:e.startTime,nextPaintTime:l,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:i-e.startTime,processingDuration:o-i,presentationDelay:Math.max(l-o,0),loadState:r(e.startTime)};return Object.assign(t,{attribution:m})}(e);t(n)}),n)},it=[2500,4e3],at={},ot=function(t,e){!function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"LCP\"),a=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())}))},o=d(\"largest-contentful-paint\",a);if(o){n=l(t,i,it,e.reportAllChanges);var c=v((function(){at[i.id]||(a(o.takeRecords()),o.disconnect(),at[i.id]=!0,n(!0))}));[\"keydown\",\"click\"].forEach((function(t){addEventListener(t,(function(){return N(c)}),{once:!0,capture:!0})})),p(c),u((function(r){i=f(\"LCP\"),n=l(t,i,it,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,at[i.id]=!0,n(!0)}))}))}}))}((function(e){var r=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var r=n();if(r){var i=r.activationStart||0,o=t.entries[t.entries.length-1],c=o.url&&performance.getEntriesByType(\"resource\").filter((function(t){return t.name===o.url}))[0],u=Math.max(0,r.responseStart-i),s=Math.max(u,c?(c.requestStart||c.startTime)-i:0),f=Math.max(s,c?c.responseEnd-i:0),d=Math.max(f,o.startTime-i);e={element:a(o.element),timeToFirstByte:u,resourceLoadDelay:s-u,resourceLoadDuration:f-s,elementRenderDelay:d-f,navigationEntry:r,lcpEntry:o},o.url&&(e.url=o.url),c&&(e.lcpResourceEntry=c)}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)},ct=[800,1800],ut=function t(e){document.prerendering?b((function(){return t(e)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return t(e)}),!0):setTimeout(e,0)},st=function(t,e){e=e||{};var r=f(\"TTFB\"),i=l(t,r,ct,e.reportAllChanges);ut((function(){var a=n();a&&(r.value=Math.max(a.responseStart-s(),0),r.entries=[a],i(!0),u((function(){r=f(\"TTFB\",0),(i=l(t,r,ct,e.reportAllChanges))(!0)})))}))},ft=function(t,e){st((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),a=Math.max(n.domainLookupStart-r,0),o=Math.max(n.connectStart-r,0),c=Math.max(n.connectEnd-r,0);e={waitingDuration:i,cacheDuration:a-i,dnsDuration:o-a,connectionDuration:c-o,requestDuration:t.value-c,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)},dt={passive:!0,capture:!0},lt=new Date,mt=function(t,e){Z||(Z=e,$=t,tt=new Date,gt(removeEventListener),pt())},pt=function(){if($>=0&&$<tt-lt){var t={entryType:\"first-input\",name:Z.type,target:Z.target,cancelable:Z.cancelable,startTime:Z.timeStamp,processingStart:Z.timeStamp+$};et.forEach((function(e){e(t)})),et=[]}},vt=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;\"pointerdown\"==t.type?function(t,e){var n=function(){mt(t,e),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",n,dt),removeEventListener(\"pointercancel\",r,dt)};addEventListener(\"pointerup\",n,dt),addEventListener(\"pointercancel\",r,dt)}(e,t):mt(e,t)}},gt=function(t){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(e){return t(e,vt,dt)}))},ht=[100,300],Tt=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FID\"),a=function(t){t.startTime<r.firstHiddenTime&&(i.value=t.processingStart-t.startTime,i.entries.push(t),n(!0))},o=function(t){t.forEach(a)},c=d(\"first-input\",o);n=l(t,i,ht,e.reportAllChanges),c&&(p(v((function(){o(c.takeRecords()),c.disconnect()}))),u((function(){var r;i=f(\"FID\"),n=l(t,i,ht,e.reportAllChanges),et=[],$=-1,Z=null,gt(addEventListener),r=a,et.push(r),pt()})))}))},yt=function(t,e){Tt((function(e){var n=function(t){var e=t.entries[0],n={eventTarget:a(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:r(e.startTime)};return Object.assign(t,{attribution:n})}(e);t(n)}),e)};export{M as CLSThresholds,L as FCPThresholds,ht as FIDThresholds,W as INPThresholds,it as LCPThresholds,ct as TTFBThresholds,D as onCLS,w as onFCP,yt as onFID,rt as onINP,ot as onLCP,ft as onTTFB};\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const SDK_VERSION = version;\n/** The prefix for start User Timing marks used for creating Traces. */\nexport const TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\n/** The prefix for stop User Timing marks used for creating Traces. */\nexport const TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\n/** The prefix for User Timing measure used for creating Traces. */\nexport const TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\n/** The prefix for out of the box page load Trace name. */\nexport const OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\n\nexport const FIRST_PAINT_COUNTER_NAME = '_fp';\n\nexport const FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\n\nexport const FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\n\nexport const LARGEST_CONTENTFUL_PAINT_METRIC_NAME = '_lcp';\nexport const LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME = 'lcp_element';\n\nexport const INTERACTION_TO_NEXT_PAINT_METRIC_NAME = '_inp';\nexport const INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME = 'inp_interactionTarget';\n\nexport const CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME = '_cls';\nexport const CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME = 'cls_largestShiftTarget';\n\nexport const CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\n\nexport const CONFIG_EXPIRY_LOCAL_STORAGE_KEY =\n  '@firebase/performance/configexpire';\n\nexport const SERVICE = 'performance';\nexport const SERVICE_NAME = 'Performance';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from '../constants';\n\nexport const enum ErrorCode {\n  TRACE_STARTED_BEFORE = 'trace started',\n  TRACE_STOPPED_BEFORE = 'trace stopped',\n  NONPOSITIVE_TRACE_START_TIME = 'nonpositive trace startTime',\n  NONPOSITIVE_TRACE_DURATION = 'nonpositive trace duration',\n  NO_WINDOW = 'no window',\n  NO_APP_ID = 'no app id',\n  NO_PROJECT_ID = 'no project id',\n  NO_API_KEY = 'no api key',\n  INVALID_CC_LOG = 'invalid cc log',\n  FB_NOT_DEFAULT = 'FB not default',\n  RC_NOT_OK = 'RC response not ok',\n  INVALID_ATTRIBUTE_NAME = 'invalid attribute name',\n  INVALID_ATTRIBUTE_VALUE = 'invalid attribute value',\n  INVALID_CUSTOM_METRIC_NAME = 'invalid custom metric name',\n  INVALID_STRING_MERGER_PARAMETER = 'invalid String merger input',\n  ALREADY_INITIALIZED = 'already initialized'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.TRACE_STARTED_BEFORE]: 'Trace {$traceName} was started before.',\n  [ErrorCode.TRACE_STOPPED_BEFORE]: 'Trace {$traceName} is not running.',\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]:\n    'Trace {$traceName} startTime should be positive.',\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]:\n    'Trace {$traceName} duration should be positive.',\n  [ErrorCode.NO_WINDOW]: 'Window is not available.',\n  [ErrorCode.NO_APP_ID]: 'App id is not available.',\n  [ErrorCode.NO_PROJECT_ID]: 'Project id is not available.',\n  [ErrorCode.NO_API_KEY]: 'Api key is not available.',\n  [ErrorCode.INVALID_CC_LOG]: 'Attempted to queue invalid cc event',\n  [ErrorCode.FB_NOT_DEFAULT]:\n    'Performance can only start when Firebase app instance is the default one.',\n  [ErrorCode.RC_NOT_OK]: 'RC response is not ok',\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]:\n    'Attribute name {$attributeName} is invalid.',\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]:\n    'Attribute value {$attributeValue} is invalid.',\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]:\n    'Custom metric name {$customMetricName} is invalid',\n  [ErrorCode.INVALID_STRING_MERGER_PARAMETER]:\n    'Input for String merger is invalid, contact support team to resolve.',\n  [ErrorCode.ALREADY_INITIALIZED]:\n    'initializePerformance() has already been called with ' +\n    'different options. To avoid this error, call initializePerformance() with the ' +\n    'same options as when it was originally called, or call getPerformance() to return the' +\n    ' already initialized instance.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.TRACE_STARTED_BEFORE]: { traceName: string };\n  [ErrorCode.TRACE_STOPPED_BEFORE]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]: { traceName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]: { attributeName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]: { attributeValue: string };\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]: { customMetricName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { SERVICE_NAME } from '../constants';\n\nexport const consoleLogger = new Logger(SERVICE_NAME);\nconsoleLogger.logLevel = LogLevel.INFO;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { isIndexedDBAvailable, areCookiesEnabled } from '@firebase/util';\nimport { consoleLogger } from '../utils/console_logger';\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution,\n  onCLS as vitalsOnCLS,\n  onINP as vitalsOnINP,\n  onLCP as vitalsOnLCP\n} from 'web-vitals/attribution';\n\ndeclare global {\n  interface Window {\n    PerformanceObserver: typeof PerformanceObserver;\n    perfMetrics?: { onFirstInputDelay(fn: (fid: number) => void): void };\n  }\n}\n\nlet apiInstance: Api | undefined;\nlet windowInstance: Window | undefined;\n\nexport type EntryType =\n  | 'mark'\n  | 'measure'\n  | 'paint'\n  | 'resource'\n  | 'frame'\n  | 'navigation';\n\n/**\n * This class holds a reference to various browser related objects injected by\n * set methods.\n */\nexport class Api {\n  private readonly performance: Performance;\n  /** PerformanceObserver constructor function. */\n  private readonly PerformanceObserver: typeof PerformanceObserver;\n  private readonly windowLocation: Location;\n  readonly onFirstInputDelay?: (fn: (fid: number) => void) => void;\n  readonly onLCP: (fn: (metric: LCPMetricWithAttribution) => void) => void;\n  readonly onINP: (fn: (metric: INPMetricWithAttribution) => void) => void;\n  readonly onCLS: (fn: (metric: CLSMetricWithAttribution) => void) => void;\n  readonly localStorage?: Storage;\n  readonly document: Document;\n  readonly navigator: Navigator;\n\n  constructor(readonly window?: Window) {\n    if (!window) {\n      throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n    }\n    this.performance = window.performance;\n    this.PerformanceObserver = window.PerformanceObserver;\n    this.windowLocation = window.location;\n    this.navigator = window.navigator;\n    this.document = window.document;\n    if (this.navigator && this.navigator.cookieEnabled) {\n      // If user blocks cookies on the browser, accessing localStorage will\n      // throw an exception.\n      this.localStorage = window.localStorage;\n    }\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\n    }\n    this.onLCP = vitalsOnLCP;\n    this.onINP = vitalsOnINP;\n    this.onCLS = vitalsOnCLS;\n  }\n\n  getUrl(): string {\n    // Do not capture the string query part of url.\n    return this.windowLocation.href.split('?')[0];\n  }\n\n  mark(name: string): void {\n    if (!this.performance || !this.performance.mark) {\n      return;\n    }\n    this.performance.mark(name);\n  }\n\n  measure(measureName: string, mark1: string, mark2: string): void {\n    if (!this.performance || !this.performance.measure) {\n      return;\n    }\n    this.performance.measure(measureName, mark1, mark2);\n  }\n\n  getEntriesByType(type: EntryType): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByType) {\n      return [];\n    }\n    return this.performance.getEntriesByType(type);\n  }\n\n  getEntriesByName(name: string): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByName) {\n      return [];\n    }\n    return this.performance.getEntriesByName(name);\n  }\n\n  getTimeOrigin(): number {\n    // Polyfill the time origin with performance.timing.navigationStart.\n    return (\n      this.performance &&\n      (this.performance.timeOrigin || this.performance.timing.navigationStart)\n    );\n  }\n\n  requiredApisAvailable(): boolean {\n    if (!fetch || !Promise || !areCookiesEnabled()) {\n      consoleLogger.info(\n        'Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.'\n      );\n      return false;\n    }\n\n    if (!isIndexedDBAvailable()) {\n      consoleLogger.info('IndexedDB is not supported by current browser');\n      return false;\n    }\n    return true;\n  }\n\n  setupObserver(\n    entryType: EntryType,\n    callback: (entry: PerformanceEntry) => void\n  ): void {\n    if (!this.PerformanceObserver) {\n      return;\n    }\n    const observer = new this.PerformanceObserver(list => {\n      for (const entry of list.getEntries()) {\n        // `entry` is a PerformanceEntry instance.\n        callback(entry);\n      }\n    });\n\n    // Start observing the entry types you care about.\n    observer.observe({ entryTypes: [entryType] });\n  }\n\n  static getInstance(): Api {\n    if (apiInstance === undefined) {\n      apiInstance = new Api(windowInstance);\n    }\n    return apiInstance;\n  }\n}\n\nexport function setupApi(window: Window): void {\n  windowInstance = window;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\nlet iid: string | undefined;\nlet authToken: string | undefined;\n\nexport function getIidPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const iidPromise = installationsService.getId();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  iidPromise.then((iidVal: string) => {\n    iid = iidVal;\n  });\n  return iidPromise;\n}\n\n// This method should be used after the iid is retrieved by getIidPromise method.\nexport function getIid(): string | undefined {\n  return iid;\n}\n\nexport function getAuthTokenPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const authTokenPromise = installationsService.getToken();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  authTokenPromise.then((authTokenVal: string) => {\n    authToken = authTokenVal;\n  });\n  return authTokenPromise;\n}\n\nexport function getAuthenticationToken(): string | undefined {\n  return authToken;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { mergeStrings } from '../utils/string_merger';\n\nlet settingsServiceInstance: SettingsService | undefined;\n\nexport class SettingsService {\n  // The variable which controls logging of automatic traces and HTTP/S network monitoring.\n  instrumentationEnabled = true;\n\n  // The variable which controls logging of custom traces.\n  dataCollectionEnabled = true;\n\n  // Configuration flags set through remote config.\n  loggingEnabled = false;\n  // Sampling rate between 0 and 1.\n  tracesSamplingRate = 1;\n  networkRequestsSamplingRate = 1;\n\n  // Address of logging service.\n  logEndPointUrl =\n    'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\n  // Performance event transport endpoint URL which should be compatible with proto3.\n  // New Address for transport service, not configurable via Remote Config.\n  flTransportEndpointUrl = mergeStrings(\n    'hts/frbslgigp.ogepscmv/ieo/eaylg',\n    'tp:/ieaeogn-agolai.o/1frlglgc/o'\n  );\n\n  transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\n\n  // Source type for performance event logs.\n  logSource = 462;\n\n  // Flags which control per session logging of traces and network requests.\n  logTraceAfterSampling = false;\n  logNetworkAfterSampling = false;\n\n  // TTL of config retrieved from remote config in hours.\n  configTimeToLive = 12;\n\n  getFlTransportFullUrl(): string {\n    return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\n  }\n\n  static getInstance(): SettingsService {\n    if (settingsServiceInstance === undefined) {\n      settingsServiceInstance = new SettingsService();\n    }\n    return settingsServiceInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\n\nexport function mergeStrings(part1: string, part2: string): string {\n  const sizeDiff = part1.length - part2.length;\n  if (sizeDiff < 0 || sizeDiff > 1) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_STRING_MERGER_PARAMETER);\n  }\n\n  const resultArray = [];\n  for (let i = 0; i < part1.length; i++) {\n    resultArray.push(part1.charAt(i));\n    if (part2.length > i) {\n      resultArray.push(part2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\n\n// The values and orders of the following enums should not be changed.\nconst enum ServiceWorkerStatus {\n  UNKNOWN = 0,\n  UNSUPPORTED = 1,\n  CONTROLLED = 2,\n  UNCONTROLLED = 3\n}\n\nexport enum VisibilityState {\n  UNKNOWN = 0,\n  VISIBLE = 1,\n  HIDDEN = 2\n}\n\nconst enum EffectiveConnectionType {\n  UNKNOWN = 0,\n  CONNECTION_SLOW_2G = 1,\n  CONNECTION_2G = 2,\n  CONNECTION_3G = 3,\n  CONNECTION_4G = 4\n}\n\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi';\n\n/**\n * NetworkInformation\n * This API is not well supported in all major browsers, so TypeScript does not provide types for it.\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation\n */\ninterface NetworkInformation extends EventTarget {\n  readonly type: ConnectionType;\n}\n\ninterface NetworkInformationWithEffectiveType extends NetworkInformation {\n  readonly effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n}\n\ninterface NavigatorWithConnection extends Navigator {\n  readonly connection: NetworkInformationWithEffectiveType;\n}\n\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\n\nexport function getServiceWorkerStatus(): ServiceWorkerStatus {\n  const navigator = Api.getInstance().navigator;\n  if (navigator?.serviceWorker) {\n    if (navigator.serviceWorker.controller) {\n      return ServiceWorkerStatus.CONTROLLED;\n    } else {\n      return ServiceWorkerStatus.UNCONTROLLED;\n    }\n  } else {\n    return ServiceWorkerStatus.UNSUPPORTED;\n  }\n}\n\nexport function getVisibilityState(): VisibilityState {\n  const document = Api.getInstance().document;\n  const visibilityState = document.visibilityState;\n  switch (visibilityState) {\n    case 'visible':\n      return VisibilityState.VISIBLE;\n    case 'hidden':\n      return VisibilityState.HIDDEN;\n    default:\n      return VisibilityState.UNKNOWN;\n  }\n}\n\nexport function getEffectiveConnectionType(): EffectiveConnectionType {\n  const navigator = Api.getInstance().navigator;\n  const navigatorConnection = (navigator as NavigatorWithConnection).connection;\n  const effectiveType =\n    navigatorConnection && navigatorConnection.effectiveType;\n  switch (effectiveType) {\n    case 'slow-2g':\n      return EffectiveConnectionType.CONNECTION_SLOW_2G;\n    case '2g':\n      return EffectiveConnectionType.CONNECTION_2G;\n    case '3g':\n      return EffectiveConnectionType.CONNECTION_3G;\n    case '4g':\n      return EffectiveConnectionType.CONNECTION_4G;\n    default:\n      return EffectiveConnectionType.UNKNOWN;\n  }\n}\n\nexport function isValidCustomAttributeName(name: string): boolean {\n  if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\n    return false;\n  }\n  const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix =>\n    name.startsWith(prefix)\n  );\n  return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\n}\n\nexport function isValidCustomAttributeValue(value: string): boolean {\n  return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getAppId(firebaseApp: FirebaseApp): string {\n  const appId = firebaseApp.options?.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_APP_ID);\n  }\n  return appId;\n}\n\nexport function getProjectId(firebaseApp: FirebaseApp): string {\n  const projectId = firebaseApp.options?.projectId;\n  if (!projectId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_PROJECT_ID);\n  }\n  return projectId;\n}\n\nexport function getApiKey(firebaseApp: FirebaseApp): string {\n  const apiKey = firebaseApp.options?.apiKey;\n  if (!apiKey) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_API_KEY);\n  }\n  return apiKey;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n  CONFIG_LOCAL_STORAGE_KEY,\n  SDK_VERSION\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\n\nimport { Api } from './api_service';\nimport { getAuthTokenPromise } from './iid_service';\nimport { SettingsService } from './settings_service';\nimport { Performance<PERSON>ontroller } from '../controllers/perf';\nimport { getProjectId, getApiKey, getAppId } from '../utils/app_utils';\n\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\n\ninterface SecondaryConfig {\n  loggingEnabled?: boolean;\n  logSource?: number;\n  logEndPointUrl?: string;\n  transportKey?: string;\n  tracesSamplingRate?: number;\n  networkRequestsSamplingRate?: number;\n}\n\n// These values will be used if the remote config object is successfully\n// retrieved, but the template does not have these fields.\nconst DEFAULT_CONFIGS: SecondaryConfig = {\n  loggingEnabled: true\n};\n\n/* eslint-disable camelcase */\ninterface RemoteConfigTemplate {\n  fpr_enabled?: string;\n  fpr_log_source?: string;\n  fpr_log_endpoint_url?: string;\n  fpr_log_transport_key?: string;\n  fpr_log_transport_web_percent?: string;\n  fpr_vc_network_request_sampling_rate?: string;\n  fpr_vc_trace_sampling_rate?: string;\n  fpr_vc_session_sampling_rate?: string;\n}\n/* eslint-enable camelcase */\n\ninterface RemoteConfigResponse {\n  entries?: RemoteConfigTemplate;\n  state?: string;\n}\n\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\n\nexport function getConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<void> {\n  const config = getStoredConfig();\n  if (config) {\n    processConfig(config);\n    return Promise.resolve();\n  }\n\n  return getRemoteConfig(performanceController, iid)\n    .then(processConfig)\n    .then(\n      config => storeConfig(config),\n      /** Do nothing for error, use defaults set in settings service. */\n      () => {}\n    );\n}\n\nfunction getStoredConfig(): RemoteConfigResponse | undefined {\n  const localStorage = Api.getInstance().localStorage;\n  if (!localStorage) {\n    return;\n  }\n  const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\n  if (!expiryString || !configValid(expiryString)) {\n    return;\n  }\n\n  const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\n  if (!configStringified) {\n    return;\n  }\n  try {\n    const configResponse: RemoteConfigResponse = JSON.parse(configStringified);\n    return configResponse;\n  } catch {\n    return;\n  }\n}\n\nfunction storeConfig(config: RemoteConfigResponse | undefined): void {\n  const localStorage = Api.getInstance().localStorage;\n  if (!config || !localStorage) {\n    return;\n  }\n\n  localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\n  localStorage.setItem(\n    CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n    String(\n      Date.now() +\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000\n    )\n  );\n}\n\nconst COULD_NOT_GET_CONFIG_MSG =\n  'Could not fetch config, will use default configs';\n\nfunction getRemoteConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<RemoteConfigResponse | undefined> {\n  // Perf needs auth token only to retrieve remote config.\n  return getAuthTokenPromise(performanceController.installations)\n    .then(authToken => {\n      const projectId = getProjectId(performanceController.app);\n      const apiKey = getApiKey(performanceController.app);\n      const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\n      const request = new Request(configEndPoint, {\n        method: 'POST',\n        headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\n        /* eslint-disable camelcase */\n        body: JSON.stringify({\n          app_instance_id: iid,\n          app_instance_id_token: authToken,\n          app_id: getAppId(performanceController.app),\n          app_version: SDK_VERSION,\n          sdk_version: REMOTE_CONFIG_SDK_VERSION\n        })\n        /* eslint-enable camelcase */\n      });\n      return fetch(request).then(response => {\n        if (response.ok) {\n          return response.json() as RemoteConfigResponse;\n        }\n        // In case response is not ok. This will be caught by catch.\n        throw ERROR_FACTORY.create(ErrorCode.RC_NOT_OK);\n      });\n    })\n    .catch(() => {\n      consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\n      return undefined;\n    });\n}\n\n/**\n * Processes config coming either from calling RC or from local storage.\n * This method only runs if call is successful or config in storage\n * is valid.\n */\nfunction processConfig(\n  config?: RemoteConfigResponse\n): RemoteConfigResponse | undefined {\n  if (!config) {\n    return config;\n  }\n  const settingsServiceInstance = SettingsService.getInstance();\n  const entries = config.entries || {};\n  if (entries.fpr_enabled !== undefined) {\n    // TODO: Change the assignment of loggingEnabled once the received type is\n    // known.\n    settingsServiceInstance.loggingEnabled =\n      String(entries.fpr_enabled) === 'true';\n  } else if (DEFAULT_CONFIGS.loggingEnabled !== undefined) {\n    // Config retrieved successfully, but there is no fpr_enabled in template.\n    // Use secondary configs value.\n    settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\n  }\n  if (entries.fpr_log_source) {\n    settingsServiceInstance.logSource = Number(entries.fpr_log_source);\n  } else if (DEFAULT_CONFIGS.logSource) {\n    settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\n  }\n\n  if (entries.fpr_log_endpoint_url) {\n    settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\n  } else if (DEFAULT_CONFIGS.logEndPointUrl) {\n    settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\n  }\n\n  // Key from Remote Config has to be non-empty string, otherwise use local value.\n  if (entries.fpr_log_transport_key) {\n    settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\n  } else if (DEFAULT_CONFIGS.transportKey) {\n    settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\n  }\n\n  if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate = Number(\n      entries.fpr_vc_network_request_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate =\n      DEFAULT_CONFIGS.networkRequestsSamplingRate;\n  }\n  if (entries.fpr_vc_trace_sampling_rate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate = Number(\n      entries.fpr_vc_trace_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate =\n      DEFAULT_CONFIGS.tracesSamplingRate;\n  }\n  // Set the per session trace and network logging flags.\n  settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.tracesSamplingRate\n  );\n  settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.networkRequestsSamplingRate\n  );\n  return config;\n}\n\nfunction configValid(expiry: string): boolean {\n  return Number(expiry) > Date.now();\n}\n\nfunction shouldLogAfterSampling(samplingRate: number): boolean {\n  return Math.random() <= samplingRate;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIidPromise } from './iid_service';\nimport { getConfig } from './remote_config_service';\nimport { Api } from './api_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum InitializationStatus {\n  notInitialized = 1,\n  initializationPending,\n  initialized\n}\n\nlet initializationStatus = InitializationStatus.notInitialized;\n\nlet initializationPromise: Promise<void> | undefined;\n\nexport function getInitializationPromise(\n  performanceController: PerformanceController\n): Promise<void> {\n  initializationStatus = InitializationStatus.initializationPending;\n\n  initializationPromise =\n    initializationPromise || initializePerf(performanceController);\n\n  return initializationPromise;\n}\n\nexport function isPerfInitialized(): boolean {\n  return initializationStatus === InitializationStatus.initialized;\n}\n\nfunction initializePerf(\n  performanceController: PerformanceController\n): Promise<void> {\n  return getDocumentReadyComplete()\n    .then(() => getIidPromise(performanceController.installations))\n    .then(iid => getConfig(performanceController, iid))\n    .then(\n      () => changeInitializationStatus(),\n      () => changeInitializationStatus()\n    );\n}\n\n/**\n * Returns a promise which resolves whenever the document readystate is complete or\n * immediately if it is called after page load complete.\n */\nfunction getDocumentReadyComplete(): Promise<void> {\n  const document = Api.getInstance().document;\n  return new Promise(resolve => {\n    if (document && document.readyState !== 'complete') {\n      const handler = (): void => {\n        if (document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', handler);\n          resolve();\n        }\n      };\n      document.addEventListener('readystatechange', handler);\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction changeInitializationStatus(): void {\n  initializationStatus = InitializationStatus.initialized;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsService } from './settings_service';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\nconst DEFAULT_REMAINING_TRIES = 3;\n\nlet remainingTries = DEFAULT_REMAINING_TRIES;\n\ninterface BatchEvent {\n  message: string;\n  eventTime: number;\n}\n\n/* eslint-disable camelcase */\n// CC/Fl accepted log format.\ninterface TransportBatchLogFormat {\n  request_time_ms: string;\n  client_info: ClientInfo;\n  log_source: number;\n  log_event: Log[];\n}\n\ninterface ClientInfo {\n  client_type: number;\n  js_client_info: {};\n}\n\ninterface Log {\n  source_extension_json_proto3: string;\n  event_time_ms: string;\n}\n/* eslint-enable camelcase */\n\nlet queue: BatchEvent[] = [];\n\nlet isTransportSetup: boolean = false;\n\nexport function setupTransportService(): void {\n  if (!isTransportSetup) {\n    processQueue(INITIAL_SEND_TIME_DELAY_MS);\n    isTransportSetup = true;\n  }\n}\n\n/**\n * Utilized by testing to clean up message queue and un-initialize transport service.\n */\nexport function resetTransportService(): void {\n  isTransportSetup = false;\n  queue = [];\n}\n\nfunction processQueue(timeOffset: number): void {\n  setTimeout(() => {\n    // If there is no remainingTries left, stop retrying.\n    if (remainingTries <= 0) {\n      return;\n    }\n\n    if (queue.length > 0) {\n      dispatchQueueEvents();\n    }\n    processQueue(DEFAULT_SEND_INTERVAL_MS);\n  }, timeOffset);\n}\n\nfunction dispatchQueueEvents(): void {\n  // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\n  // The staged events will be used for current logRequest attempt, remaining events will be kept\n  // for next attempt.\n  const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\n\n  /* eslint-disable camelcase */\n  // We will pass the JSON serialized event to the backend.\n  const log_event: Log[] = staged.map(evt => ({\n    source_extension_json_proto3: evt.message,\n    event_time_ms: String(evt.eventTime)\n  }));\n\n  const data: TransportBatchLogFormat = {\n    request_time_ms: String(Date.now()),\n    client_info: {\n      client_type: 1, // 1 is JS\n      js_client_info: {}\n    },\n    log_source: SettingsService.getInstance().logSource,\n    log_event\n  };\n  /* eslint-enable camelcase */\n\n  postToFlEndpoint(data)\n    .then(() => {\n      remainingTries = DEFAULT_REMAINING_TRIES;\n    })\n    .catch(() => {\n      // If the request fails for some reason, add the events that were attempted\n      // back to the primary queue to retry later.\n      queue = [...staged, ...queue];\n      remainingTries--;\n      consoleLogger.info(`Tries left: ${remainingTries}.`);\n      processQueue(DEFAULT_SEND_INTERVAL_MS);\n    });\n}\n\nfunction postToFlEndpoint(data: TransportBatchLogFormat): Promise<void> {\n  const flTransportFullUrl =\n    SettingsService.getInstance().getFlTransportFullUrl();\n  const body = JSON.stringify(data);\n\n  return navigator.sendBeacon && navigator.sendBeacon(flTransportFullUrl, body)\n    ? Promise.resolve()\n    : fetch(flTransportFullUrl, {\n        method: 'POST',\n        body,\n        keepalive: true\n      }).then();\n}\n\nfunction addToQueue(evt: BatchEvent): void {\n  if (!evt.eventTime || !evt.message) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_CC_LOG);\n  }\n  // Add the new event to the queue.\n  queue = [...queue, evt];\n}\n\n/** Log handler for cc service to send the performance logs to the server. */\nexport function transportHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  serializer: (...args: any[]) => string\n): (...args: unknown[]) => void {\n  return (...args) => {\n    const message = serializer(...args);\n    addToQueue({\n      message,\n      eventTime: Date.now()\n    });\n  };\n}\n\n/**\n * Force flush the queued events. Useful at page unload time to ensure all\n * events are uploaded.\n */\nexport function flushQueuedEvents(): void {\n  while (queue.length > 0) {\n    dispatchQueueEvents();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIid } from './iid_service';\nimport { NetworkRequest } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { Api } from './api_service';\nimport { SettingsService } from './settings_service';\nimport {\n  getServiceWorkerStatus,\n  getVisibilityState,\n  getEffectiveConnectionType\n} from '../utils/attributes_utils';\nimport {\n  isPerfInitialized,\n  getInitializationPromise\n} from './initialization_service';\nimport { transport<PERSON><PERSON><PERSON>, flushQueuedEvents } from './transport_service';\nimport { SDK_VERSION } from '../constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { getAppId } from '../utils/app_utils';\n\nconst enum ResourceType {\n  NetworkRequest,\n  Trace\n}\n\n/* eslint-disable camelcase */\ninterface ApplicationInfo {\n  google_app_id: string;\n  app_instance_id?: string;\n  web_app_info: WebAppInfo;\n  application_process_state: number;\n}\n\ninterface WebAppInfo {\n  sdk_version: string;\n  page_url: string;\n  service_worker_status: number;\n  visibility_state: number;\n  effective_connection_type: number;\n}\n\ninterface PerfNetworkLog {\n  application_info: ApplicationInfo;\n  network_request_metric: NetworkRequestMetric;\n}\n\ninterface PerfTraceLog {\n  application_info: ApplicationInfo;\n  trace_metric: TraceMetric;\n}\n\ninterface NetworkRequestMetric {\n  url: string;\n  http_method: number;\n  http_response_code: number;\n  response_payload_bytes?: number;\n  client_start_time_us?: number;\n  time_to_response_initiated_us?: number;\n  time_to_response_completed_us?: number;\n}\n\ninterface TraceMetric {\n  name: string;\n  is_auto: boolean;\n  client_start_time_us: number;\n  duration_us: number;\n  counters?: { [key: string]: number };\n  custom_attributes?: { [key: string]: string };\n}\n\ninterface Logger {\n  send: (\n    resource: NetworkRequest | Trace,\n    resourceType: ResourceType\n  ) => void | undefined;\n  flush: () => void;\n}\n\nlet logger: Logger;\n//\n// This method is not called before initialization.\nfunction sendLog(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): void {\n  if (!logger) {\n    logger = {\n      send: transportHandler(serializer),\n      flush: flushQueuedEvents\n    };\n  }\n  logger.send(resource, resourceType);\n}\n\nexport function logTrace(trace: Trace): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log if trace is auto generated and instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled && trace.isAuto) {\n    return;\n  }\n  // Do not log if trace is custom and data collection is disabled.\n  if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\n    return;\n  }\n  // Do not log if required apis are not available.\n  if (!Api.getInstance().requiredApisAvailable()) {\n    return;\n  }\n\n  if (isPerfInitialized()) {\n    sendTraceLog(trace);\n  } else {\n    // Custom traces can be used before the initialization but logging\n    // should wait until after.\n    getInitializationPromise(trace.performanceController).then(\n      () => sendTraceLog(trace),\n      () => sendTraceLog(trace)\n    );\n  }\n}\n\nexport function flushLogs(): void {\n  if (logger) {\n    logger.flush();\n  }\n}\n\nfunction sendTraceLog(trace: Trace): void {\n  if (!getIid()) {\n    return;\n  }\n\n  const settingsService = SettingsService.getInstance();\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logTraceAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(trace, ResourceType.Trace);\n}\n\nexport function logNetworkRequest(networkRequest: NetworkRequest): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log network requests if instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled) {\n    return;\n  }\n\n  // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\n  // Need to blacklist both old and new endpoints to avoid migration gap.\n  const networkRequestUrl = networkRequest.url;\n\n  // Blacklist old log endpoint and new transport endpoint.\n  // Because Performance SDK doesn't instrument requests sent from SDK itself.\n  const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\n  const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\n  if (\n    networkRequestUrl === logEndpointUrl ||\n    networkRequestUrl === flEndpointUrl\n  ) {\n    return;\n  }\n\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logNetworkAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(networkRequest, ResourceType.NetworkRequest);\n}\n\nfunction serializer(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): string {\n  if (resourceType === ResourceType.NetworkRequest) {\n    return serializeNetworkRequest(resource as NetworkRequest);\n  }\n  return serializeTrace(resource as Trace);\n}\n\nfunction serializeNetworkRequest(networkRequest: NetworkRequest): string {\n  const networkRequestMetric: NetworkRequestMetric = {\n    url: networkRequest.url,\n    http_method: networkRequest.httpMethod || 0,\n    http_response_code: 200,\n    response_payload_bytes: networkRequest.responsePayloadBytes,\n    client_start_time_us: networkRequest.startTimeUs,\n    time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\n    time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\n  };\n  const perfMetric: PerfNetworkLog = {\n    application_info: getApplicationInfo(\n      networkRequest.performanceController.app\n    ),\n    network_request_metric: networkRequestMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction serializeTrace(trace: Trace): string {\n  const traceMetric: TraceMetric = {\n    name: trace.name,\n    is_auto: trace.isAuto,\n    client_start_time_us: trace.startTimeUs,\n    duration_us: trace.durationUs\n  };\n\n  if (Object.keys(trace.counters).length !== 0) {\n    traceMetric.counters = trace.counters;\n  }\n  const customAttributes = trace.getAttributes();\n  if (Object.keys(customAttributes).length !== 0) {\n    traceMetric.custom_attributes = customAttributes;\n  }\n\n  const perfMetric: PerfTraceLog = {\n    application_info: getApplicationInfo(trace.performanceController.app),\n    trace_metric: traceMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction getApplicationInfo(firebaseApp: FirebaseApp): ApplicationInfo {\n  return {\n    google_app_id: getAppId(firebaseApp),\n    app_instance_id: getIid(),\n    web_app_info: {\n      sdk_version: SDK_VERSION,\n      page_url: Api.getInstance().getUrl(),\n      service_worker_status: getServiceWorkerStatus(),\n      visibility_state: getVisibilityState(),\n      effective_connection_type: getEffectiveConnectionType()\n    },\n    application_process_state: 0\n  };\n}\n\n/* eslint-enable camelcase */\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\nimport { logNetworkRequest } from '../services/perf_logger';\nimport { PerformanceController } from '../controllers/perf';\n\n// The order of values of this enum should not be changed.\nexport const enum HttpMethod {\n  HTTP_METHOD_UNKNOWN = 0,\n  GET = 1,\n  PUT = 2,\n  POST = 3,\n  DELETE = 4,\n  HEAD = 5,\n  PATCH = 6,\n  OPTIONS = 7,\n  TRACE = 8,\n  CONNECT = 9\n}\n\n// Durations are in microseconds.\nexport interface NetworkRequest {\n  performanceController: PerformanceController;\n  url: string;\n  httpMethod?: HttpMethod;\n  requestPayloadBytes?: number;\n  responsePayloadBytes?: number;\n  httpResponseCode?: number;\n  responseContentType?: string;\n  startTimeUs?: number;\n  timeToRequestCompletedUs?: number;\n  timeToResponseInitiatedUs?: number;\n  timeToResponseCompletedUs?: number;\n}\n\nexport function createNetworkRequestEntry(\n  performanceController: PerformanceController,\n  entry: PerformanceEntry\n): void {\n  const performanceEntry = entry as PerformanceResourceTiming;\n  if (!performanceEntry || performanceEntry.responseStart === undefined) {\n    return;\n  }\n  const timeOrigin = Api.getInstance().getTimeOrigin();\n  const startTimeUs = Math.floor(\n    (performanceEntry.startTime + timeOrigin) * 1000\n  );\n  const timeToResponseInitiatedUs = performanceEntry.responseStart\n    ? Math.floor(\n        (performanceEntry.responseStart - performanceEntry.startTime) * 1000\n      )\n    : undefined;\n  const timeToResponseCompletedUs = Math.floor(\n    (performanceEntry.responseEnd - performanceEntry.startTime) * 1000\n  );\n  // Remove the query params from logged network request url.\n  const url = performanceEntry.name && performanceEntry.name.split('?')[0];\n  const networkRequest: NetworkRequest = {\n    performanceController,\n    url,\n    responsePayloadBytes: performanceEntry.transferSize,\n    startTimeUs,\n    timeToResponseInitiatedUs,\n    timeToResponseCompletedUs\n  };\n\n  logNetworkRequest(networkRequest);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst MAX_METRIC_NAME_LENGTH = 100;\nconst RESERVED_AUTO_PREFIX = '_';\nconst oobMetrics = [\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME\n];\n\n/**\n * Returns true if the metric is custom and does not start with reserved prefix, or if\n * the metric is one of out of the box page load trace metrics.\n */\nexport function isValidMetricName(name: string, traceName?: string): boolean {\n  if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\n    return false;\n  }\n  return (\n    (traceName &&\n      traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\n      oobMetrics.indexOf(name) > -1) ||\n    !name.startsWith(RESERVED_AUTO_PREFIX)\n  );\n}\n\n/**\n * Converts the provided value to an integer value to be used in case of a metric.\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\n *\n * @returns Converted integer number to be set for the metric.\n */\nexport function convertMetricValueToInteger(providedValue: number): number {\n  const valueAsInteger: number = Math.floor(providedValue);\n  if (valueAsInteger < providedValue) {\n    consoleLogger.info(\n      `Metric value should be an Integer, setting the value as : ${valueAsInteger}.`\n    );\n  }\n  return valueAsInteger;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  TRACE_START_MARK_PREFIX,\n  TRACE_STOP_MARK_PREFIX,\n  TRACE_MEASURE_PREFIX,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME\n} from '../constants';\nimport { Api } from '../services/api_service';\nimport { logTrace, flushLogs } from '../services/perf_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport {\n  isValidCustomAttributeName,\n  isValidCustomAttributeValue\n} from '../utils/attributes_utils';\nimport {\n  isValidMetricName,\n  convertMetricValueToInteger\n} from '../utils/metric_utils';\nimport { PerformanceTrace } from '../public_types';\nimport { PerformanceController } from '../controllers/perf';\nimport { CoreVitalMetric, WebVitalMetrics } from './web_vitals';\n\nconst enum TraceState {\n  UNINITIALIZED = 1,\n  RUNNING,\n  TERMINATED\n}\n\nexport class Trace implements PerformanceTrace {\n  private state: TraceState = TraceState.UNINITIALIZED;\n  startTimeUs!: number;\n  durationUs!: number;\n  private customAttributes: { [key: string]: string } = {};\n  counters: { [counterName: string]: number } = {};\n  private api = Api.getInstance();\n  private randomId = Math.floor(Math.random() * 1000000);\n  private traceStartMark!: string;\n  private traceStopMark!: string;\n  private traceMeasure!: string;\n\n  /**\n   * @param performanceController The performance controller running.\n   * @param name The name of the trace.\n   * @param isAuto If the trace is auto-instrumented.\n   * @param traceMeasureName The name of the measure marker in user timing specification. This field\n   * is only set when the trace is built for logging when the user directly uses the user timing\n   * api (performance.mark and performance.measure).\n   */\n  constructor(\n    readonly performanceController: PerformanceController,\n    readonly name: string,\n    readonly isAuto = false,\n    traceMeasureName?: string\n  ) {\n    if (!this.isAuto) {\n      this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceMeasure =\n        traceMeasureName ||\n        `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\n\n      if (traceMeasureName) {\n        // For the case of direct user timing traces, no start stop will happen. The measure object\n        // is already available.\n        this.calculateTraceMetrics();\n      }\n    }\n  }\n\n  /**\n   * Starts a trace. The measurement of the duration starts at this point.\n   */\n  start(): void {\n    if (this.state !== TraceState.UNINITIALIZED) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STARTED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.api.mark(this.traceStartMark);\n    this.state = TraceState.RUNNING;\n  }\n\n  /**\n   * Stops the trace. The measurement of the duration of the trace stops at this point and trace\n   * is logged.\n   */\n  stop(): void {\n    if (this.state !== TraceState.RUNNING) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STOPPED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.state = TraceState.TERMINATED;\n    this.api.mark(this.traceStopMark);\n    this.api.measure(\n      this.traceMeasure,\n      this.traceStartMark,\n      this.traceStopMark\n    );\n    this.calculateTraceMetrics();\n    logTrace(this);\n  }\n\n  /**\n   * Records a trace with predetermined values. If this method is used a trace is created and logged\n   * directly. No need to use start and stop methods.\n   * @param startTime Trace start time since epoch in millisec\n   * @param duration The duration of the trace in millisec\n   * @param options An object which can optionally hold maps of custom metrics and custom attributes\n   */\n  record(\n    startTime: number,\n    duration: number,\n    options?: {\n      metrics?: { [key: string]: number };\n      attributes?: { [key: string]: string };\n    }\n  ): void {\n    if (startTime <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_START_TIME, {\n        traceName: this.name\n      });\n    }\n    if (duration <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_DURATION, {\n        traceName: this.name\n      });\n    }\n\n    this.durationUs = Math.floor(duration * 1000);\n    this.startTimeUs = Math.floor(startTime * 1000);\n    if (options && options.attributes) {\n      this.customAttributes = { ...options.attributes };\n    }\n    if (options && options.metrics) {\n      for (const metricName of Object.keys(options.metrics)) {\n        if (!isNaN(Number(options.metrics[metricName]))) {\n          this.counters[metricName] = Math.floor(\n            Number(options.metrics[metricName])\n          );\n        }\n      }\n    }\n    logTrace(this);\n  }\n\n  /**\n   * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\n   * custom metric if one with the given name does not exist. The value will be floored down to an\n   * integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Increment by value\n   */\n  incrementMetric(counter: string, numAsInteger = 1): void {\n    if (this.counters[counter] === undefined) {\n      this.putMetric(counter, numAsInteger);\n    } else {\n      this.putMetric(counter, this.counters[counter] + numAsInteger);\n    }\n  }\n\n  /**\n   * Sets a custom metric to a specified value. Will create a new custom metric if one with the\n   * given name does not exist. The value will be floored down to an integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Set custom metric to this value\n   */\n  putMetric(counter: string, numAsInteger: number): void {\n    if (isValidMetricName(counter, this.name)) {\n      this.counters[counter] = convertMetricValueToInteger(numAsInteger ?? 0);\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_CUSTOM_METRIC_NAME, {\n        customMetricName: counter\n      });\n    }\n  }\n\n  /**\n   * Returns the value of the custom metric by that name. If a custom metric with that name does\n   * not exist will return zero.\n   * @param counter\n   */\n  getMetric(counter: string): number {\n    return this.counters[counter] || 0;\n  }\n\n  /**\n   * Sets a custom attribute of a trace to a certain value.\n   * @param attr\n   * @param value\n   */\n  putAttribute(attr: string, value: string): void {\n    const isValidName = isValidCustomAttributeName(attr);\n    const isValidValue = isValidCustomAttributeValue(value);\n    if (isValidName && isValidValue) {\n      this.customAttributes[attr] = value;\n      return;\n    }\n    // Throw appropriate error when the attribute name or value is invalid.\n    if (!isValidName) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_NAME, {\n        attributeName: attr\n      });\n    }\n    if (!isValidValue) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_VALUE, {\n        attributeValue: value\n      });\n    }\n  }\n\n  /**\n   * Retrieves the value a custom attribute of a trace is set to.\n   * @param attr\n   */\n  getAttribute(attr: string): string | undefined {\n    return this.customAttributes[attr];\n  }\n\n  removeAttribute(attr: string): void {\n    if (this.customAttributes[attr] === undefined) {\n      return;\n    }\n    delete this.customAttributes[attr];\n  }\n\n  getAttributes(): { [key: string]: string } {\n    return { ...this.customAttributes };\n  }\n\n  private setStartTime(startTime: number): void {\n    this.startTimeUs = startTime;\n  }\n\n  private setDuration(duration: number): void {\n    this.durationUs = duration;\n  }\n\n  /**\n   * Calculates and assigns the duration and start time of the trace using the measure performance\n   * entry.\n   */\n  private calculateTraceMetrics(): void {\n    const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\n    const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\n    if (perfMeasureEntry) {\n      this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\n      this.startTimeUs = Math.floor(\n        (perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000\n      );\n    }\n  }\n\n  /**\n   * @param navigationTimings A single element array which contains the navigationTIming object of\n   * the page load\n   * @param paintTimings A array which contains paintTiming object of the page load\n   * @param firstInputDelay First input delay in millisec\n   */\n  static createOobTrace(\n    performanceController: PerformanceController,\n    navigationTimings: PerformanceNavigationTiming[],\n    paintTimings: PerformanceEntry[],\n    webVitalMetrics: WebVitalMetrics,\n    firstInputDelay?: number\n  ): void {\n    const route = Api.getInstance().getUrl();\n    if (!route) {\n      return;\n    }\n    const trace = new Trace(\n      performanceController,\n      OOB_TRACE_PAGE_LOAD_PREFIX + route,\n      true\n    );\n    const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\n    trace.setStartTime(timeOriginUs);\n\n    // navigationTimings includes only one element.\n    if (navigationTimings && navigationTimings[0]) {\n      trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\n      trace.putMetric(\n        'domInteractive',\n        Math.floor(navigationTimings[0].domInteractive * 1000)\n      );\n      trace.putMetric(\n        'domContentLoadedEventEnd',\n        Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000)\n      );\n      trace.putMetric(\n        'loadEventEnd',\n        Math.floor(navigationTimings[0].loadEventEnd * 1000)\n      );\n    }\n\n    const FIRST_PAINT = 'first-paint';\n    const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\n    if (paintTimings) {\n      const firstPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_PAINT\n      );\n      if (firstPaint && firstPaint.startTime) {\n        trace.putMetric(\n          FIRST_PAINT_COUNTER_NAME,\n          Math.floor(firstPaint.startTime * 1000)\n        );\n      }\n      const firstContentfulPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT\n      );\n      if (firstContentfulPaint && firstContentfulPaint.startTime) {\n        trace.putMetric(\n          FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n          Math.floor(firstContentfulPaint.startTime * 1000)\n        );\n      }\n\n      if (firstInputDelay) {\n        trace.putMetric(\n          FIRST_INPUT_DELAY_COUNTER_NAME,\n          Math.floor(firstInputDelay * 1000)\n        );\n      }\n    }\n\n    this.addWebVitalMetric(\n      trace,\n      LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n      LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.lcp\n    );\n    this.addWebVitalMetric(\n      trace,\n      CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n      CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME,\n      webVitalMetrics.cls\n    );\n    this.addWebVitalMetric(\n      trace,\n      INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n      INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.inp\n    );\n\n    // Page load logs are sent at unload time and so should be logged and\n    // flushed immediately.\n    logTrace(trace);\n    flushLogs();\n  }\n\n  static addWebVitalMetric(\n    trace: Trace,\n    metricKey: string,\n    attributeKey: string,\n    metric?: CoreVitalMetric\n  ): void {\n    if (metric) {\n      trace.putMetric(metricKey, Math.floor(metric.value * 1000));\n      if (metric.elementAttribution) {\n        trace.putAttribute(attributeKey, metric.elementAttribution);\n      }\n    }\n  }\n\n  static createUserTimingTrace(\n    performanceController: PerformanceController,\n    measureName: string\n  ): void {\n    const trace = new Trace(\n      performanceController,\n      measureName,\n      false,\n      measureName\n    );\n    logTrace(trace);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution\n} from 'web-vitals/attribution';\n\nimport { TRACE_MEASURE_PREFIX } from '../constants';\nimport { PerformanceController } from '../controllers/perf';\nimport { createNetworkRequestEntry } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { WebVitalMetrics } from '../resources/web_vitals';\n\nimport { Api } from './api_service';\nimport { getIid } from './iid_service';\n\nlet webVitalMetrics: WebVitalMetrics = {};\nlet sentPageLoadTrace: boolean = false;\nlet firstInputDelay: number | undefined;\n\nexport function setupOobResources(\n  performanceController: PerformanceController\n): void {\n  // Do not initialize unless iid is available.\n  if (!getIid()) {\n    return;\n  }\n  // The load event might not have fired yet, and that means performance\n  // navigation timing object has a duration of 0. The setup should run after\n  // all current tasks in js queue.\n  setTimeout(() => setupOobTraces(performanceController), 0);\n  setTimeout(() => setupNetworkRequests(performanceController), 0);\n  setTimeout(() => setupUserTimingTraces(performanceController), 0);\n}\n\nfunction setupNetworkRequests(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  const resources = api.getEntriesByType('resource');\n  for (const resource of resources) {\n    createNetworkRequestEntry(performanceController, resource);\n  }\n  api.setupObserver('resource', entry =>\n    createNetworkRequestEntry(performanceController, entry)\n  );\n}\n\nfunction setupOobTraces(performanceController: PerformanceController): void {\n  const api = Api.getInstance();\n  // Better support for Safari\n  if ('onpagehide' in window) {\n    api.document.addEventListener('pagehide', () =>\n      sendOobTrace(performanceController)\n    );\n  } else {\n    api.document.addEventListener('unload', () =>\n      sendOobTrace(performanceController)\n    );\n  }\n  api.document.addEventListener('visibilitychange', () => {\n    if (api.document.visibilityState === 'hidden') {\n      sendOobTrace(performanceController);\n    }\n  });\n\n  if (api.onFirstInputDelay) {\n    api.onFirstInputDelay((fid: number) => {\n      firstInputDelay = fid;\n    });\n  }\n\n  api.onLCP((metric: LCPMetricWithAttribution) => {\n    webVitalMetrics.lcp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.element\n    };\n  });\n  api.onCLS((metric: CLSMetricWithAttribution) => {\n    webVitalMetrics.cls = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.largestShiftTarget\n    };\n  });\n  api.onINP((metric: INPMetricWithAttribution) => {\n    webVitalMetrics.inp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.interactionTarget\n    };\n  });\n}\n\nfunction setupUserTimingTraces(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  // Run through the measure performance entries collected up to this point.\n  const measures = api.getEntriesByType('measure');\n  for (const measure of measures) {\n    createUserTimingTrace(performanceController, measure);\n  }\n  // Setup an observer to capture the measures from this point on.\n  api.setupObserver('measure', entry =>\n    createUserTimingTrace(performanceController, entry)\n  );\n}\n\nfunction createUserTimingTrace(\n  performanceController: PerformanceController,\n  measure: PerformanceEntry\n): void {\n  const measureName = measure.name;\n  // Do not create a trace, if the user timing marks and measures are created by\n  // the sdk itself.\n  if (\n    measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\n    TRACE_MEASURE_PREFIX\n  ) {\n    return;\n  }\n  Trace.createUserTimingTrace(performanceController, measureName);\n}\n\nfunction sendOobTrace(performanceController: PerformanceController): void {\n  if (!sentPageLoadTrace) {\n    sentPageLoadTrace = true;\n    const api = Api.getInstance();\n    const navigationTimings = api.getEntriesByType(\n      'navigation'\n    ) as PerformanceNavigationTiming[];\n    const paintTimings = api.getEntriesByType('paint');\n\n    // On page unload web vitals may be updated so queue the oob trace creation\n    // so that these updates have time to be included.\n    setTimeout(() => {\n      Trace.createOobTrace(\n        performanceController,\n        navigationTimings,\n        paintTimings,\n        webVitalMetrics,\n        firstInputDelay\n      );\n    }, 0);\n  }\n}\n\n/**\n * This service will only export the page load trace once. This function allows\n * resetting it for unit tests\n */\nexport function resetForUnitTests(): void {\n  sentPageLoadTrace = false;\n  firstInputDelay = undefined;\n  webVitalMetrics = {};\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { setupOobResources } from '../services/oob_resources_service';\nimport { SettingsService } from '../services/settings_service';\nimport { getInitializationPromise } from '../services/initialization_service';\nimport { Api } from '../services/api_service';\nimport { FirebaseApp } from '@firebase/app';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { PerformanceSettings, FirebasePerformance } from '../public_types';\nimport { validateIndexedDBOpenable } from '@firebase/util';\nimport { setupTransportService } from '../services/transport_service';\nimport { consoleLogger } from '../utils/console_logger';\n\nexport class PerformanceController implements FirebasePerformance {\n  private initialized: boolean = false;\n\n  constructor(\n    readonly app: FirebaseApp,\n    readonly installations: _FirebaseInstallationsInternal\n  ) {}\n\n  /**\n   * This method *must* be called internally as part of creating a\n   * PerformanceController instance.\n   *\n   * Currently it's not possible to pass the settings object through the\n   * constructor using Components, so this method exists to be called with the\n   * desired settings, to ensure nothing is collected without the user's\n   * consent.\n   */\n  _init(settings?: PerformanceSettings): void {\n    if (this.initialized) {\n      return;\n    }\n\n    if (settings?.dataCollectionEnabled !== undefined) {\n      this.dataCollectionEnabled = settings.dataCollectionEnabled;\n    }\n    if (settings?.instrumentationEnabled !== undefined) {\n      this.instrumentationEnabled = settings.instrumentationEnabled;\n    }\n\n    if (Api.getInstance().requiredApisAvailable()) {\n      validateIndexedDBOpenable()\n        .then(isAvailable => {\n          if (isAvailable) {\n            setupTransportService();\n            getInitializationPromise(this).then(\n              () => setupOobResources(this),\n              () => setupOobResources(this)\n            );\n            this.initialized = true;\n          }\n        })\n        .catch(error => {\n          consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\n        });\n    } else {\n      consoleLogger.info(\n        'Firebase Performance cannot start if the browser does not support ' +\n          '\"Fetch\" and \"Promise\", or cookies are disabled.'\n      );\n    }\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    SettingsService.getInstance().instrumentationEnabled = val;\n  }\n  get instrumentationEnabled(): boolean {\n    return SettingsService.getInstance().instrumentationEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    SettingsService.getInstance().dataCollectionEnabled = val;\n  }\n  get dataCollectionEnabled(): boolean {\n    return SettingsService.getInstance().dataCollectionEnabled;\n  }\n}\n", "/**\n * The Firebase Performance Monitoring Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebasePerformance,\n  PerformanceSettings,\n  PerformanceTrace\n} from './public_types';\nimport { ERROR_FACTORY, ErrorCode } from './utils/errors';\nimport { setupApi } from './services/api_service';\nimport { PerformanceController } from './controllers/perf';\nimport {\n  _registerComponent,\n  _getProvider,\n  registerVersion,\n  FirebaseApp,\n  getApp\n} from '@firebase/app';\nimport {\n  InstanceFactory,\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { Trace } from './resources/trace';\nimport '@firebase/installations';\nimport { deepEqual, getModularInstance } from '@firebase/util';\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @public\n */\nexport function getPerformance(\n  app: FirebaseApp = getApp()\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n  const perfInstance = provider.getImmediate() as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\n * @public\n */\nexport function initializePerformance(\n  app: FirebaseApp,\n  settings?: PerformanceSettings\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n\n  // throw if an instance was already created.\n  // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialSettings = provider.getOptions() as PerformanceSettings;\n    if (deepEqual(initialSettings, settings ?? {})) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n    }\n  }\n\n  const perfInstance = provider.initialize({\n    options: settings\n  }) as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a new `PerformanceTrace` instance.\n * @param performance - The {@link FirebasePerformance} instance to use.\n * @param name - The name of the trace.\n * @public\n */\nexport function trace(\n  performance: FirebasePerformance,\n  name: string\n): PerformanceTrace {\n  performance = getModularInstance(performance);\n  return new Trace(performance as PerformanceController, name);\n}\n\nconst factory: InstanceFactory<'performance'> = (\n  container: ComponentContainer,\n  { options: settings }: { options?: PerformanceSettings }\n) => {\n  // Dependencies\n  const app = container.getProvider('app').getImmediate();\n  const installations = container\n    .getProvider('installations-internal')\n    .getImmediate();\n\n  if (app.name !== DEFAULT_ENTRY_NAME) {\n    throw ERROR_FACTORY.create(ErrorCode.FB_NOT_DEFAULT);\n  }\n  if (typeof window === 'undefined') {\n    throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n  }\n  setupApi(window);\n  const perfInstance = new PerformanceController(app, installations);\n  perfInstance._init(settings);\n\n  return perfInstance;\n};\n\nfunction registerPerformance(): void {\n  _registerComponent(\n    new Component('performance', factory, ComponentType.PUBLIC)\n  );\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterPerformance();\n\nexport { FirebasePerformance, PerformanceSettings, PerformanceTrace };\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "keys", "b<PERSON><PERSON><PERSON>", "k", "includes", "aProp", "bProp", "isObject", "thing", "getModularInstance", "_delegate", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "t", "e", "n", "self", "performance", "getEntriesByType", "responseStart", "r", "document", "readyState", "domInteractive", "domContentLoadedEventStart", "domComplete", "i", "nodeName", "nodeType", "toLowerCase", "toUpperCase", "id", "classList", "trim", "length", "parentNode", "o", "u", "addEventListener", "persisted", "timeStamp", "s", "activationStart", "f", "prerendering", "wasDiscarded", "type", "rating", "delta", "entries", "concat", "Math", "floor", "random", "navigationType", "d", "PerformanceObserver", "supportedEntryTypes", "Promise", "resolve", "then", "getEntries", "observe", "assign", "buffered", "l", "m", "requestAnimationFrame", "p", "visibilityState", "v", "g", "h", "T", "E", "y", "removeEventListener", "S", "setTimeout", "firstHiddenTime", "L", "M", "D", "for<PERSON>ach", "disconnect", "startTime", "max", "push", "reportAllChanges", "C", "hadRecentInput", "c", "takeRecords", "reduce", "sources", "find", "node", "largestShiftTarget", "largestShiftTime", "largestShiftValue", "largestShiftSource", "largestShiftEntry", "loadState", "attribution", "x", "I", "A", "interactionId", "min", "F", "interactionCount", "P", "durationThreshold", "B", "O", "Map", "R", "q", "H", "entryType", "get", "duration", "latency", "set", "sort", "splice", "delete", "N", "requestIdleCallback", "W", "z", "PerformanceEventTiming", "j", "clear", "U", "V", "G", "WeakMap", "J", "K", "Q", "X", "Y", "size", "has", "map", "filter", "Set", "nt", "processingEnd", "add", "target", "abs", "renderTime", "processingStart", "rt", "apply", "interaction<PERSON>arget", "interactionTargetElement", "interactionType", "startsWith", "interactionTime", "nextPaintTime", "processedEventEntries", "longAnimationFrameEntries", "inputDelay", "processingDuration", "presentationDelay", "it", "at", "ot", "slice", "once", "capture", "timeToFirstByte", "resourceLoadDelay", "resourceLoadDuration", "elementRenderDelay", "url", "requestStart", "responseEnd", "element", "navigationEntry", "lcpEntry", "lcpResourceEntry", "Component", "instanceFactory", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "idbProxyTraps", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "wrapFunction", "func", "IDBDatabase", "transaction", "getCursorAdvanceMethods", "IDBCursor", "advance", "continue", "continuePrimaryKey", "unwrap", "storeNames", "tx", "call", "transformCachableValue", "cacheDonePromiseForTransaction", "done", "reject", "unlisten", "complete", "DOMException", "object", "getIdbProxyableTypes", "IDBObjectStore", "IDBIndex", "some", "Proxy", "IDBRequest", "promisifyRequest", "request", "promise", "success", "result", "catch", "newValue", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "all", "replaceTraps", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "version", "INTERNAL_AUTH_VERSION", "TOKEN_EXPIRATION_BUFFER", "ERROR_FACTORY", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "responseExpiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "getAuthorizationHeader", "retryIfServerError", "fn", "sleep", "ms", "VALID_FID_PATTERN", "generateFid", "fidByteArray", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "fid", "encode", "b64String", "bufferToBase64UrlSafe", "array", "btoa", "fromCharCode", "substr", "test", "_a", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "broadcastFidChange", "channel", "getBroadcastChannel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "closeBroadcastChannel", "close", "callbacks", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "openDB", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "updateOrCreateInstallationEntry", "entry", "registrationStatus", "clearTimedOutRequest", "entryWithPromise", "triggerRegistrationIfNecessary", "navigator", "onLine", "inProgressEntry", "registrationTime", "registerInstallation", "registeredInstallationEntry", "createInstallationRequest", "heartbeatServiceProvider", "endpoint", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "body", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "waitUntilFidRegistration", "updateInstallationRequest", "hasInstallationRequestTimedOut", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "isAuthTokenValid", "isAuthTokenExpired", "waitUntilAuthTokenRequest", "updateAuthTokenRequest", "makeAuthTokenRequestInProgressEntry", "inProgressAuthToken", "requestTime", "fetchAuthTokenFromServer", "updatedInstallationEntry", "hasAuthTokenRequestTimedOut", "getToken", "installationsImpl", "completeInstallationRegistration", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "container", "app", "get<PERSON><PERSON><PERSON>", "extractAppConfig", "options", "config<PERSON><PERSON><PERSON>", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "registerInstallations", "_registerComponent", "registerVersion", "SDK_VERSION", "TRACE_MEASURE_PREFIX", "OOB_TRACE_PAGE_LOAD_PREFIX", "FIRST_CONTENTFUL_PAINT_COUNTER_NAME", "FIRST_INPUT_DELAY_COUNTER_NAME", "LARGEST_CONTENTFUL_PAINT_METRIC_NAME", "INTERACTION_TO_NEXT_PAINT_METRIC_NAME", "CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME", "CONFIG_LOCAL_STORAGE_KEY", "CONFIG_EXPIRY_LOCAL_STORAGE_KEY", "SERVICE_NAME", "consoleLogger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "apiInstance", "windowInstance", "iid", "settingsServiceInstance", "Api", "window", "windowLocation", "location", "cookieEnabled", "localStorage", "perfMetrics", "onFirstInputDelay", "onLCP", "vitalsOnLCP", "onINP", "vitalsOnINP", "onCLS", "vitalsOnCLS", "getUrl", "href", "split", "mark", "measure", "measureName", "mark1", "mark2", "getEntriesByName", "getTime<PERSON>rigin", "<PERSON><PERSON><PERSON><PERSON>", "timing", "navigationStart", "requiredApisAvailable", "areCookiesEnabled", "isIndexedDBAvailable", "setupObserver", "list", "entryTypes", "getInstance", "getIid", "mergeStrings", "part1", "part2", "sizeDiff", "resultArray", "char<PERSON>t", "join", "SettingsService", "instrumentationEnabled", "dataCollectionEnabled", "loggingEnabled", "tracesSamplingRate", "networkRequestsSamplingRate", "logEndPointUrl", "flTransportEndpointUrl", "transportKey", "logSource", "logTraceAfterSampling", "logNetworkAfterSampling", "configTimeToLive", "getFlTransportFullUrl", "VisibilityState", "RESERVED_ATTRIBUTE_PREFIXES", "ATTRIBUTE_FORMAT_REGEX", "RegExp", "getServiceWorkerStatus", "serviceWorker", "controller", "getVisibilityState", "VISIBLE", "HIDDEN", "UNKNOWN", "getEffectiveConnectionType", "navigatorConnection", "connection", "effectiveType", "getAppId", "firebaseApp", "REMOTE_CONFIG_SDK_VERSION", "DEFAULT_CONFIGS", "FIS_AUTH_PREFIX", "getConfig", "performanceController", "config", "getStoredConfig", "expiryString", "getItem", "config<PERSON><PERSON><PERSON>", "expiry", "configStringified", "parse", "processConfig", "getRemoteConfig", "getAuthTokenPromise", "installationsService", "authTokenPromise", "authTokenVal", "getProjectId", "getApi<PERSON>ey", "Request", "Authorization", "app_instance_id", "app_instance_id_token", "app_id", "app_version", "sdk_version", "COULD_NOT_GET_CONFIG_MSG", "storeConfig", "setItem", "fpr_enabled", "fpr_log_source", "fpr_log_endpoint_url", "fpr_log_transport_key", "fpr_vc_network_request_sampling_rate", "fpr_vc_trace_sampling_rate", "shouldLogAfterSampling", "samplingRate", "initializationPromise", "initializationStatus", "getInitializationPromise", "initializePerf", "getDocumentReadyComplete", "handler", "getIidPromise", "iidPromise", "iidVal", "changeInitializationStatus", "logger", "remainingTries", "queue", "isTransportSetup", "processQueue", "timeOffset", "dispatchQueueEvents", "staged", "log_event", "evt", "source_extension_json_proto3", "event_time_ms", "eventTime", "postToFlEndpoint", "flTransportFullUrl", "sendBeacon", "keepalive", "request_time_ms", "client_info", "client_type", "js_client_info", "log_source", "transportHandler", "serializer", "addToQueue", "flushQueuedEvents", "sendLog", "resource", "resourceType", "send", "flush", "logTrace", "trace", "settingsService", "isAuto", "isPerfInitialized", "sendTraceLog", "serializeNetworkRequest", "networkRequest", "networkRequestMetric", "http_method", "httpMethod", "http_response_code", "response_payload_bytes", "responsePayloadBytes", "client_start_time_us", "startTimeUs", "time_to_response_initiated_us", "timeToResponseInitiatedUs", "time_to_response_completed_us", "timeToResponseCompletedUs", "perfMetric", "application_info", "getApplicationInfo", "network_request_metric", "serializeTrace", "traceMetric", "is_auto", "duration_us", "durationUs", "counters", "customAttributes", "getAttributes", "custom_attributes", "trace_metric", "google_app_id", "web_app_info", "page_url", "service_worker_status", "visibility_state", "effective_connection_type", "application_process_state", "createNetworkRequestEntry", "performanceEntry", "logNetworkRequest", "networkRequestUrl", "logEndpointUrl", "flEndpointUrl", "transferSize", "oobMetrics", "Trace", "traceMeasureName", "state", "api", "randomId", "traceStartMark", "traceStopMark", "traceMeasure", "calculateTraceMetrics", "start", "traceName", "stop", "record", "attributes", "metrics", "metricName", "isNaN", "incrementMetric", "counter", "numAsInteger", "putMetric", "isValidMetricName", "indexOf", "customMetricName", "convertMetricValueToInteger", "providedValue", "valueAsInteger", "getMetric", "putAttribute", "attr", "isValidName", "isValidCustomAttributeName", "prefix", "match", "isValidValue", "isValidCustomAttributeValue", "attributeName", "attributeValue", "getAttribute", "removeAttribute", "setStartTime", "setDuration", "perfMeasureEntries", "perfMeasureEntry", "createOobTrace", "navigationTimings", "paintTimings", "webVitalMetrics", "firstInputDelay", "route", "timeOriginUs", "domContentLoadedEventEnd", "loadEventEnd", "<PERSON><PERSON><PERSON><PERSON>", "paintObject", "firstContentful<PERSON><PERSON>t", "addWebVitalMetric", "lcp", "cls", "inp", "flushLogs", "metricKey", "<PERSON><PERSON><PERSON>", "metric", "elementAttribution", "createUserTimingTrace", "sentPageLoadTrace", "setupOobResources", "setupOobTraces", "sendOobTrace", "setupNetworkRequests", "resources", "setupUserTimingTraces", "measures", "substring", "PerformanceController", "initialized", "_init", "settings", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "onsuccess", "deleteDatabase", "onupgradeneeded", "onerror", "isAvailable", "setupTransportService", "getPerformance", "getApp", "initializePerformance", "provider", "isInitialized", "existingInstance", "getOptions", "initialize", "factory", "setupApi", "perfInstance", "registerPerformance"], "mappings": "iGAyEM,MAAOA,sBAAsBC,MAIjC,WAAAC,CAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,OAExD,EAGU,MAAAD,aAIX,WAAAX,CACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJ,MAAAH,CACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,CAAA,EACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,KAAO,GAEtD,CAf+BJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,EAGxD,EAUH,MAAMkB,EAAU,gBC7EA,SAAAM,UAAUC,EAAWC,GACnC,GAAID,IAAMC,EACR,OAAO,EAGT,MAAMC,EAAQvB,OAAOwB,KAAKH,GACpBI,EAAQzB,OAAOwB,KAAKF,GAC1B,IAAK,MAAMI,KAAKH,EAAO,CACrB,IAAKE,EAAME,SAASD,GAClB,OAAO,EAGT,MAAME,EAASP,EAA8BK,GACvCG,EAASP,EAA8BI,GAC7C,GAAII,SAASF,IAAUE,SAASD,IAC9B,IAAKT,UAAUQ,EAAOC,GACpB,OAAO,OAEJ,GAAID,IAAUC,EACnB,OAAO,CAEV,CAED,IAAK,MAAMH,KAAKD,EACd,IAAKF,EAAMI,SAASD,GAClB,OAAO,EAGX,OAAO,CACT,CAEA,SAASI,SAASC,GAChB,OAAiB,OAAVA,GAAmC,iBAAVA,CAClC,CCtEM,SAAUC,mBACd1B,GAEA,OAAIA,GAAYA,EAA+B2B,UACrC3B,EAA+B2B,UAEhC3B,CAEX,KCyBY4B,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAPD,CAAYA,IAAAA,EAOX,CAAA,IAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAIlE,MACR,8DAA8D4D,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAASpD,WACnBsD,EAMN,EC1HC,IAAAO,EAAEC,EAAEC,EAAE,WAAW,IAAIF,EAAEG,KAAKC,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAGL,GAAGA,EAAEM,cAAc,GAAGN,EAAEM,cAAcF,YAAYT,MAAM,OAAOK,CAAE,EAACO,EAAE,SAASP,GAAG,GAAG,YAAYQ,SAASC,WAAW,MAAM,UAAU,IAAIR,EAAEC,IAAI,GAAGD,EAAE,CAAC,GAAGD,EAAEC,EAAES,eAAe,MAAM,UAAU,GAAG,IAAIT,EAAEU,4BAA4BX,EAAEC,EAAEU,2BAA2B,MAAM,kBAAkB,GAAG,IAAIV,EAAEW,aAAaZ,EAAEC,EAAEW,YAAY,MAAM,oBAAoB,CAAC,MAAM,UAAW,EAACC,EAAE,SAASb,GAAG,IAAIC,EAAED,EAAEc,SAAS,OAAO,IAAId,EAAEe,SAASd,EAAEe,cAAcf,EAAEgB,cAAchE,QAAQ,KAAK,GAAI,EAACQ,EAAE,SAASuC,EAAEC,GAAG,IAAIC,EAAE,GAAG,IAAI,KAAKF,GAAG,IAAIA,EAAEe,UAAU,CAAC,IAAIR,EAAEP,EAAEvC,EAAE8C,EAAEW,GAAG,IAAIX,EAAEW,GAAGL,EAAEN,IAAIA,EAAEY,WAAWZ,EAAEY,UAAU9D,OAAOkD,EAAEY,UAAU9D,MAAM+D,QAAQb,EAAEY,UAAU9D,MAAM+D,OAAOC,OAAO,IAAId,EAAEY,UAAU9D,MAAM+D,OAAOnE,QAAQ,OAAO,KAAK,IAAI,GAAGiD,EAAEmB,OAAO5D,EAAE4D,QAAQpB,GAAG,KAAK,EAAE,OAAOC,GAAGzC,EAAE,GAAGyC,EAAEA,EAAEzC,EAAE,IAAIyC,EAAEzC,EAAE8C,EAAEW,GAAG,MAAMlB,EAAEO,EAAEe,UAAW,CAAA,CAAC,MAAMtB,GAAE,CAAE,OAAOE,CAAE,EAACqB,GAAG,EAAyBC,EAAE,SAASxB,GAAGyB,iBAAiB,YAAY,SAASxB,GAAGA,EAAEyB,YAAYH,EAAEtB,EAAE0B,UAAU3B,EAAEC,GAAI,IAAE,EAAG,EAAE2B,EAAE,WAAW,IAAI5B,EAAEE,IAAI,OAAOF,GAAGA,EAAE6B,iBAAiB,CAAC,EAAEC,EAAE,SAAS9B,EAAEC,GAAG,IAAIM,EAAEL,IAAIW,EAAE,WAAgK,OAAtVU,GAAsM,EAAEV,EAAE,qBAAqBN,IAAIC,SAASuB,cAAcH,IAAI,EAAEf,EAAE,YAAYL,SAASwB,aAAanB,EAAE,UAAUN,EAAE0B,OAAOpB,EAAEN,EAAE0B,KAAKhF,QAAQ,KAAK,OAAa,CAACd,KAAK6D,EAAE3C,WAAM,IAAS4C,GAAG,EAAEA,EAAEiC,OAAO,OAAOC,MAAM,EAAEC,QAAQ,GAAGlB,GAAG,MAAMmB,OAAOzC,KAAKD,MAAM,KAAK0C,OAAOC,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMC,eAAe5B,EAAE,EAAE6B,EAAE,SAAS1C,EAAEC,EAAEC,GAAG,IAAI,GAAGyC,oBAAoBC,oBAAoB7E,SAASiC,GAAG,CAAC,IAAIO,EAAE,IAAIoC,qBAAqB,SAAS3C,GAAG6C,QAAQC,UAAUC,MAAM,WAAW9C,EAAED,EAAEgD,aAAc,GAAG,IAAG,OAAOzC,EAAE0C,QAAQ7G,OAAO8G,OAAO,CAACjB,KAAKjC,EAAEmD,UAAS,GAAIjD,GAAG,CAAA,IAAKK,CAAE,CAAA,CAAC,MAAMP,GAAE,CAAG,EAACoD,EAAE,SAASpD,EAAEC,EAAEC,EAAEK,GAAG,IAAIM,EAAEpD,EAAE,OAAO,SAAS8D,GAAGtB,EAAE5C,OAAO,IAAIkE,GAAGhB,MAAM9C,EAAEwC,EAAE5C,OAAOwD,GAAG,UAAK,IAASA,KAAKA,EAAEZ,EAAE5C,MAAM4C,EAAEkC,MAAM1E,EAAEwC,EAAEiC,OAAO,SAASlC,EAAEC,GAAG,OAAOD,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,OAA9D,CAAsEA,EAAE5C,MAAM6C,GAAGF,EAAEC,GAAI,CAAC,EAACoD,EAAE,SAASrD,GAAGsD,uBAAqB,WAAa,OAAOA,uBAAqB,WAAa,OAAOtD,GAAI,GAAG,GAAG,EAACuD,EAAE,SAASvD,GAAGQ,SAASiB,iBAAiB,oBAAoB,WAAW,WAAWjB,SAASgD,iBAAiBxD,GAAI,KAAIyD,EAAE,SAASzD,GAAG,IAAIC,GAAE,EAAG,OAAO,WAAWA,IAAID,IAAIC,GAAE,EAAI,GAAEyD,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAWnD,SAASgD,iBAAiBhD,SAASuB,aAAa,IAAI,CAAC,EAAE6B,EAAE,SAAS5D,GAAG,WAAWQ,SAASgD,iBAAiBE,GAAG,IAAIA,EAAE,qBAAqB1D,EAAEiC,KAAKjC,EAAE2B,UAAU,EAAEkC,IAAI,EAAEC,EAAE,WAAWrC,iBAAiB,mBAAmBmC,GAAE,GAAInC,iBAAiB,qBAAqBmC,GAAE,EAAI,EAACC,EAAE,WAAWE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAI,EAACI,EAAE,WAAW,OAAON,EAAE,IAAIA,EAAEC,IAAIG,IAAItC,cAAcyC,YAAU,WAAaP,EAAEC,IAAIG,GAAI,GAAE,EAAG,KAAI,CAAC,mBAAII,GAAkB,OAAOR,KAAKhG,EAAE,SAASsC,GAAGQ,SAASuB,aAAaN,iBAAiB,sBAAoB,WAAa,OAAOzB,GAAI,IAAE,GAAIA,GAAG,EAAEmE,EAAE,CAAC,KAAK,KAAwaC,EAAE,CAAC,GAAG,KAAKC,EAAE,SAASrE,EAAEC,IAAI,SAASD,EAAEC,GAAGA,EAAEA,GAAG,CAAE,EAAld,SAASD,EAAEC,GAAGA,EAAEA,GAAG,CAAE,EAACvC,GAAG,WAAW,IAAIwC,EAAEK,EAAEyD,IAAInD,EAAEiB,EAAE,OAAOrE,EAAEiF,EAAE,SAAO,SAAW1C,GAAGA,EAAEsE,SAAO,SAAWtE,GAAG,2BAA2BA,EAAE7D,OAAOsB,EAAE8G,aAAavE,EAAEwE,UAAUjE,EAAE2D,kBAAkBrD,EAAExD,MAAMiF,KAAKmC,IAAIzE,EAAEwE,UAAU5C,IAAI,GAAGf,EAAEuB,QAAQsC,KAAK1E,GAAGE,GAAE,IAAM,GAAG,IAAGzC,IAAIyC,EAAEkD,EAAEpD,EAAEa,EAAEsD,EAAElE,EAAE0E,kBAAkBnD,GAAG,SAASjB,GAAGM,EAAEiB,EAAE,OAAO5B,EAAEkD,EAAEpD,EAAEa,EAAEsD,EAAElE,EAAE0E,kBAAkBtB,GAAG,WAAWxC,EAAExD,MAAM+C,YAAYT,MAAMY,EAAEoB,UAAUzB,GAAE,EAAI,GAAG,IAAI,IAAsD0E,CAAEnB,GAAG,WAAW,IAAIvD,EAAEK,EAAEuB,EAAE,MAAM,GAAGjB,EAAE,EAAEpD,EAAE,GAAG8D,EAAE,SAASvB,GAAGA,EAAEsE,SAAO,SAAWtE,GAAG,IAAIA,EAAE6E,eAAe,CAAC,IAAI5E,EAAExC,EAAE,GAAGyC,EAAEzC,EAAEA,EAAE4D,OAAO,GAAGR,GAAGb,EAAEwE,UAAUtE,EAAEsE,UAAU,KAAKxE,EAAEwE,UAAUvE,EAAEuE,UAAU,KAAK3D,GAAGb,EAAE3C,MAAMI,EAAEiH,KAAK1E,KAAKa,EAAEb,EAAE3C,MAAMI,EAAE,CAACuC,GAAG,CAAE,IAAGa,EAAEN,EAAElD,QAAQkD,EAAElD,MAAMwD,EAAEN,EAAE6B,QAAQ3E,EAAEyC,MAAM4E,EAAEpC,EAAE,eAAenB,GAAGuD,IAAI5E,EAAEkD,EAAEpD,EAAEO,EAAE6D,EAAEnE,EAAE0E,kBAAkBpB,GAAC,WAAahC,EAAEuD,EAAEC,eAAe7E,GAAE,EAAI,IAAGsB,GAAC,WAAaX,EAAE,EAAEN,EAAEuB,EAAE,MAAM,GAAG5B,EAAEkD,EAAEpD,EAAEO,EAAE6D,EAAEnE,EAAE0E,kBAAkBtB,GAAG,WAAW,OAAOnD,GAAI,GAAG,IAAG+D,WAAW/D,EAAE,GAAI,KAAxf,EAA8f,SAASD,GAAG,IAAIC,EAAE,SAASF,GAAG,IAAIC,EAAEC,EAAE,CAAE,EAAC,GAAGF,EAAEoC,QAAQf,OAAO,CAAC,IAAIR,EAAEb,EAAEoC,QAAQ4C,QAAM,SAAWhF,EAAEC,GAAG,OAAOD,GAAGA,EAAE3C,MAAM4C,EAAE5C,MAAM2C,EAAEC,CAAE,IAAG,GAAGY,GAAGA,EAAEoE,SAASpE,EAAEoE,QAAQ5D,OAAO,CAAC,IAAIE,GAAGtB,EAAEY,EAAEoE,SAASC,MAAM,SAASlF,GAAG,OAAOA,EAAEmF,MAAM,IAAInF,EAAEmF,KAAKpE,QAAS,KAAId,EAAE,GAAGsB,IAAIrB,EAAE,CAACkF,mBAAmB3H,EAAE8D,EAAE4D,MAAME,iBAAiBxE,EAAE2D,UAAUc,kBAAkBzE,EAAExD,MAAMkI,mBAAmBhE,EAAEiE,kBAAkB3E,EAAE4E,UAAUlF,EAAEM,EAAE2D,YAAa,CAAA,CAAC,OAAOpI,OAAO8G,OAAOlD,EAAE,CAAC0F,YAAYxF,GAAI,CAAhb,CAAibD,GAAGD,EAAEE,EAAG,GAAED,EAAE,EAA6Z0F,EAAE,EAAEC,EAAE,IAAI9H,EAAE,EAAE+H,EAAE,SAAS7F,GAAGA,EAAEsE,SAAO,SAAWtE,GAAGA,EAAE8F,gBAAgBF,EAAEtD,KAAKyD,IAAIH,EAAE5F,EAAE8F,eAAehI,EAAEwE,KAAKmC,IAAI3G,EAAEkC,EAAE8F,eAAeH,EAAE7H,GAAGA,EAAE8H,GAAG,EAAE,EAAE,EAAG,KAAII,EAAE,WAAW,OAAOhG,EAAE2F,EAAEvF,YAAY6F,kBAAkB,CAAE,EAACC,EAAE,WAAW,qBAAqB9F,aAAaJ,IAAIA,EAAE0C,EAAE,QAAQmD,EAAE,CAAC5D,KAAK,QAAQkB,UAAS,EAAGgD,kBAAkB,IAAK,EAACC,EAAE,GAAGC,EAAE,IAAIC,IAAIC,EAAE,EAA8EC,EAAE,GAAGC,EAAE,SAASzG,GAAG,GAAGwG,EAAElC,SAAO,SAAWrE,GAAG,OAAOA,EAAED,EAAG,IAAGA,EAAE8F,eAAe,gBAAgB9F,EAAE0G,UAAU,CAAC,IAAIzG,EAAEmG,EAAEA,EAAE/E,OAAO,GAAGnB,EAAEmG,EAAEM,IAAI3G,EAAE8F,eAAe,GAAG5F,GAAGkG,EAAE/E,OAAO,IAAIrB,EAAE4G,SAAS3G,EAAE4G,QAAQ,CAAC,GAAG3G,EAAEF,EAAE4G,SAAS1G,EAAE2G,SAAS3G,EAAEkC,QAAQ,CAACpC,GAAGE,EAAE2G,QAAQ7G,EAAE4G,UAAU5G,EAAE4G,WAAW1G,EAAE2G,SAAS7G,EAAEwE,YAAYtE,EAAEkC,QAAQ,GAAGoC,WAAWtE,EAAEkC,QAAQsC,KAAK1E,OAAO,CAAC,IAAIO,EAAE,CAACW,GAAGlB,EAAE8F,cAAce,QAAQ7G,EAAE4G,SAASxE,QAAQ,CAACpC,IAAIqG,EAAES,IAAIvG,EAAEW,GAAGX,GAAG6F,EAAE1B,KAAKnE,EAAG,CAAA6F,EAAEW,MAAM,SAAS/G,EAAEC,GAAG,OAAOA,EAAE4G,QAAQ7G,EAAE6G,OAAQ,IAAGT,EAAE/E,OAAO,IAAI+E,EAAEY,OAAO,IAAI1C,SAAS,SAAStE,GAAG,OAAOqG,EAAEY,OAAOjH,EAAEkB,GAAI,GAAG,CAAA,CAAE,EAACgG,EAAE,SAASlH,GAAG,IAAIC,EAAEE,KAAKgH,qBAAqBhH,KAAK8D,WAAW/D,GAAG,EAAE,OAAOF,EAAEyD,EAAEzD,GAAG,WAAWQ,SAASgD,gBAAgBxD,KAAKE,EAAED,EAAED,GAAGuD,EAAEvD,IAAIE,CAAE,EAACkH,EAAE,CAAC,IAAI,KAAKC,EAAE,SAASrH,EAAEC,GAAG,2BAA2BE,MAAM,kBAAkBmH,uBAAuBhL,YAAY2D,EAAEA,GAAG,CAAE,EAACvC,GAAC,WAAa,IAAIwC,EAAEgG,IAAI,IAAI3F,EAAEM,EAAEiB,EAAE,OAAOrE,EAAE,SAASuC,GAAGkH,GAAC,WAAalH,EAAEsE,QAAQmC,GAAG,IAAIxG,EAAz8B,WAAW,IAAID,EAAEsC,KAAKyD,IAAIK,EAAE/E,OAAO,EAAEiB,KAAKC,OAAOyD,IAAIO,GAAG,KAAK,OAAOH,EAAEpG,EAAG,CAAk4BuH,GAAItH,GAAGA,EAAE4G,UAAUhG,EAAExD,QAAQwD,EAAExD,MAAM4C,EAAE4G,QAAQhG,EAAEuB,QAAQnC,EAAEmC,QAAQ7B,IAAK,GAAG,EAACgB,EAAEmB,EAAE,QAAQjF,EAAE,CAAC0I,kBAAkB,QAAQjG,EAAED,EAAEkG,yBAAoB,IAASjG,EAAEA,EAAE,KAAKK,EAAE6C,EAAEpD,EAAEa,EAAEuG,EAAEnH,EAAE0E,kBAAkBpD,IAAIA,EAAE0B,QAAQ,CAAChB,KAAK,cAAckB,UAAS,IAAKI,GAAG,WAAW9F,EAAE8D,EAAEwD,eAAexE,GAAE,EAAI,IAAGiB,GAAC,WAAa+E,EAAEP,IAAII,EAAE/E,OAAO,EAAEgF,EAAEmB,QAAQ3G,EAAEiB,EAAE,OAAOvB,EAAE6C,EAAEpD,EAAEa,EAAEuG,EAAEnH,EAAE0E,iBAAkB,IAAI,IAAI,EAAC8C,EAAE,GAAGC,EAAE,GAAGvK,EAAE,EAAEwK,GAAE,IAAIC,QAAQC,GAAE,IAAIvB,IAAIwB,IAAG,EAAEC,EAAE,SAAS/H,GAAGyH,EAAEA,EAAEpF,OAAOrC,GAAGgI,GAAI,EAACA,EAAE,WAAWF,GAAE,IAAIA,GAAEZ,EAAEe,GAAG,EAAEA,EAAE,WAAWJ,GAAEK,KAAK,IAAIL,GAAEvD,SAAO,SAAWtE,EAAEC,GAAGoG,EAAE8B,IAAIlI,IAAI4H,GAAEZ,OAAOhH,EAAG,IAAG,IAAID,EAAEoG,EAAEgC,KAAK,SAASpI,GAAG,OAAO2H,GAAEhB,IAAI3G,EAAEoC,QAAQ,GAAI,IAAGnC,EAAEyH,EAAErG,OAAO,GAAGqG,EAAEA,EAAEW,QAAM,SAAWnI,EAAEK,GAAG,OAAOA,GAAGN,GAAGD,EAAEjC,SAASmC,EAAG,IAAG,IAAI,IAAIA,EAAE,IAAIoI,IAAI/H,EAAE,EAAEA,EAAEmH,EAAErG,OAAOd,IAAI,CAAC,IAAIM,EAAE6G,EAAEnH,GAAGgI,GAAG1H,EAAE2D,UAAU3D,EAAE2H,eAAelE,SAAO,SAAWtE,GAAGE,EAAEuI,IAAIzI,EAAG,GAAE,CAAC,IAAIvC,EAAEgK,EAAEpG,OAAO,EAAE,GAAGoG,EAAEA,EAAEY,iBAAiBrI,EAAEC,GAAG,OAAOD,EAAEwE,UAAUrH,GAAG8C,EAAExC,GAAGyC,EAAEiI,IAAInI,EAAG,IAAG8H,IAAG,CAAG,EAAAtB,EAAE9B,MAAI,SAAW1E,GAAGA,EAAE8F,eAAe9F,EAAE0I,SAASb,GAAEM,IAAInI,EAAE8F,gBAAgB+B,GAAEf,IAAI9G,EAAE8F,cAAc9F,EAAE0I,OAAQ,IAAG,SAAS1I,GAAG,IAAIC,EAAEC,EAAEF,EAAEwE,UAAUxE,EAAE4G,SAASzJ,EAAEmF,KAAKmC,IAAItH,EAAE6C,EAAEwI,eAAe,IAAI,IAAIjI,EAAEmH,EAAErG,OAAO,EAAEd,GAAG,EAAEA,IAAI,CAAC,IAAIM,EAAE6G,EAAEnH,GAAG,GAAG+B,KAAKqG,IAAIzI,EAAEW,EAAE+H,aAAa,EAAE,EAAE3I,EAAEY,GAAG2D,UAAUlC,KAAKyD,IAAI/F,EAAEwE,UAAUvE,EAAEuE,WAAWvE,EAAE4I,gBAAgBvG,KAAKyD,IAAI/F,EAAE6I,gBAAgB5I,EAAE4I,iBAAiB5I,EAAEuI,cAAclG,KAAKmC,IAAIzE,EAAEwI,cAAcvI,EAAEuI,eAAevI,EAAEmC,QAAQsC,KAAK1E,GAAG,KAAM,CAAA,CAACC,IAAIA,EAAE,CAACuE,UAAUxE,EAAEwE,UAAUqE,gBAAgB7I,EAAE6I,gBAAgBL,cAAcxI,EAAEwI,cAAcI,WAAW1I,EAAEkC,QAAQ,CAACpC,IAAI0H,EAAEhD,KAAKzE,KAAKD,EAAE8F,eAAe,gBAAgB9F,EAAE0G,YAAYiB,GAAEb,IAAI9G,EAAEC,GAAG+H,GAAI,IAAG,IAAcO,GAAG,SAASvI,EAAEC,GAAG,IAAI,IAAIC,EAAEK,EAAE,GAAGM,EAAE,EAAEX,EAAEuH,EAAE5G,GAAGA,IAAI,KAAKX,EAAEsE,UAAUtE,EAAE0G,SAAS5G,GAAG,CAAC,GAAGE,EAAEsE,UAAUvE,EAAE,MAAMM,EAAEmE,KAAKxE,EAAG,CAAA,OAAOK,CAAC,EAAEuI,GAAG,SAAS9I,EAAEE,GAAGD,IAAIA,EAAEyC,EAAE,uBAAuBqF,IAAIV,GAAC,SAAWpH,GAAG,IAAIC,EAAE,SAASF,GAAG,IAAIC,EAAED,EAAEoC,QAAQ,GAAGlC,EAAEyH,GAAEhB,IAAI1G,GAAGY,EAAEZ,EAAE4I,gBAAgBtH,EAAErB,EAAEsI,cAAc1D,EAAE5E,EAAEkC,QAAQ2E,MAAM,SAAS/G,EAAEC,GAAG,OAAOD,EAAE6I,gBAAgB5I,EAAE4I,eAAgB,IAAGrH,EAAE+G,GAAGtI,EAAEuE,UAAUjD,GAAGK,EAAE5B,EAAEoC,QAAQ8C,MAAI,SAAWlF,GAAG,OAAOA,EAAE0I,MAAO,IAAG5G,EAAEF,GAAGA,EAAE8G,QAAQb,GAAElB,IAAI1G,EAAE6F,eAAepD,EAAE,CAACzC,EAAEuE,UAAUvE,EAAE2G,SAASrF,GAAGc,OAAOb,EAAE4G,KAAK,SAASpI,GAAG,OAAOA,EAAEwE,UAAUxE,EAAE4G,QAAS,KAAIxD,EAAEd,KAAKmC,IAAIsE,MAAMzG,KAAKI,GAAGW,EAAE,CAAC2F,kBAAkBvL,EAAEqE,GAAGmH,yBAAyBnH,EAAEoH,gBAAgBjJ,EAAE9D,KAAKgN,WAAW,OAAO,WAAW,UAAUC,gBAAgBnJ,EAAEuE,UAAU6E,cAAcjG,EAAEkG,sBAAsBxE,EAAEyE,0BAA0B/H,EAAEgI,WAAW3I,EAAEZ,EAAEuE,UAAUiF,mBAAmBlI,EAAEV,EAAE6I,kBAAkBpH,KAAKmC,IAAIrB,EAAE7B,EAAE,GAAGkE,UAAUlF,EAAEN,EAAEuE,YAAY,OAAOpI,OAAO8G,OAAOlD,EAAE,CAAC0F,YAAYrC,GAAI,CAAluB,CAAmuBpD,GAAGD,EAAEE,EAAG,GAAEA,IAAIyJ,GAAG,CAAC,KAAK,KAAKC,GAAG,GAAGC,GAAG,SAAS7J,EAAEC,IAAI,SAASD,EAAEC,GAAGA,EAAEA,GAAG,GAAGvC,GAAG,WAAW,IAAIwC,EAAEK,EAAEyD,IAAInD,EAAEiB,EAAE,OAAOrE,EAAE,SAASuC,GAAGC,EAAE0E,mBAAmB3E,EAAEA,EAAE8J,OAAO,IAAI9J,EAAEsE,SAAO,SAAWtE,GAAGA,EAAEwE,UAAUjE,EAAE2D,kBAAkBrD,EAAExD,MAAMiF,KAAKmC,IAAIzE,EAAEwE,UAAU5C,IAAI,GAAGf,EAAEuB,QAAQ,CAACpC,GAAGE,IAAK,GAAE,EAAEqB,EAAEmB,EAAE,2BAA2BjF,GAAG,GAAG8D,EAAE,CAACrB,EAAEkD,EAAEpD,EAAEa,EAAE8I,GAAG1J,EAAE0E,kBAAkB,IAAIG,EAAErB,cAAcmG,GAAG/I,EAAEK,MAAMzD,EAAE8D,EAAEwD,eAAexD,EAAEgD,aAAaqF,GAAG/I,EAAEK,KAAI,EAAGhB,GAAE,GAAK,IAAG,CAAC,UAAU,SAASoE,SAAO,SAAWtE,GAAGyB,iBAAiBzB,GAAG,WAAW,OAAOkH,EAAEpC,EAAG,GAAE,CAACiF,MAAK,EAAGC,SAAQ,GAAK,IAAGzG,EAAEuB,GAAGtD,GAAG,SAASjB,GAAGM,EAAEiB,EAAE,OAAO5B,EAAEkD,EAAEpD,EAAEa,EAAE8I,GAAG1J,EAAE0E,kBAAkBtB,GAAC,WAAaxC,EAAExD,MAAM+C,YAAYT,MAAMY,EAAEoB,UAAUiI,GAAG/I,EAAEK,KAAI,EAAGhB,GAAE,EAAI,GAAG,GAAE,CAAE,GAAE,CAAznB,WAAqoBD,GAAG,IAAIM,EAAE,SAASP,GAAG,IAAIC,EAAE,CAACgK,gBAAgB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmBpK,EAAE3C,OAAO,GAAG2C,EAAEoC,QAAQf,OAAO,CAAC,IAAId,EAAEL,IAAI,GAAGK,EAAE,CAAC,IAAIM,EAAEN,EAAEsB,iBAAiB,EAAEN,EAAEvB,EAAEoC,QAAQpC,EAAEoC,QAAQf,OAAO,GAAGyD,EAAEvD,EAAE8I,KAAKjK,YAAYC,iBAAiB,YAAYgI,QAAM,SAAWrI,GAAG,OAAOA,EAAE7D,OAAOoF,EAAE8I,GAAI,IAAG,GAAG7I,EAAEc,KAAKmC,IAAI,EAAElE,EAAED,cAAcO,GAAGe,EAAEU,KAAKmC,IAAIjD,EAAEsD,GAAGA,EAAEwF,cAAcxF,EAAEN,WAAW3D,EAAE,GAAGiB,EAAEQ,KAAKmC,IAAI7C,EAAEkD,EAAEA,EAAEyF,YAAY1J,EAAE,GAAG6B,EAAEJ,KAAKmC,IAAI3C,EAAEP,EAAEiD,UAAU3D,GAAGZ,EAAE,CAACuK,QAAQ/M,EAAE8D,EAAEiJ,SAASP,gBAAgBzI,EAAE0I,kBAAkBtI,EAAEJ,EAAE2I,qBAAqBrI,EAAEF,EAAEwI,mBAAmB1H,EAAEZ,EAAE2I,gBAAgBlK,EAAEmK,SAASnJ,GAAGA,EAAE8I,MAAMpK,EAAEoK,IAAI9I,EAAE8I,KAAKvF,IAAI7E,EAAE0K,iBAAiB7F,EAAG,CAAA,CAAC,OAAO1I,OAAO8G,OAAOlD,EAAE,CAAC0F,YAAYzF,GAAI,CAApqB,CAAqqBA,GAAGD,EAAEO,EAAG,GAAEN,EAAG,EC4BhpT,MAAA2K,UAiBX,WAAA/O,CACWM,EACA0O,EACA5I,GAFA/F,KAAIC,KAAJA,EACAD,KAAe2O,gBAAfA,EACA3O,KAAI+F,KAAJA,EAnBX/F,KAAiB4O,mBAAG,EAIpB5O,KAAY6O,aAAe,GAE3B7O,KAAA8O,kBAA2C,OAE3C9O,KAAiB+O,kBAAwC,IAYrD,CAEJ,oBAAAC,CAAqBC,GAEnB,OADAjP,KAAK8O,kBAAoBG,EAClBjP,IACR,CAED,oBAAAkP,CAAqBN,GAEnB,OADA5O,KAAK4O,kBAAoBA,EAClB5O,IACR,CAED,eAAAmP,CAAgBC,GAEd,OADApP,KAAK6O,aAAeO,EACbpP,IACR,CAED,0BAAAqP,CAA2BC,GAEzB,OADAtP,KAAK+O,kBAAoBO,EAClBtP,IACR,ECnEH,IAAIuP,GACAC,GAqBJ,MAAMC,GAAmB,IAAI/D,QACvBgE,GAAqB,IAAIhE,QACzBiE,GAA2B,IAAIjE,QAC/BkE,GAAiB,IAAIlE,QACrBmE,GAAwB,IAAInE,QA0DlC,IAAIoE,GAAgB,CAChB,GAAArF,CAAI+B,EAAQuD,EAAMC,GACd,GAAIxD,aAAkByD,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOL,GAAmBjF,IAAI+B,GAElC,GAAa,qBAATuD,EACA,OAAOvD,EAAO0D,kBAAoBP,GAAyBlF,IAAI+B,GAGnE,GAAa,UAATuD,EACA,OAAOC,EAASE,iBAAiB,QAC3BC,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,GAE5D,CAED,OAAOG,KAAK7D,EAAOuD,GACtB,EACDnF,IAAG,CAAC4B,EAAQuD,EAAM5O,KACdqL,EAAOuD,GAAQ5O,GACR,GAEX8K,IAAG,CAACO,EAAQuD,IACJvD,aAAkByD,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQvD,GAMvB,SAAS8D,aAAaC,GAIlB,OAAIA,IAASC,YAAYpQ,UAAUqQ,aAC7B,qBAAsBR,eAAe7P,UA9G/C,SAASsQ,0BACL,OAAQlB,KACHA,GAAuB,CACpBmB,UAAUvQ,UAAUwQ,QACpBD,UAAUvQ,UAAUyQ,SACpBF,UAAUvQ,UAAU0Q,oBAEhC,CAmHQJ,GAA0B7O,SAAS0O,GAC5B,YAAahN,GAIhB,OADAgN,EAAK1D,MAAMkE,OAAO/Q,MAAOuD,GAClB8M,KAAKZ,GAAiBhF,IAAIzK,MACrC,EAEG,YAAauD,GAGhB,OAAO8M,KAAKE,EAAK1D,MAAMkE,OAAO/Q,MAAOuD,GACzC,EAvBW,SAAUyN,KAAezN,GAC5B,MAAM0N,EAAKV,EAAKW,KAAKH,OAAO/Q,MAAOgR,KAAezN,GAElD,OADAoM,GAAyB/E,IAAIqG,EAAID,EAAWnG,KAAOmG,EAAWnG,OAAS,CAACmG,IACjEX,KAAKY,EAChB,CAoBR,CACA,SAASE,uBAAuBhQ,GAC5B,MAAqB,mBAAVA,EACAmP,aAAanP,IAGpBA,aAAiB8O,gBAhGzB,SAASmB,+BAA+BH,GAEpC,GAAIvB,GAAmBzD,IAAIgF,GACvB,OACJ,MAAMI,EAAO,IAAI1K,SAAQ,CAACC,EAAS0K,KAC/B,MAAMC,SAAW,KACbN,EAAGpJ,oBAAoB,WAAY2J,UACnCP,EAAGpJ,oBAAoB,QAAS/E,OAChCmO,EAAGpJ,oBAAoB,QAAS/E,MAAM,EAEpC0O,SAAW,KACb5K,IACA2K,UAAU,EAERzO,MAAQ,KACVwO,EAAOL,EAAGnO,OAAS,IAAI2O,aAAa,aAAc,eAClDF,UAAU,EAEdN,EAAG1L,iBAAiB,WAAYiM,UAChCP,EAAG1L,iBAAiB,QAASzC,OAC7BmO,EAAG1L,iBAAiB,QAASzC,MAAM,IAGvC4M,GAAmB9E,IAAIqG,EAAII,EAC/B,CAyEQD,CAA+BjQ,GA9JhBuQ,EA+JDvQ,EA1JtB,SAASwQ,uBACL,OAAQpC,KACHA,GAAoB,CACjBiB,YACAoB,eACAC,SACAlB,UACAV,gBAEZ,CAiJ6B0B,GA/JgCG,MAAMlJ,GAAM8I,aAAkB9I,IAgK5E,IAAImJ,MAAM5Q,EAAO2O,IAErB3O,GAlKW,IAACuQ,CAmKvB,CACA,SAASrB,KAAKlP,GAGV,GAAIA,aAAiB6Q,WACjB,OA3IR,SAASC,iBAAiBC,GACtB,MAAMC,EAAU,IAAIxL,SAAQ,CAACC,EAAS0K,KAClC,MAAMC,SAAW,KACbW,EAAQrK,oBAAoB,UAAWuK,SACvCF,EAAQrK,oBAAoB,QAAS/E,MAAM,EAEzCsP,QAAU,KACZxL,EAAQyJ,KAAK6B,EAAQG,SACrBd,UAAU,EAERzO,MAAQ,KACVwO,EAAOY,EAAQpP,OACfyO,UAAU,EAEdW,EAAQ3M,iBAAiB,UAAW6M,SACpCF,EAAQ3M,iBAAiB,QAASzC,MAAM,IAe5C,OAbAqP,EACKtL,MAAM1F,IAGHA,aAAiBwP,WACjBlB,GAAiB7E,IAAIzJ,EAAO+Q,EAC/B,IAGAI,OAAM,SAGXzC,GAAsBjF,IAAIuH,EAASD,GAC5BC,CACX,CA4GeF,CAAiB9Q,GAG5B,GAAIyO,GAAe3D,IAAI9K,GACnB,OAAOyO,GAAenF,IAAItJ,GAC9B,MAAMoR,EAAWpB,uBAAuBhQ,GAOxC,OAJIoR,IAAapR,IACbyO,GAAehF,IAAIzJ,EAAOoR,GAC1B1C,GAAsBjF,IAAI2H,EAAUpR,IAEjCoR,CACX,CACA,MAAMxB,OAAU5P,GAAU0O,GAAsBpF,IAAItJ,GCrIpD,MAAMqR,GAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,GAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,GAAgB,IAAItI,IAC1B,SAASuI,UAAUnG,EAAQuD,GACvB,KAAMvD,aAAkBgE,cAClBT,KAAQvD,GACM,iBAATuD,EACP,OAEJ,GAAI2C,GAAcjI,IAAIsF,GAClB,OAAO2C,GAAcjI,IAAIsF,GAC7B,MAAM6C,EAAiB7C,EAAKhP,QAAQ,aAAc,IAC5C8R,EAAW9C,IAAS6C,EACpBE,EAAUL,GAAa5Q,SAAS+Q,GACtC,KAEEA,KAAmBC,EAAWhB,SAAWD,gBAAgBxR,aACrD0S,IAAWN,GAAY3Q,SAAS+Q,GAClC,OAEJ,MAAMhP,OAASmP,eAAgBC,KAAczP,GAEzC,MAAM0N,EAAKjR,KAAKyQ,YAAYuC,EAAWF,EAAU,YAAc,YAC/D,IAAItG,EAASyE,EAAGgC,MAQhB,OAPIJ,IACArG,EAASA,EAAO0G,MAAM3P,EAAK4P,iBAMjBxM,QAAQyM,IAAI,CACtB5G,EAAOoG,MAAmBrP,GAC1BuP,GAAW7B,EAAGI,QACd,EACR,EAEA,OADAqB,GAAc9H,IAAImF,EAAMnM,QACjBA,MACX,ED+BA,SAASyP,aAAa/D,GAClBQ,GAAgBR,EAASQ,GAC7B,CChCAuD,EAAcC,GACPpT,OAAA8G,OAAA9G,OAAA8G,OAAA,CAAA,EAAAsM,GACH,CAAA7I,IAAK,CAAC+B,EAAQuD,EAAMC,IAAa2C,UAAUnG,EAAQuD,IAASuD,EAAS7I,IAAI+B,EAAQuD,EAAMC,GACvF/D,IAAK,CAACO,EAAQuD,MAAW4C,UAAUnG,EAAQuD,IAASuD,EAASrH,IAAIO,EAAQuD,sDCxEhEwD,GAAqB,IAErBC,GAAkB,KAAKC,KACvBC,GAAwB,SAKxBC,GAA0B,KCwB1BC,GAAgB,IAAItT,aDtBV,gBACK,gBCD2C,CACrE,4BACE,kDACF,iBAA4B,2CAC5B,yBAAoC,mCACpC,iBACE,6FACF,cAAyB,kDACzB,8BACE,6EA4BE,SAAUuT,cAAc/Q,GAC5B,OACEA,aAAiBrD,eACjBqD,EAAMlD,KAAKiC,SAAQ,iBAEvB,CCxCgB,SAAAiS,0BAAyBC,UAAEA,IACzC,MAAO,4DAAqCA,iBAC9C,CAEM,SAAUC,iCACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,WA8DuCC,EA9DMJ,EAASG,UAgEjDE,OAAOD,EAAkBtT,QAAQ,IAAK,SA/D3CwT,aAAc7Q,KAAKD,OA6DvB,IAA2C4Q,CA3D3C,CAEOtB,eAAeyB,qBACpBC,EACAR,GAEA,MACMS,SADoCT,EAASU,QACpB7R,MAC/B,OAAO8Q,GAAcrT,OAAiC,iBAAA,CACpDkU,cACAG,WAAYF,EAAU9U,KACtBiV,cAAeH,EAAU7U,QACzBiV,aAAcJ,EAAUK,QAE5B,CAEgB,SAAAC,YAAWC,OAAEA,IAC3B,OAAO,IAAIC,QAAQ,CACjB,eAAgB,mBAChBC,OAAQ,mBACR,iBAAkBF,GAEtB,CAEgB,SAAAG,mBACdC,GACAC,aAAEA,IAEF,MAAMC,EAAUP,WAAWK,GAE3B,OADAE,EAAQC,OAAO,gBAmCjB,SAASC,uBAAuBH,GAC9B,MAAO,GAAG5B,MAAyB4B,GACrC,CArCkCG,CAAuBH,IAChDC,CACT,CAeOxC,eAAe2C,mBACpBC,GAEA,MAAMtD,QAAesD,IAErB,OAAItD,EAAO0C,QAAU,KAAO1C,EAAO0C,OAAS,IAEnCY,IAGFtD,CACT,CCnFM,SAAUuD,MAAMC,GACpB,OAAO,IAAIlP,SAAcC,IACvBmB,WAAWnB,EAASiP,EAAG,GAE3B,CCHO,MAAMC,GAAoB,oBAOjB,SAAAC,cACd,IAGE,MAAMC,EAAe,IAAIC,WAAW,KAElChS,KAAKiS,QAAWjS,KAAyCkS,UACpDC,gBAAgBJ,GAGvBA,EAAa,GAAK,IAAcA,EAAa,GAAK,GAElD,MAAMK,EAUV,SAASC,OAAON,GACd,MAAMO,EChCF,SAAUC,sBAAsBC,GAEpC,OADYC,KAAKtV,OAAOuV,gBAAgBF,IAC7B1V,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IAChD,CD6BoByV,CAAsBR,GAIxC,OAAOO,EAAUK,OAAO,EAAG,GAC7B,CAhBgBN,CAAON,GAEnB,OAAOF,GAAkBe,KAAKR,GAAOA,EApBd,EAqBxB,CAAC,MAAAS,GAEA,MAvBuB,EAwBxB,CACH,CEzBM,SAAUC,OAAO1B,GACrB,MAAO,GAAGA,EAAU2B,WAAW3B,EAAU4B,OAC3C,CCDA,MAAMC,GAA2D,IAAI9M,IAMrD,SAAA+M,WAAW9B,EAAsBgB,GAC/C,MAAMnV,EAAM6V,OAAO1B,GAEnB+B,uBAAuBlW,EAAKmV,GAsD9B,SAASgB,mBAAmBnW,EAAamV,GACvC,MAAMiB,EASR,SAASC,uBACFC,IAAoB,qBAAsBvT,OAC7CuT,GAAmB,IAAIC,iBAAiB,yBACxCD,GAAiBE,UAAY3T,IAC3BqT,uBAAuBrT,EAAEpD,KAAKO,IAAK6C,EAAEpD,KAAK0V,IAAI,GAGlD,OAAOmB,EACT,CAjBkBD,GACZD,GACFA,EAAQK,YAAY,CAAEzW,MAAKmV,SAiB/B,SAASuB,wBACyB,IAA5BV,GAAmBlL,MAAcwL,KACnCA,GAAiBK,QACjBL,GAAmB,KAEvB,CApBEI,EACF,CA3DEP,CAAmBnW,EAAKmV,EAC1B,CAyCA,SAASe,uBAAuBlW,EAAamV,GAC3C,MAAMyB,EAAYZ,GAAmBzM,IAAIvJ,GACzC,GAAK4W,EAIL,IAAK,MAAMxI,KAAYwI,EACrBxI,EAAS+G,EAEb,CAUA,IAAImB,GAA4C,KCrEhD,MAEMO,GAAoB,+BAS1B,IAAIC,GAA2D,KAC/D,SAASC,eAgBP,OAfKD,KACHA,GT3BJ,SAASE,OAAOjY,EAAMwT,GAAS0E,QAAEA,EAAOC,QAAEA,EAAOC,SAAEA,EAAQC,WAAEA,GAAe,IACxE,MAAMpG,EAAUqG,UAAUC,KAAKvY,EAAMwT,GAC/BgF,EAAcpI,KAAK6B,GAoBzB,OAnBIkG,GACAlG,EAAQ3M,iBAAiB,iBAAkBmT,IACvCN,EAAQ/H,KAAK6B,EAAQG,QAASqG,EAAMC,WAAYD,EAAME,WAAYvI,KAAK6B,EAAQzB,aAAciI,EAAM,IAGvGP,GACAjG,EAAQ3M,iBAAiB,WAAYmT,GAAUP,EAE/CO,EAAMC,WAAYD,EAAME,WAAYF,KAExCD,EACK5R,MAAMgS,IACHP,GACAO,EAAGtT,iBAAiB,SAAS,IAAM+S,MACnCD,GACAQ,EAAGtT,iBAAiB,iBAAkBmT,GAAUL,EAASK,EAAMC,WAAYD,EAAME,WAAYF,IAChG,IAEApG,OAAM,SACJmG,CACX,CSIgBP,CAdM,kCACG,EAa+B,CAClDE,QAAS,CAACS,EAAIF,KAMZ,GACO,IADCA,EAEJE,EAAGC,kBAAkBf,GACxB,KAIAC,EACT,CAeOjF,eAAenI,IACpByK,EACAlU,GAEA,MAAMD,EAAM6V,OAAO1B,GAEbpE,SADWgH,gBACHxH,YAAYsH,GAAmB,aACvC3H,EAAca,EAAGb,YAAY2H,IAC7BgB,QAAkB3I,EAAY3F,IAAIvJ,GAQxC,aAPMkP,EAAY4I,IAAI7X,EAAOD,SACvB+P,EAAGI,KAEJ0H,GAAYA,EAAS1C,MAAQlV,EAAMkV,KACtCc,WAAW9B,EAAWlU,EAAMkV,KAGvBlV,CACT,CAGO4R,eAAekG,OAAO5D,GAC3B,MAAMnU,EAAM6V,OAAO1B,GAEbpE,SADWgH,gBACHxH,YAAYsH,GAAmB,mBACvC9G,EAAGb,YAAY2H,IAAmBhN,OAAO7J,SACzC+P,EAAGI,IACX,CAQO0B,eAAemG,OACpB7D,EACA8D,GAEA,MAAMjY,EAAM6V,OAAO1B,GAEbpE,SADWgH,gBACHxH,YAAYsH,GAAmB,aACvC9E,EAAQhC,EAAGb,YAAY2H,IACvBgB,QAAiD9F,EAAMxI,IAC3DvJ,GAEIqR,EAAW4G,EAASJ,GAa1B,YAXiB5I,IAAboC,QACIU,EAAMlI,OAAO7J,SAEb+R,EAAM+F,IAAIzG,EAAUrR,SAEtB+P,EAAGI,MAELkB,GAAcwG,GAAYA,EAAS1C,MAAQ9D,EAAS8D,KACtDc,WAAW9B,EAAW9C,EAAS8D,KAG1B9D,CACT,CClFOQ,eAAeqG,qBACpBC,GAEA,IAAIC,EAEJ,MAAMC,QAA0BL,OAAOG,EAAchE,WAAWmE,IAC9D,MAAMD,EAwBV,SAASE,gCACPD,GAEA,MAAME,EAA2BF,GAAY,CAC3CnD,IAAKN,cACL4D,mBAA6C,GAG/C,OAAOC,qBAAqBF,EAC9B,CAjC8BD,CAAgCD,GACpDK,EAyCV,SAASC,+BACPT,EACAE,GAEA,GAAwC,IAApCA,EAAkBI,mBAAkD,CACtE,IAAKI,UAAUC,OAAQ,CAKrB,MAAO,CACLT,oBACAD,oBALmC3S,QAAQ2K,OAC3CsC,GAAcrT,OAA6B,gBAM9C,CAGD,MAAM0Z,EAA+C,CACnD5D,IAAKkD,EAAkBlD,IACvBsD,mBAA6C,EAC7CO,iBAAkBxW,KAAKD,OAEnB6V,EAkBVvG,eAAeoH,qBACbd,EACAE,GAEA,IACE,MAAMa,QCxGHrH,eAAesH,2BACpBhF,UAAEA,EAASiF,yBAAEA,IACbjE,IAAEA,IAEF,MAAMkE,EAAWzG,yBAAyBuB,GAEpCE,EAAUP,WAAWK,GAGrBmF,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFpF,EAAQC,OAAO,oBAAqBmF,EAEvC,CAED,MAAME,EAAO,CACXxE,MACAyE,YAAapH,GACbuD,MAAO5B,EAAU4B,MACjB8D,WAAYvH,IAGRtB,EAAuB,CAC3BtO,OAAQ,OACR2R,UACAsF,KAAMG,KAAKC,UAAUJ,IAGjB5G,QAAiByB,oBAAmB,IAAMwF,MAAMX,EAAUrI,KAChE,GAAI+B,EAASkH,GAAI,CACf,MAAMC,QAAkDnH,EAASU,OAOjE,MANiE,CAC/D0B,IAAK+E,EAAc/E,KAAOA,EAC1BsD,mBAA2C,EAC3CrE,aAAc8F,EAAc9F,aAC5B+F,UAAWrH,iCAAiCoH,EAAcC,WAG7D,CACC,YAAY7G,qBAAqB,sBAAuBP,EAE5D,CD2D8CoG,CACxChB,EACAE,GAEF,OAAO3O,IAAIyO,EAAchE,UAAW+E,EACrC,CAAC,MAAOrW,GAYP,MAXI8P,cAAc9P,IAAkC,MAA5BA,EAAEjE,WAAW8U,iBAG7BqE,OAAOI,EAAchE,iBAGrBzK,IAAIyO,EAAchE,UAAW,CACjCgB,IAAKkD,EAAkBlD,IACvBsD,mBAA6C,IAG3C5V,CACP,CACH,CA1CgCoW,CAC1Bd,EACAY,GAEF,MAAO,CAAEV,kBAAmBU,EAAiBX,sBAC9C,CAAM,OAC+B,IAApCC,EAAkBI,mBAEX,CACLJ,oBACAD,oBAAqBgC,yBAAyBjC,IAGzC,CAAEE,oBAEb,CA9E6BO,CACvBT,EACAE,GAGF,OADAD,EAAsBO,EAAiBP,oBAChCO,EAAiBN,iBAAiB,IAG3C,MLvCyB,KKuCrBA,EAAkBlD,IAEb,CAAEkD,wBAAyBD,GAG7B,CACLC,oBACAD,sBAEJ,CA2FAvG,eAAeuI,yBACbjC,GAMA,IAAIK,QAAiC6B,0BACnClC,EAAchE,WAEhB,KAA+B,IAAxBqE,EAAMC,0BAEL/D,MAAM,KAEZ8D,QAAc6B,0BAA0BlC,EAAchE,WAGxD,GAA4B,IAAxBqE,EAAMC,mBAAkD,CAE1D,MAAMJ,kBAAEA,EAAiBD,oBAAEA,SACnBF,qBAAqBC,GAE7B,OAAIC,GAIKC,CAEV,CAED,OAAOG,CACT,CAUA,SAAS6B,0BACPlG,GAEA,OAAO6D,OAAO7D,GAAWmE,IACvB,IAAKA,EACH,MAAM5F,GAAcrT,OAAM,0BAE5B,OAAOqZ,qBAAqBJ,EAAS,GAEzC,CAEA,SAASI,qBAAqBF,GAC5B,OAUF,SAAS8B,+BACPjC,GAEA,OACoE,IAAlEA,EAAkBI,oBAClBJ,EAAkBW,iBAAmB3G,GAAqB7P,KAAKD,KAEnE,CAjBM+X,CAA+B9B,GAC1B,CACLrD,IAAKqD,EAAMrD,IACXsD,mBAA6C,GAI1CD,CACT,CEzLO3G,eAAe0I,0BACpBpG,UAAEA,EAASiF,yBAAEA,GACbf,GAEA,MAAMgB,EAuCR,SAASmB,6BACPrG,GACAgB,IAAEA,IAEF,MAAO,GAAGvC,yBAAyBuB,MAAcgB,uBACnD,CA5CmBqF,CAA6BrG,EAAWkE,GAEnDhE,EAAUH,mBAAmBC,EAAWkE,GAGxCiB,EAAmBF,EAAyBG,aAAa,CAC7DC,UAAU,IAEZ,GAAIF,EAAkB,CACpB,MAAMG,QAAyBH,EAAiBI,sBAC5CD,GACFpF,EAAQC,OAAO,oBAAqBmF,EAEvC,CAED,MAAME,EAAO,CACXc,aAAc,CACZZ,WAAYvH,GACZyD,MAAO5B,EAAU4B,QAIf/E,EAAuB,CAC3BtO,OAAQ,OACR2R,UACAsF,KAAMG,KAAKC,UAAUJ,IAGjB5G,QAAiByB,oBAAmB,IAAMwF,MAAMX,EAAUrI,KAChE,GAAI+B,EAASkH,GAAI,CAIf,OADEnH,uCAFqDC,EAASU,OAIjE,CACC,YAAYH,qBAAqB,sBAAuBP,EAE5D,CCnCOlB,eAAe6I,iBACpBvC,EACAwC,GAAe,GAEf,IAAIC,EACJ,MAAMpC,QAAcR,OAAOG,EAAchE,WAAWmE,IAClD,IAAKuC,kBAAkBvC,GACrB,MAAM5F,GAAcrT,OAAM,kBAG5B,MAAMyb,EAAexC,EAAS6B,UAC9B,IAAKQ,GA+HT,SAASI,iBAAiBZ,GACxB,OACqD,IAAnDA,EAAUlH,gBAKd,SAAS+H,mBAAmBb,GAC1B,MAAM5X,EAAMC,KAAKD,MACjB,OACEA,EAAM4X,EAAU9G,cAChB8G,EAAU9G,aAAe8G,EAAUjH,UAAY3Q,EAAMkQ,EAEzD,CAVKuI,CAAmBb,EAExB,CApIyBY,CAAiBD,GAEpC,OAAOxC,EACF,GAA8B,IAA1BwC,EAAa7H,cAGtB,OADA2H,EA0BN/I,eAAeoJ,0BACb9C,EACAwC,GAMA,IAAInC,QAAc0C,uBAAuB/C,EAAchE,WACvD,KAAoC,IAA7BqE,EAAM2B,UAAUlH,qBAEfyB,MAAM,KAEZ8D,QAAc0C,uBAAuB/C,EAAchE,WAGrD,MAAMgG,EAAY3B,EAAM2B,UACxB,OAA2B,IAAvBA,EAAUlH,cAELyH,iBAAiBvC,EAAewC,GAEhCR,CAEX,CAjDqBc,CAA0B9C,EAAewC,GACjDrC,EACF,CAEL,IAAKO,UAAUC,OACb,MAAMpG,GAAcrT,OAAM,eAG5B,MAAM0Z,EAkIZ,SAASoC,oCACP7C,GAEA,MAAM8C,EAA2C,CAC/CnI,cAAwC,EACxCoI,YAAa7Y,KAAKD,OAEpB,OAAAvD,OAAA8G,OAAA9G,OAAA8G,OAAA,CAAA,EACKwS,GAAQ,CACX6B,UAAWiB,GAEf,CA7I8BD,CAAoC7C,GAE5D,OADAsC,EAsEN/I,eAAeyJ,yBACbnD,EACAE,GAEA,IACE,MAAM8B,QAAkBI,yBACtBpC,EACAE,GAEIkD,EACDvc,OAAA8G,OAAA9G,OAAA8G,OAAA,CAAA,EAAAuS,GACH,CAAA8B,cAGF,aADMzQ,IAAIyO,EAAchE,UAAWoH,GAC5BpB,CACR,CAAC,MAAOtX,GACP,IACE8P,cAAc9P,IACe,MAA5BA,EAAEjE,WAAW8U,YAAkD,MAA5B7Q,EAAEjE,WAAW8U,WAK5C,CACL,MAAM6H,EACDvc,OAAA8G,OAAA9G,OAAA8G,OAAA,CAAA,EAAAuS,GACH,CAAA8B,UAAW,CAAElH,cAAa,WAEtBvJ,IAAIyO,EAAchE,UAAWoH,EACpC,YAPOxD,OAAOI,EAAchE,WAQ7B,MAAMtR,CACP,CACH,CAtGqByY,CAAyBnD,EAAeY,GAChDA,CACR,KAMH,OAHkB6B,QACRA,EACLpC,EAAM2B,SAEb,CAyCA,SAASe,uBACP/G,GAEA,OAAO6D,OAAO7D,GAAWmE,IACvB,IAAKuC,kBAAkBvC,GACrB,MAAM5F,GAAcrT,OAAM,kBAI5B,OAmFJ,SAASmc,4BAA4BrB,GACnC,OACuD,IAArDA,EAAUlH,eACVkH,EAAUkB,YAAchJ,GAAqB7P,KAAKD,KAEtD,CAxFQiZ,CADiBlD,EAAS6B,WAGvBnb,OAAA8G,OAAA9G,OAAA8G,OAAA,CAAA,EAAAwS,GACH,CAAA6B,UAAW,CAAElH,cAAa,KAIvBqF,CAAQ,GAEnB,CAoCA,SAASuC,kBACPxC,GAEA,YACwBpJ,IAAtBoJ,GACgE,IAAhEA,EAAkBI,kBAEtB,CCnJO5G,eAAe4J,SACpBtD,EACAwC,GAAe,GAEf,MAAMe,EAAoBvD,QAS5BtG,eAAe8J,iCACbxD,GAEA,MAAMC,oBAAEA,SAA8BF,qBAAqBC,GAEvDC,SAEIA,CAEV,CAjBQuD,CAAiCD,GAKvC,aADwBhB,iBAAiBgB,EAAmBf,IAC3C3H,KACnB,CCWA,SAAS4I,qBAAqBC,GAC5B,OAAOnJ,GAAcrT,OAA4C,4BAAA,CAC/Dwc,aAEJ,CC3BA,MAAMC,GAAqB,gBAGrBC,cACJC,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCpF,EDfF,SAAUgI,iBAAiBF,GAC/B,IAAKA,IAAQA,EAAIG,QACf,MAAMR,qBAAqB,qBAG7B,IAAKK,EAAIld,KACP,MAAM6c,qBAAqB,YAI7B,MAAMS,EAA2C,CAC/C,YACA,SACA,SAGF,IAAK,MAAMC,KAAWD,EACpB,IAAKJ,EAAIG,QAAQE,GACf,MAAMV,qBAAqBU,GAI/B,MAAO,CACLxG,QAASmG,EAAIld,KACb8T,UAAWoJ,EAAIG,QAAQvJ,UACvBkB,OAAQkI,EAAIG,QAAQrI,OACpBgC,MAAOkG,EAAIG,QAAQrG,MAEvB,CCboBoG,CAAiBF,GASnC,MANqD,CACnDA,MACA9H,YACAiF,yBAL+BmD,aAAaN,EAAK,aAMjDO,QAAS,IAAM/W,QAAQC,UAED,EAGpB+W,gBACJT,IAEA,MAAMC,EAAMD,EAAUE,YAAY,OAAO3C,eAEnCpB,EAAgBoE,aAAaN,EAAKH,IAAoBvC,eAM5D,MAJ8D,CAC5DmD,MAAO,IC5BJ7K,eAAe6K,MAAMvE,GAC1B,MAAMuD,EAAoBvD,GACpBE,kBAAEA,EAAiBD,oBAAEA,SAA8BF,qBACvDwD,GAWF,OARItD,EACFA,EAAoBhH,MAAMzO,QAAQf,OAIlC8Y,iBAAiBgB,GAAmBtK,MAAMzO,QAAQf,OAG7CyW,EAAkBlD,GAC3B,CDaiBuH,CAAMvE,GACnBsD,SAAWd,GAA2Bc,SAAStD,EAAewC,GAEpC,GAGd,SAAAgC,wBACdC,EACE,IAAIpP,UAAUsO,GAAoBC,cAAoC,WAExEa,EACE,IAAIpP,UAtC4B,yBAwC9BiP,gBAED,WAEL,CE3CAE,GACAE,EAAgB9d,GAAMwT,IAEtBsK,EAAgB9d,GAAMwT,GAAS,uDCflBuK,GAAcvK,GAMdwK,GAAuB,wBAEvBC,GAA6B,OAI7BC,GAAsC,OAEtCC,GAAiC,OAEjCC,GAAuC,OAGvCC,GAAwC,OAGxCC,GAAsC,OAGtCC,GAA2B,+BAE3BC,GACX,qCAGWC,GAAe,cC6Bf9K,GAAgB,IAAItT,aD9BV,cCgCrBoe,GA1CqE,CACrE,gBAAkC,yCAClC,gBAAkC,qCAClC,8BACE,mDACF,6BACE,kDACF,YAAuB,2BACvB,YAAuB,2BACvB,gBAA2B,+BAC3B,aAAwB,4BACxB,iBAA4B,sCAC5B,iBACE,4EACF,qBAAuB,wBACvB,yBACE,8CACF,0BACE,gDACF,6BACE,oDACF,8BACE,uEACF,sBACE,2PC3CSC,GAAgB,IzByGhB,MAAAC,OAOX,WAAAjf,CAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAAS6e,UAAG3b,EAsBZlD,KAAW8e,YAAe1b,kBAc1BpD,KAAe+e,gBAAsB,IAzC5C,CAOD,YAAIvb,GACF,OAAOxD,KAAK6e,SACb,CAED,YAAIrb,CAASwb,GACX,KAAMA,KAAO5c,GACX,MAAM,IAAI6c,UAAU,kBAAkBD,+BAExChf,KAAK6e,UAAYG,CAClB,CAGD,WAAAE,CAAYF,GACVhf,KAAK6e,UAA2B,iBAARG,EAAmB3c,EAAkB2c,GAAOA,CACrE,CAOD,cAAIG,GACF,OAAOnf,KAAK8e,WACb,CACD,cAAIK,CAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtBjf,KAAK8e,YAAcE,CACpB,CAMD,kBAAII,GACF,OAAOpf,KAAK+e,eACb,CACD,kBAAIK,CAAeJ,GACjBhf,KAAK+e,gBAAkBC,CACxB,CAMD,KAAA1c,IAASiB,GACPvD,KAAK+e,iBAAmB/e,KAAK+e,gBAAgB/e,KAAMoC,EAASG,SAAUgB,GACtEvD,KAAK8e,YAAY9e,KAAMoC,EAASG,SAAUgB,EAC3C,CACD,GAAA8b,IAAO9b,GACLvD,KAAK+e,iBACH/e,KAAK+e,gBAAgB/e,KAAMoC,EAASK,WAAYc,GAClDvD,KAAK8e,YAAY9e,KAAMoC,EAASK,WAAYc,EAC7C,CACD,IAAAb,IAAQa,GACNvD,KAAK+e,iBAAmB/e,KAAK+e,gBAAgB/e,KAAMoC,EAASO,QAASY,GACrEvD,KAAK8e,YAAY9e,KAAMoC,EAASO,QAASY,EAC1C,CACD,IAAAX,IAAQW,GACNvD,KAAK+e,iBAAmB/e,KAAK+e,gBAAgB/e,KAAMoC,EAASS,QAASU,GACrEvD,KAAK8e,YAAY9e,KAAMoC,EAASS,QAASU,EAC1C,CACD,KAAAT,IAASS,GACPvD,KAAK+e,iBAAmB/e,KAAK+e,gBAAgB/e,KAAMoC,EAASW,SAAUQ,GACtEvD,KAAK8e,YAAY9e,KAAMoC,EAASW,SAAUQ,EAC3C,GyB9LqCmb,ICgBxC,IAAIY,GACAC,GClBAC,GCAAC,GHEJd,GAAcnb,SAAWpB,EAASO,KC8BrB,MAAA+c,IAaX,WAAA/f,CAAqBggB,GACnB,GADmB3f,KAAM2f,OAANA,GACdA,EACH,MAAM/L,GAAcrT,OAAM,aAE5BP,KAAKkE,YAAcyb,EAAOzb,YAC1BlE,KAAKyG,oBAAsBkZ,EAAOlZ,oBAClCzG,KAAK4f,eAAiBD,EAAOE,SAC7B7f,KAAK+Z,UAAY4F,EAAO5F,UACxB/Z,KAAKsE,SAAWqb,EAAOrb,SACnBtE,KAAK+Z,WAAa/Z,KAAK+Z,UAAU+F,gBAGnC9f,KAAK+f,aAAeJ,EAAOI,cAEzBJ,EAAOK,aAAeL,EAAOK,YAAYC,oBAC3CjgB,KAAKigB,kBAAoBN,EAAOK,YAAYC,mBAE9CjgB,KAAKkgB,MAAQC,GACbngB,KAAKogB,MAAQC,GACbrgB,KAAKsgB,MAAQC,CACd,CAED,MAAAC,GAEE,OAAOxgB,KAAK4f,eAAea,KAAKC,MAAM,KAAK,EAC5C,CAED,IAAAC,CAAK1gB,GACED,KAAKkE,aAAgBlE,KAAKkE,YAAYyc,MAG3C3gB,KAAKkE,YAAYyc,KAAK1gB,EACvB,CAED,OAAA2gB,CAAQC,EAAqBC,EAAeC,GACrC/gB,KAAKkE,aAAgBlE,KAAKkE,YAAY0c,SAG3C5gB,KAAKkE,YAAY0c,QAAQC,EAAaC,EAAOC,EAC9C,CAED,gBAAA5c,CAAiB4B,GACf,OAAK/F,KAAKkE,aAAgBlE,KAAKkE,YAAYC,iBAGpCnE,KAAKkE,YAAYC,iBAAiB4B,GAFhC,EAGV,CAED,gBAAAib,CAAiB/gB,GACf,OAAKD,KAAKkE,aAAgBlE,KAAKkE,YAAY8c,iBAGpChhB,KAAKkE,YAAY8c,iBAAiB/gB,GAFhC,EAGV,CAED,aAAAghB,GAEE,OACEjhB,KAAKkE,cACJlE,KAAKkE,YAAYgd,YAAclhB,KAAKkE,YAAYid,OAAOC,gBAE3D,CAED,qBAAAC,GACE,OAAKnG,OAAUvU,kBG8GH2a,oBACd,QAAyB,oBAAdvH,YAA8BA,UAAU+F,cAIrD,CHnH+BwB,cG8DfC,uBACd,IACE,MAA4B,iBAAdhJ,SACf,CAAC,MAAOxU,GACP,OAAO,CACR,CACH,CH7DSwd,KACH5C,GAAcjc,KAAK,kDACZ,IARPic,GAAcjc,KACZ,2GAEK,EAQV,CAED,aAAA8e,CACEhX,EACA8E,GAEA,IAAKtP,KAAKyG,oBACR,OAEe,IAAIzG,KAAKyG,qBAAoBgb,IAC5C,IAAK,MAAM/H,KAAS+H,EAAK3a,aAEvBwI,EAASoK,EACV,IAIM3S,QAAQ,CAAE2a,WAAY,CAAClX,IACjC,CAED,kBAAOmX,GAIL,YAHoBxR,IAAhBmP,KACFA,GAAc,IAAII,IAAIH,KAEjBD,EACR,ECnIa,SAAAsC,SACd,OAAOpC,EACT,CGjBgB,SAAAqC,aAAaC,EAAeC,GAC1C,MAAMC,EAAWF,EAAM3c,OAAS4c,EAAM5c,OACtC,GAAI6c,EAAW,GAAKA,EAAW,EAC7B,MAAMpO,GAAcrT,OAAM,+BAG5B,MAAM0hB,EAAc,GACpB,IAAK,IAAItd,EAAI,EAAGA,EAAImd,EAAM3c,OAAQR,IAChCsd,EAAYzZ,KAAKsZ,EAAMI,OAAOvd,IAC1Bod,EAAM5c,OAASR,GACjBsd,EAAYzZ,KAAKuZ,EAAMG,OAAOvd,IAIlC,OAAOsd,EAAYE,KAAK,GAC1B,CFba,MAAAC,gBAAb,WAAAziB,GAEEK,KAAsBqiB,wBAAG,EAGzBriB,KAAqBsiB,uBAAG,EAGxBtiB,KAAcuiB,gBAAG,EAEjBviB,KAAkBwiB,mBAAG,EACrBxiB,KAA2ByiB,4BAAG,EAG9BziB,KAAc0iB,eACZ,oEAGF1iB,KAAA2iB,uBAAyBd,aACvB,mCACA,mCAGF7hB,KAAA4iB,aAAef,aAAa,uBAAwB,uBAGpD7hB,KAAS6iB,UAAG,IAGZ7iB,KAAqB8iB,uBAAG,EACxB9iB,KAAuB+iB,yBAAG,EAG1B/iB,KAAgBgjB,iBAAG,EAYpB,CAVC,qBAAAC,GACE,OAAOjjB,KAAK2iB,uBAAuBxc,OAAO,QAASnG,KAAK4iB,aACzD,CAED,kBAAOjB,GAIL,YAHgCxR,IAA5BsP,KACFA,GAA0B,IAAI2C,iBAEzB3C,EACR,EGtCH,IAAYyD,IAAZ,SAAYA,GACVA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,OAAA,GAAA,QACD,CAJD,CAAYA,KAAAA,GAIX,CAAA,IAsCD,MAAMC,GAA8B,CAAC,YAAa,UAAW,OACvDC,GAAyB,IAAIC,OAAO,kBAI1B,SAAAC,yBACd,MAAMvJ,EAAY2F,IAAIiC,cAAc5H,UACpC,OAAIA,aAAS,EAATA,EAAWwJ,eACTxJ,EAAUwJ,cAAcC,WACY,EAEE,EAGH,CAE3C,CAEgB,SAAAC,qBAGd,OAFiB/D,IAAIiC,cAAcrd,SACFgD,iBAE/B,IAAK,UACH,OAAO4b,GAAgBQ,QACzB,IAAK,SACH,OAAOR,GAAgBS,OACzB,QACE,OAAOT,GAAgBU,QAE7B,CAEgB,SAAAC,6BACd,MACMC,EADYpE,IAAIiC,cAAc5H,UAC+BgK,WAGnE,OADED,GAAuBA,EAAoBE,eAE3C,IAAK,UACH,OAAkD,EACpD,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,IAAK,KACH,OAA6C,EAC/C,QACE,OAAuC,EAE7C,CCjGM,SAAUC,SAASC,SACvB,MAAMjN,EAA2B,QAAnBH,EAAAoN,EAAY5G,eAAO,IAAAxG,OAAA,EAAAA,EAAEG,MACnC,IAAKA,EACH,MAAMrD,GAAcrT,OAAM,aAE5B,OAAO0W,CACT,CCKA,MAAMkN,GAA4B,QAa5BC,GAAmC,CACvC7B,gBAAgB,GAqBZ8B,GAAkB,8BAER,SAAAC,UACdC,EACA/E,GAEA,MAAMgF,EAeR,SAASC,kBACP,MAAM1E,EAAeL,IAAIiC,cAAc5B,aACvC,IAAKA,EACH,OAEF,MAAM2E,EAAe3E,EAAa4E,QAAQlG,IAC1C,IAAKiG,IA4IP,SAASE,YAAYC,GACnB,OAAOvQ,OAAOuQ,GAAUnhB,KAAKD,KAC/B,CA9IwBmhB,CAAYF,GAChC,OAGF,MAAMI,EAAoB/E,EAAa4E,QAAQnG,IAC/C,IAAKsG,EACH,OAEF,IAEE,OAD6C9J,KAAK+J,MAAMD,EAEzD,CAAC,MAAAhO,GACA,MACD,CACH,CAnCiB2N,GACf,OAAID,GACFQ,cAAcR,GACP7d,QAAQC,WAqDnB,SAASqe,gBACPV,EACA/E,GAGA,ON/FI,SAAU0F,oBACdC,GAEA,MAAMC,EAAmBD,EAAqBxI,WAK9C,OAHAyI,EAAiBve,MAAMwe,IAAD,IAGfD,CACT,CMsFSF,CAAoBX,EAAsBlL,eAC9CxS,MAAKwU,IACJ,MAAMtH,ED3GN,SAAUuR,aAAapB,SAC3B,MAAMnQ,EAA+B,QAAnB+C,EAAAoN,EAAY5G,eAAO,IAAAxG,OAAA,EAAAA,EAAE/C,UACvC,IAAKA,EACH,MAAMH,GAAcrT,OAAM,iBAE5B,OAAOwT,CACT,CCqGwBuR,CAAaf,EAAsBpH,KAC/ClI,EDpGN,SAAUsQ,UAAUrB,SACxB,MAAMjP,EAA4B,QAAnB6B,EAAAoN,EAAY5G,eAAO,IAAAxG,OAAA,EAAAA,EAAE7B,OACpC,IAAKA,EACH,MAAMrB,GAAcrT,OAAM,cAE5B,OAAO0U,CACT,CC8FqBsQ,CAAUhB,EAAsBpH,KAEzCjL,EAAU,IAAIsT,QADG,2DAA2DzR,mCAA2CkB,IACjF,CAC1CrR,OAAQ,OACR2R,QAAS,CAAEkQ,cAAe,GAAGpB,MAAmBhJ,KAEhDR,KAAMG,KAAKC,UAAU,CACnByK,gBAAiBlG,EACjBmG,sBAAuBtK,EACvBuK,OAAQ3B,SAASM,EAAsBpH,KACvC0I,YAAa7H,GACb8H,YAAa3B,OAIjB,OAAOjJ,MAAMhJ,GAASrL,MAAKoN,IACzB,GAAIA,EAASkH,GACX,OAAOlH,EAASU,OAGlB,MAAMf,GAAcrT,OAAM,qBAAqB,GAC/C,IAEH+R,OAAM,KACLqM,GAAcjc,KAAKqjB,GACH,GAEtB,CArFSd,CAAgBV,EAAuB/E,GAC3C3Y,KAAKme,eACLne,MACC2d,GA4BN,SAASwB,YAAYxB,GACnB,MAAMzE,EAAeL,IAAIiC,cAAc5B,aACvC,IAAKyE,IAAWzE,EACd,OAGFA,EAAakG,QAAQzH,GAA0BxD,KAAKC,UAAUuJ,IAC9DzE,EAAakG,QACXxH,GACArd,OACEsC,KAAKD,MAC8C,GAAjD2e,gBAAgBT,cAAcqB,iBAAwB,GAAK,KAGnE,CA1CgBgD,CAAYxB,KAEtB,QAEN,CAwCA,MAAMuB,GACJ,mDA4CF,SAASf,cACPR,GAEA,IAAKA,EACH,OAAOA,EAET,MAAM/E,EAA0B2C,gBAAgBT,cAC1Czb,EAAUse,EAAOte,SAAW,GAqDlC,YApD4BiK,IAAxBjK,EAAQggB,YAGVzG,EAAwB8C,eACU,SAAhCnhB,OAAO8E,EAAQggB,aAIjBzG,EAAwB8C,eAAiB6B,GAAgB7B,eAEvDrc,EAAQigB,eACV1G,EAAwBoD,UAAYvO,OAAOpO,EAAQigB,gBAC1C/B,GAAgBvB,YACzBpD,EAAwBoD,UAAYuB,GAAgBvB,WAGlD3c,EAAQkgB,qBACV3G,EAAwBiD,eAAiBxc,EAAQkgB,qBACxChC,GAAgB1B,iBACzBjD,EAAwBiD,eAAiB0B,GAAgB1B,gBAIvDxc,EAAQmgB,sBACV5G,EAAwBmD,aAAe1c,EAAQmgB,sBACtCjC,GAAgBxB,eACzBnD,EAAwBmD,aAAewB,GAAgBxB,mBAGJzS,IAAjDjK,EAAQogB,qCACV7G,EAAwBgD,4BAA8BnO,OACpDpO,EAAQogB,2CAE+CnW,IAAhDiU,GAAgB3B,8BACzBhD,EAAwBgD,4BACtB2B,GAAgB3B,kCAEuBtS,IAAvCjK,EAAQqgB,2BACV9G,EAAwB+C,mBAAqBlO,OAC3CpO,EAAQqgB,iCAEsCpW,IAAvCiU,GAAgB5B,qBACzB/C,EAAwB+C,mBACtB4B,GAAgB5B,oBAGpB/C,EAAwBqD,sBAAwB0D,uBAC9C/G,EAAwB+C,oBAE1B/C,EAAwBsD,wBAA0ByD,uBAChD/G,EAAwBgD,6BAEnB+B,CACT,CAMA,SAASgC,uBAAuBC,GAC9B,OAAOrgB,KAAKE,UAAYmgB,CAC1B,CCnNA,IAEIC,GAFAC,GAA2D,EAIzD,SAAUC,yBACdrC,GAOA,OALAoC,GAAkE,EAElED,GACEA,IASJ,SAASG,eACPtC,GAEA,OAaF,SAASuC,2BACP,MAAMxiB,EAAWob,IAAIiC,cAAcrd,SACnC,OAAO,IAAIqC,SAAQC,IACjB,GAAItC,GAAoC,aAAxBA,EAASC,WAA2B,CAClD,MAAMwiB,QAAU,KACc,aAAxBziB,EAASC,aACXD,EAASuD,oBAAoB,mBAAoBkf,SACjDngB,IACD,EAEHtC,EAASiB,iBAAiB,mBAAoBwhB,QAC/C,MACCngB,GACD,GAEL,CA5BSkgB,GACJjgB,MAAK,IP7BJ,SAAUmgB,cACd7B,GAEA,MAAM8B,EAAa9B,EAAqBvH,QAKxC,OAHAqJ,EAAWpgB,MAAMqgB,IACf1H,GAAM0H,CAAM,IAEPD,CACT,COoBgBD,CAAczC,EAAsBlL,iBAC/CxS,MAAK2Y,GAAO8E,UAAUC,EAAuB/E,KAC7C3Y,MACC,IAAMsgB,+BACN,IAAMA,8BAEZ,CAnB6BN,CAAetC,GAEnCmC,EACT,CAuCA,SAASS,6BACPR,GAAwD,CAC1D,CCxDA,ICoEIS,GDpEAC,GAF4B,EA6B5BC,GAAsB,GAEtBC,IAA4B,EAiBhC,SAASC,aAAaC,GACpB1f,YAAW,KAELsf,IAAkB,IAIlBC,GAAMniB,OAAS,GACjBuiB,sBAEFF,aA7D6B,KA6DS,GACrCC,EACL,CAEA,SAASC,sBAIP,MAAMC,EAASL,GAAMxc,OAAO,EAnEM,KAuE5B8c,EAAmBD,EAAOzb,KAAI2b,IAAQ,CAC1CC,6BAA8BD,EAAIhoB,QAClCkoB,cAAe3mB,OAAOymB,EAAIG,gBA4B9B,SAASC,iBAAiBtnB,GACxB,MAAMunB,EACJ9F,gBAAgBT,cAAcsB,wBAC1BpI,EAAOG,KAAKC,UAAUta,GAE5B,OAAOoZ,UAAUoO,YAAcpO,UAAUoO,WAAWD,EAAoBrN,GACpElU,QAAQC,UACRsU,MAAMgN,EAAoB,CACxBtkB,OAAQ,OACRiX,OACAuN,WAAW,IACVvhB,MACT,EA1BEohB,CAXsC,CACpCI,gBAAiBjnB,OAAOsC,KAAKD,OAC7B6kB,YAAa,CACXC,YAAa,EACbC,eAAgB,CAAE,GAEpBC,WAAYrG,gBAAgBT,cAAckB,UAC1C+E,cAKC/gB,MAAK,KACJwgB,GAxF0B,CAwFc,IAEzC/U,OAAM,KAGLgV,GAAQ,IAAIK,KAAWL,IACvBD,KACA1I,GAAcjc,KAAK,eAAe2kB,OAClCG,aAnG2B,IAmGW,GAE5C,UAyBgBkB,iBAEdC,GAEA,MAAO,IAAIplB,MAbb,SAASqlB,WAAWf,GAClB,IAAKA,EAAIG,YAAcH,EAAIhoB,QACzB,MAAM+T,GAAcrT,OAAM,kBAG5B+mB,GAAQ,IAAIA,GAAOO,EACrB,CASIe,CAAW,CACT/oB,QAFc8oB,KAAcplB,GAG5BykB,UAAWtkB,KAAKD,OAChB,CAEN,CAMgB,SAAAolB,oBACd,KAAOvB,GAAMniB,OAAS,GACpBuiB,qBAEJ,CCvEA,SAASoB,QACPC,EACAC,GAEK5B,KACHA,GAAS,CACP6B,KAAMP,iBAAiBC,YACvBO,MAAOL,oBAGXzB,GAAO6B,KAAKF,EAAUC,EACxB,CAEM,SAAUG,SAASC,GACvB,MAAMC,EAAkBjH,gBAAgBT,eAEnC0H,EAAgBhH,wBAA0B+G,EAAME,SAIhDD,EAAgB/G,uBAA0B8G,EAAME,SAIhD5J,IAAIiC,cAAcN,2BF9ET,SAAAkI,oBACd,OAAiE,IAA1D5C,EACT,CEgFM4C,GAKF3C,yBAAyBwC,EAAM7E,uBAAuB1d,MACpD,IAAM2iB,aAAaJ,KACnB,IAAMI,aAAaJ,KANrBI,aAAaJ,GASjB,CAQA,SAASI,aAAaJ,GACpB,IAAKxH,SACH,OAGF,MAAMyH,EAAkBjH,gBAAgBT,cAErC0H,EAAgB9G,gBAChB8G,EAAgBvG,uBAKnBgG,QAAQM,EAAK,EACf,CAkCA,SAAST,WACPI,EACAC,GAEA,OAAgD,IAA5CA,EAMN,SAASS,wBAAwBC,GAC/B,MAAMC,EAA6C,CACjDxb,IAAKub,EAAevb,IACpByb,YAAaF,EAAeG,YAAc,EAC1CC,mBAAoB,IACpBC,uBAAwBL,EAAeM,qBACvCC,qBAAsBP,EAAeQ,YACrCC,8BAA+BT,EAAeU,0BAC9CC,8BAA+BX,EAAeY,2BAE1CC,EAA6B,CACjCC,iBAAkBC,mBAChBf,EAAenF,sBAAsBpH,KAEvCuN,uBAAwBf,GAE1B,OAAO3O,KAAKC,UAAUsP,EACxB,CAtBWd,CAAwBV,GAwBnC,SAAS4B,eAAevB,GACtB,MAAMwB,EAA2B,CAC/B3qB,KAAMmpB,EAAMnpB,KACZ4qB,QAASzB,EAAME,OACfW,qBAAsBb,EAAMc,YAC5BY,YAAa1B,EAAM2B,YAGsB,IAAvC7qB,OAAOwB,KAAK0nB,EAAM4B,UAAU7lB,SAC9BylB,EAAYI,SAAW5B,EAAM4B,UAE/B,MAAMC,EAAmB7B,EAAM8B,gBACc,IAAzChrB,OAAOwB,KAAKupB,GAAkB9lB,SAChCylB,EAAYO,kBAAoBF,GAGlC,MAAMV,EAA2B,CAC/BC,iBAAkBC,mBAAmBrB,EAAM7E,sBAAsBpH,KACjEiO,aAAcR,GAEhB,OAAO5P,KAAKC,UAAUsP,EACxB,CA3CSI,CAAe5B,EACxB,CA4CA,SAAS0B,mBAAmBvG,GAC1B,MAAO,CACLmH,cAAepH,SAASC,GACxBwB,gBAAiB9D,SACjB0J,aAAc,CACZxF,YAAa9H,GACbuN,SAAU7L,IAAIiC,cAAcnB,SAC5BgL,sBAAuBlI,yBACvBmI,iBAAkBhI,qBAClBiI,0BAA2B7H,8BAE7B8H,0BAA2B,EAE/B,CC9MgB,SAAAC,0BACdrH,EACA7K,GAEA,MAAMmS,EAAmBnS,EACzB,IAAKmS,QAAuD1b,IAAnC0b,EAAiBznB,cACxC,OAEF,MAAM8c,EAAaxB,IAAIiC,cAAcV,gBAC/BiJ,EAAc9jB,KAAKC,MACqB,KAA3CwlB,EAAiBvjB,UAAY4Y,IAE1BkJ,EAA4ByB,EAAiBznB,cAC/CgC,KAAKC,MAC6D,KAA/DwlB,EAAiBznB,cAAgBynB,EAAiBvjB,iBAErD6H,EACEma,EAA4BlkB,KAAKC,MACyB,KAA7DwlB,EAAiBxd,YAAcwd,EAAiBvjB,aD2F/C,SAAUwjB,kBAAkBpC,GAChC,MAAML,EAAkBjH,gBAAgBT,cAExC,IAAK0H,EAAgBhH,uBACnB,OAKF,MAAM0J,EAAoBrC,EAAevb,IAInC6d,EAAiB3C,EAAgB3G,eAAehC,MAAM,KAAK,GAC3DuL,EAAgB5C,EAAgB1G,uBAAuBjC,MAAM,KAAK,GAEtEqL,IAAsBC,GACtBD,IAAsBE,GAMrB5C,EAAgB9G,gBAChB8G,EAAgBtG,yBAKnB+F,QAAQY,EAAc,EACxB,CC5GEoC,CATuC,CACrCvH,wBACApW,IAHU0d,EAAiB5rB,MAAQ4rB,EAAiB5rB,KAAKygB,MAAM,KAAK,GAIpEsJ,qBAAsB6B,EAAiBK,aACvChC,cACAE,4BACAE,6BAIJ,CCtDA,MAEM6B,GAAa,CfDqB,MeGtChO,GACAC,GACAC,GACAE,GACAD,ICiBW,MAAA8N,MAoBX,WAAAzsB,CACW4kB,EACAtkB,EACAqpB,GAAS,EAClB+C,GAHSrsB,KAAqBukB,sBAArBA,EACAvkB,KAAIC,KAAJA,EACAD,KAAMspB,OAANA,EAtBHtpB,KAAAssB,MAA6C,EAG7CtsB,KAAgBirB,iBAA8B,GACtDjrB,KAAQgrB,SAAsC,GACtChrB,KAAAusB,IAAM7M,IAAIiC,cACV3hB,KAAAwsB,SAAWpmB,KAAKC,MAAsB,IAAhBD,KAAKE,UAmB5BtG,KAAKspB,SACRtpB,KAAKysB,eAAiB,uBAA8BzsB,KAAKwsB,YAAYxsB,KAAKC,OAC1ED,KAAK0sB,cAAgB,sBAA6B1sB,KAAKwsB,YAAYxsB,KAAKC,OACxED,KAAK2sB,aACHN,GACA,GAAGpO,MAAwBje,KAAKwsB,YAAYxsB,KAAKC,OAE/CosB,GAGFrsB,KAAK4sB,wBAGV,CAKD,KAAAC,GACE,GAAc,IAAV7sB,KAAKssB,MACP,MAAM1Y,GAAcrT,OAAuC,gBAAA,CACzDusB,UAAW9sB,KAAKC,OAGpBD,KAAKusB,IAAI5L,KAAK3gB,KAAKysB,gBACnBzsB,KAAKssB,MAAK,CACX,CAMD,IAAAS,GACE,GAAc,IAAV/sB,KAAKssB,MACP,MAAM1Y,GAAcrT,OAAuC,gBAAA,CACzDusB,UAAW9sB,KAAKC,OAGpBD,KAAKssB,MAAK,EACVtsB,KAAKusB,IAAI5L,KAAK3gB,KAAK0sB,eACnB1sB,KAAKusB,IAAI3L,QACP5gB,KAAK2sB,aACL3sB,KAAKysB,eACLzsB,KAAK0sB,eAEP1sB,KAAK4sB,wBACLzD,SAASnpB,KACV,CASD,MAAAgtB,CACE1kB,EACAoC,EACA4S,GAKA,GAAIhV,GAAa,EACf,MAAMsL,GAAcrT,OAA+C,8BAAA,CACjEusB,UAAW9sB,KAAKC,OAGpB,GAAIyK,GAAY,EACd,MAAMkJ,GAAcrT,OAA6C,6BAAA,CAC/DusB,UAAW9sB,KAAKC,OASpB,GALAD,KAAK+qB,WAAa3kB,KAAKC,MAAiB,IAAXqE,GAC7B1K,KAAKkqB,YAAc9jB,KAAKC,MAAkB,IAAZiC,GAC1BgV,GAAWA,EAAQ2P,aACrBjtB,KAAKirB,iBAAgB/qB,OAAA8G,OAAA,CAAA,EAAQsW,EAAQ2P,aAEnC3P,GAAWA,EAAQ4P,QACrB,IAAK,MAAMC,KAAcjtB,OAAOwB,KAAK4b,EAAQ4P,SACtCE,MAAM9Y,OAAOgJ,EAAQ4P,QAAQC,OAChCntB,KAAKgrB,SAASmC,GAAc/mB,KAAKC,MAC/BiO,OAAOgJ,EAAQ4P,QAAQC,MAK/BhE,SAASnpB,KACV,CASD,eAAAqtB,CAAgBC,EAAiBC,EAAe,QACfpd,IAA3BnQ,KAAKgrB,SAASsC,GAChBttB,KAAKwtB,UAAUF,EAASC,GAExBvtB,KAAKwtB,UAAUF,EAASttB,KAAKgrB,SAASsC,GAAWC,EAEpD,CAQD,SAAAC,CAAUF,EAAiBC,GACzB,IDtJY,SAAAE,kBAAkBxtB,EAAc6sB,GAC9C,QAAoB,IAAhB7sB,EAAKkF,QAAgBlF,EAAKkF,OAhBD,OAoB1B2nB,GACCA,EAAU7f,WAAWiR,KACrBiO,GAAWuB,QAAQztB,IAAS,IAC7BA,EAAKgN,WAtBmB,KAwB7B,CC4IQwgB,CAAkBH,EAASttB,KAAKC,MAGlC,MAAM2T,GAAcrT,OAA6C,6BAAA,CAC/DotB,iBAAkBL,IAHpBttB,KAAKgrB,SAASsC,GDrId,SAAUM,4BAA4BC,GAC1C,MAAMC,EAAyB1nB,KAAKC,MAAMwnB,GAM1C,OALIC,EAAiBD,GACnBlP,GAAcjc,KACZ,6DAA6DorB,MAG1DA,CACT,CC6H+BF,CAA4BL,QAAAA,EAAgB,EAMxE,CAOD,SAAAQ,CAAUT,GACR,OAAOttB,KAAKgrB,SAASsC,IAAY,CAClC,CAOD,YAAAU,CAAaC,EAAc9sB,GACzB,MAAM+sB,ERlGJ,SAAUC,2BAA2BluB,GACzC,QAAoB,IAAhBA,EAAKkF,QAAgBlF,EAAKkF,OAjDE,OAoDFge,GAA4BrR,MAAKsc,GAC7DnuB,EAAKgN,WAAWmhB,QAEiBnuB,EAAKouB,MAAMjL,IAChD,CQ0FwB+K,CAA2BF,GACzCK,ERzFJ,SAAUC,4BAA4BptB,GAC1C,OAAwB,IAAjBA,EAAMgE,QAAgBhE,EAAMgE,QA1DF,GA2DnC,CQuFyBopB,CAA4BptB,GACjD,GAAI+sB,GAAeI,EACjBtuB,KAAKirB,iBAAiBgD,GAAQ9sB,MADhC,CAKA,IAAK+sB,EACH,MAAMta,GAAcrT,OAAyC,yBAAA,CAC3DiuB,cAAeP,IAGnB,IAAKK,EACH,MAAM1a,GAAcrT,OAA0C,0BAAA,CAC5DkuB,eAAgBttB,GATnB,CAYF,CAMD,YAAAutB,CAAaT,GACX,OAAOjuB,KAAKirB,iBAAiBgD,EAC9B,CAED,eAAAU,CAAgBV,QACsB9d,IAAhCnQ,KAAKirB,iBAAiBgD,WAGnBjuB,KAAKirB,iBAAiBgD,EAC9B,CAED,aAAA/C,GACE,OAAYhrB,OAAA8G,OAAA,CAAA,EAAAhH,KAAKirB,iBAClB,CAEO,YAAA2D,CAAatmB,GACnBtI,KAAKkqB,YAAc5hB,CACpB,CAEO,WAAAumB,CAAYnkB,GAClB1K,KAAK+qB,WAAargB,CACnB,CAMO,qBAAAkiB,GACN,MAAMkC,EAAqB9uB,KAAKusB,IAAIvL,iBAAiBhhB,KAAK2sB,cACpDoC,EAAmBD,GAAsBA,EAAmB,GAC9DC,IACF/uB,KAAK+qB,WAAa3kB,KAAKC,MAAkC,IAA5B0oB,EAAiBrkB,UAC9C1K,KAAKkqB,YAAc9jB,KAAKC,MACoC,KAAzD0oB,EAAiBzmB,UAAYtI,KAAKusB,IAAItL,kBAG5C,CAQD,qBAAO+N,CACLzK,EACA0K,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAQ3P,IAAIiC,cAAcnB,SAChC,IAAK6O,EACH,OAEF,MAAMjG,EAAQ,IAAIgD,MAChB7H,EACArG,GAA6BmR,GAC7B,GAEIC,EAAelpB,KAAKC,MAA0C,IAApCqZ,IAAIiC,cAAcV,iBAClDmI,EAAMwF,aAAaU,GAGfL,GAAqBA,EAAkB,KACzC7F,EAAMyF,YAAYzoB,KAAKC,MAAsC,IAAhC4oB,EAAkB,GAAGvkB,WAClD0e,EAAMoE,UACJ,iBACApnB,KAAKC,MAA4C,IAAtC4oB,EAAkB,GAAGzqB,iBAElC4kB,EAAMoE,UACJ,2BACApnB,KAAKC,MAAsD,IAAhD4oB,EAAkB,GAAGM,2BAElCnG,EAAMoE,UACJ,eACApnB,KAAKC,MAA0C,IAApC4oB,EAAkB,GAAGO,gBAMpC,GAAIN,EAAc,CAChB,MAAMO,EAAaP,EAAalmB,MAC9B0mB,GAJgB,gBAIDA,EAAYzvB,OAEzBwvB,GAAcA,EAAWnnB,WAC3B8gB,EAAMoE,UhB1S0B,MgB4S9BpnB,KAAKC,MAA6B,IAAvBopB,EAAWnnB,YAG1B,MAAMqnB,EAAuBT,EAAalmB,MACxC0mB,GAZ2B,2BAYZA,EAAYzvB,OAEzB0vB,GAAwBA,EAAqBrnB,WAC/C8gB,EAAMoE,UACJrP,GACA/X,KAAKC,MAAuC,IAAjCspB,EAAqBrnB,YAIhC8mB,GACFhG,EAAMoE,UACJpP,GACAhY,KAAKC,MAAwB,IAAlB+oB,GAGhB,CAEDpvB,KAAK4vB,kBACHxG,EACA/K,GhB5TiD,cgB8TjD8Q,EAAgBU,KAElB7vB,KAAK4vB,kBACHxG,EACA7K,GhB5TgD,yBgB8ThD4Q,EAAgBW,KAElB9vB,KAAK4vB,kBACHxG,EACA9K,GhBrUkD,wBgBuUlD6Q,EAAgBY,KAKlB5G,SAASC,GH1OG,SAAA4G,YACV5I,IACFA,GAAO8B,OAEX,CGuOI8G,EACD,CAED,wBAAOJ,CACLxG,EACA6G,EACAC,EACAC,GAEIA,IACF/G,EAAMoE,UAAUyC,EAAW7pB,KAAKC,MAAqB,IAAf8pB,EAAOhvB,QACzCgvB,EAAOC,oBACThH,EAAM4E,aAAakC,EAAcC,EAAOC,oBAG7C,CAED,4BAAOC,CACL9L,EACA1D,GAQAsI,SANc,IAAIiD,MAChB7H,EACA1D,GACA,EACAA,GAGH,EChXH,IAEIuO,GAFAD,GAAmC,CAAA,EACnCmB,IAA6B,EAG3B,SAAUC,kBACdhM,GAGK3C,WAML7Z,YAAW,IAkBb,SAASyoB,eAAejM,GACtB,MAAMgI,EAAM7M,IAAIiC,cAEZ,eAAgBhC,OAClB4M,EAAIjoB,SAASiB,iBAAiB,YAAY,IACxCkrB,aAAalM,KAGfgI,EAAIjoB,SAASiB,iBAAiB,UAAU,IACtCkrB,aAAalM,KAGjBgI,EAAIjoB,SAASiB,iBAAiB,oBAAoB,KACX,WAAjCgnB,EAAIjoB,SAASgD,iBACfmpB,aAAalM,EACd,IAGCgI,EAAItM,mBACNsM,EAAItM,mBAAmB5J,IACrB+Y,GAAkB/Y,CAAG,IAIzBkW,EAAIrM,OAAOiQ,UACThB,GAAgBU,IAAM,CACpB1uB,MAAOgvB,EAAOhvB,MACdivB,mBAAsC,QAAlBtZ,EAAAqZ,EAAO3mB,mBAAW,IAAAsN,OAAA,EAAAA,EAAExI,QACzC,IAEHie,EAAIjM,OAAO6P,UACThB,GAAgBW,IAAM,CACpB3uB,MAAOgvB,EAAOhvB,MACdivB,mBAAsC,QAAlBtZ,EAAAqZ,EAAO3mB,mBAAW,IAAAsN,OAAA,EAAAA,EAAE5N,mBACzC,IAEHqjB,EAAInM,OAAO+P,UACThB,GAAgBY,IAAM,CACpB5uB,MAAOgvB,EAAOhvB,MACdivB,mBAAsC,QAAlBtZ,EAAAqZ,EAAO3mB,mBAAW,IAAAsN,OAAA,EAAAA,EAAEhK,kBACzC,GAEL,CA5DmB0jB,CAAejM,IAAwB,GACxDxc,YAAW,IAIb,SAAS2oB,qBACPnM,GAEA,MAAMgI,EAAM7M,IAAIiC,cACVgP,EAAYpE,EAAIpoB,iBAAiB,YACvC,IAAK,MAAM4kB,KAAY4H,EACrB/E,0BAA0BrH,EAAuBwE,GAEnDwD,EAAI/K,cAAc,YAAY9H,GAC5BkS,0BAA0BrH,EAAuB7K,IAErD,CAfmBgX,CAAqBnM,IAAwB,GAC9Dxc,YAAW,IA4Db,SAAS6oB,sBACPrM,GAEA,MAAMgI,EAAM7M,IAAIiC,cAEVkP,EAAWtE,EAAIpoB,iBAAiB,WACtC,IAAK,MAAMyc,KAAWiQ,EACpBR,sBAAsB9L,EAAuB3D,GAG/C2L,EAAI/K,cAAc,WAAW9H,GAC3B2W,sBAAsB9L,EAAuB7K,IAEjD,CAzEmBkX,CAAsBrM,IAAwB,GACjE,CA0EA,SAAS8L,sBACP9L,EACA3D,GAEA,MAAMC,EAAcD,EAAQ3gB,KAI1B4gB,EAAYiQ,UAAU,EAAG7S,MACzBA,IAIFmO,MAAMiE,sBAAsB9L,EAAuB1D,EACrD,CAEA,SAAS4P,aAAalM,GACpB,IAAK+L,GAAmB,CACtBA,IAAoB,EACpB,MAAM/D,EAAM7M,IAAIiC,cACVsN,EAAoB1C,EAAIpoB,iBAC5B,cAEI+qB,EAAe3C,EAAIpoB,iBAAiB,SAI1C4D,YAAW,KACTqkB,MAAM4C,eACJzK,EACA0K,EACAC,EACAC,GACAC,GACD,GACA,EACJ,CACH,CCpIa,MAAA2B,sBAGX,WAAApxB,CACWwd,EACA9D,GADArZ,KAAGmd,IAAHA,EACAnd,KAAaqZ,cAAbA,EAJHrZ,KAAWgxB,aAAY,CAK3B,CAWJ,KAAAC,CAAMC,GACAlxB,KAAKgxB,mBAI+B7gB,KAApC+gB,aAAQ,EAARA,EAAU5O,yBACZtiB,KAAKsiB,sBAAwB4O,EAAS5O,4BAECnS,KAArC+gB,aAAQ,EAARA,EAAU7O,0BACZriB,KAAKqiB,uBAAyB6O,EAAS7O,wBAGrC3C,IAAIiC,cAAcN,iCZoJV8P,4BACd,OAAO,IAAIxqB,SAAQ,CAACC,EAAS0K,KAC3B,IACE,IAAI8f,GAAoB,EACxB,MAAMC,EACJ,0DACInf,EAAUjO,KAAKsU,UAAUC,KAAK6Y,GACpCnf,EAAQof,UAAY,KAClBpf,EAAQG,OAAOwF,QAEVuZ,GACHntB,KAAKsU,UAAUgZ,eAAeF,GAEhCzqB,GAAQ,EAAK,EAEfsL,EAAQsf,gBAAkB,KACxBJ,GAAW,CAAK,EAGlBlf,EAAQuf,QAAU,WAChBngB,GAAoB,QAAbwF,EAAA5E,EAAQpP,aAAK,IAAAgU,OAAA,EAAAA,EAAEjX,UAAW,GAAG,CAEvC,CAAC,MAAOiD,GACPwO,EAAOxO,EACR,IAEL,CY7KMquB,GACGtqB,MAAK6qB,IACAA,KNHE,SAAAC,wBACTpK,KACHC,aArC+B,MAsC/BD,IAAmB,EAEvB,CMDYoK,GACA/K,yBAAyB5mB,MAAM6G,MAC7B,IAAM0pB,kBAAkBvwB,QACxB,IAAMuwB,kBAAkBvwB,QAE1BA,KAAKgxB,aAAc,EACpB,IAEF1e,OAAMxP,IACL6b,GAAcjc,KAAK,0CAA0CI,IAAQ,IAGzE6b,GAAcjc,KACZ,qHAIL,CAED,0BAAI2f,CAAuBrD,GACzBoD,gBAAgBT,cAAcU,uBAAyBrD,CACxD,CACD,0BAAIqD,GACF,OAAOD,gBAAgBT,cAAcU,sBACtC,CAED,yBAAIC,CAAsBtD,GACxBoD,gBAAgBT,cAAcW,sBAAwBtD,CACvD,CACD,yBAAIsD,GACF,OAAOF,gBAAgBT,cAAcW,qBACtC,ECnCa,SAAAsP,eACdzU,EAAmB0U,KAEnB1U,EAAMjb,mBAAmBib,GAGzB,OAFiBM,aAAaN,EAAK,eACL1C,cAEhC,CAQgB,SAAAqX,sBACd3U,EACA+T,GAEA/T,EAAMjb,mBAAmBib,GACzB,MAAM4U,EAAWtU,aAAaN,EAAK,eAInC,GAAI4U,EAASC,gBAAiB,CAC5B,MAAMC,EAAmBF,EAAStX,eAElC,GAAInZ,UADoBywB,EAASG,aACFhB,QAAAA,EAAY,CAAA,GACzC,OAAOe,EAEP,MAAMre,GAAcrT,OAAM,sBAE7B,CAKD,OAHqBwxB,EAASI,WAAW,CACvC7U,QAAS4T,GAGb,CAQgB,SAAA9H,MACdllB,EACAjE,GAGA,OADAiE,EAAchC,mBAAmBgC,GAC1B,IAAIkoB,MAAMloB,EAAsCjE,EACzD,CAEA,MAAMmyB,QAA0C,CAC9ClV,GACEI,QAAS4T,MAGX,MAAM/T,EAAMD,EAAUE,YAAY,OAAO3C,eACnCpB,EAAgB6D,EACnBE,YAAY,0BACZ3C,eAEH,GAvEyB,cAuErB0C,EAAIld,KACN,MAAM2T,GAAcrT,OAAM,kBAE5B,GAAsB,oBAAXof,OACT,MAAM/L,GAAcrT,OAAM,chB2CxB,SAAU8xB,SAAS1S,GACvBJ,GAAiBI,CACnB,CgB3CE0S,CAAS1S,QACT,MAAM2S,EAAe,IAAIvB,sBAAsB5T,EAAK9D,GAGpD,OAFAiZ,EAAarB,MAAMC,GAEZoB,CAAY,GAGrB,SAASC,sBACPzU,EACE,IAAIpP,UAAU,cAAe0jB,QAA8B,WAE7DrU,EAAgB9d,GAAMwT,IAEtBsK,EAAgB9d,GAAMwT,GAAS,UACjC,CAEA8e", "preExistingComment": "firebase-performance.js.map"}