((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(Qd,Hd){try{!(function(){function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}let s=i(Qd),a=()=>{},u=function(t){var r=[];let n=0;for(let i=0;i<t.length;i++){let e=t.charCodeAt(i);e<128?r[n++]=e:(e<2048?r[n++]=e>>6|192:(55296==(64512&e)&&i+1<t.length&&56320==(64512&t.charCodeAt(i+1))?(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++i)),r[n++]=e>>18|240,r[n++]=e>>12&63|128):r[n++]=e>>12|224,r[n++]=e>>6&63|128),r[n++]=63&e|128)}return r},B={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(r,e){if(!Array.isArray(r))throw Error("encodeByteArray takes an array as a parameter");this.init_();var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let h=0;h<r.length;h+=3){var s=r[h],a=h+1<r.length,o=a?r[h+1]:0,u=h+2<r.length,l=u?r[h+2]:0;let e=(15&o)<<2|l>>6,t=63&l;u||(t=64,a)||(e=64),i.push(n[s>>2],n[(3&s)<<4|o>>4],n[e],n[t])}return i.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(u(e),t)},decodeString(r,n){if(this.HAS_NATIVE_SUPPORT&&!n)return atob(r);{var i=this.decodeStringToByteArray(r,n);var s=[];let e=0,t=0;for(;e<i.length;){var a,o,u,l=i[e++];l<128?s[t++]=String.fromCharCode(l):191<l&&l<224?(a=i[e++],s[t++]=String.fromCharCode((31&l)<<6|63&a)):239<l&&l<365?(a=((7&l)<<18|(63&i[e++])<<12|(63&i[e++])<<6|63&i[e++])-65536,s[t++]=String.fromCharCode(55296+(a>>10)),s[t++]=String.fromCharCode(56320+(1023&a))):(o=i[e++],u=i[e++],s[t++]=String.fromCharCode((15&l)<<12|(63&o)<<6|63&u))}return s.join("");return}},decodeStringToByteArray(e,t){this.init_();var r=t?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let u=0;u<e.length;){var i=r[e.charAt(u++)],s=u<e.length?r[e.charAt(u)]:0,a=++u<e.length?r[e.charAt(u)]:64,o=++u<e.length?r[e.charAt(u)]:64;if(++u,null==i||null==s||null==a||null==o)throw new q;n.push(i<<2|s>>4),64!==a&&(n.push(s<<4&240|a>>2),64!==o)&&n.push(a<<6&192|o)}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class q extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let j=function(e){var t=u(e);return B.encodeByteArray(t,!0)},z=function(e){return j(e).replace(/\./g,"")},K=function(e){try{return B.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null};function G(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}let $=()=>G().__FIREBASE_DEFAULTS__,Q=()=>{var e;return"undefined"!=typeof process&&void 0!==process.env&&(e=process.env.__FIREBASE_DEFAULTS__)?JSON.parse(e):void 0},H=()=>{if("undefined"!=typeof document){let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}var t=e&&K(e[1]);return t&&JSON.parse(t)}},W=()=>{try{return a()||$()||Q()||H()}catch(e){console.info("Unable to get __FIREBASE_DEFAULTS__ due to: "+e)}};function X(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch(e){return!1}}let Y={};let J=!1;function Z(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&X(window.location.host)&&Y[e]!==t&&!Y[e]&&!J){Y[e]=t;let l="__firebase__banner";let h=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(Y))(Y[e]?t.emulator:t.prod).push(e);return t})().prod.length;function c(e){return"__firebase__banner__"+e}function d(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;J=!0,(e=document.getElementById(l))&&e.remove()},e}function r(){var e,t,r=(e=>{let t=document.getElementById(e),r=!1;return t||((t=document.createElement("div")).setAttribute("id",e),r=!0),{created:r,element:t}})(l),n=c("text"),i=document.getElementById(n)||document.createElement("span"),s=c("learnmore"),a=document.getElementById(s)||document.createElement("a"),o=c("preprendIcon"),u=document.getElementById(o)||document.createElementNS("http://www.w3.org/2000/svg","svg");r.created&&(r=r.element,(t=r).style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",(t=a).setAttribute("id",s),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline",s=d(),t=o,(e=u).setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px",r.append(u,i,a,s),document.body.appendChild(r)),h?(i.innerText="Preview backend disconnected.",u.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(u.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,i.innerText="Preview backend running in this workspace."),i.setAttribute("id",n)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",r):r()}}function ee(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function te(){var e=null==(e=W())?void 0:e.forceEnvironment;if("node"===e)return 1;if("browser"!==e)try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){}}function re(){return!te()&&navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function ne(){return!te()&&navigator.userAgent&&(navigator.userAgent.includes("Safari")||navigator.userAgent.includes("WebKit"))&&!navigator.userAgent.includes("Chrome")}class ie extends Error{constructor(e,t,r){super(t),this.code=e,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,ie.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,se.prototype.create)}}class se{constructor(e,t,r){this.service=e,this.serviceName=t,this.errors=r}create(e,...t){var n,r=t[0]||{},i=this.service+"/"+e,s=this.errors[e],s=s?(n=r,s.replace(ae,(e,t)=>{var r=n[t];return null!=r?String(r):`<${t}?>`})):"Error",s=this.serviceName+`: ${s} (${i}).`;return new ie(i,s,r)}}let ae=/\{\$([^}]+)}/g;function oe(e,t){if(e!==t){var r,n,i=Object.keys(e),s=Object.keys(t);for(r of i){if(!s.includes(r))return!1;var a=e[r],o=t[r];if(ue(a)&&ue(o)){if(!oe(a,o))return!1}else if(a!==o)return!1}for(n of s)if(!i.includes(n))return!1}return!0}function ue(e){return null!==e&&"object"==typeof e}function _(e){return e&&e._delegate?e._delegate:e}class le{constructor(e,t,r){this.name=e,this.instanceFactory=t,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}var c;(n=c=c||{})[n.DEBUG=0]="DEBUG",n[n.VERBOSE=1]="VERBOSE",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.SILENT=5]="SILENT";let he={debug:c.DEBUG,verbose:c.VERBOSE,info:c.INFO,warn:c.WARN,error:c.ERROR,silent:c.SILENT},ce=c.INFO,de={[c.DEBUG]:"log",[c.VERBOSE]:"log",[c.INFO]:"info",[c.WARN]:"warn",[c.ERROR]:"error"},fe=(e,t,...r)=>{if(!(t<e.logLevel)){var n=(new Date).toISOString(),i=de[t];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[i](`[${n}]  ${e.name}:`,...r)}};var ge,me,pr,yr,vr,wr,_r,br,Ir,Tr,m,pe,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Er=(!(function(){var e,t,s;function r(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(){}function a(e,t,r){r=r||0;var n=Array(16);if("string"==typeof t)for(var i=0;i<16;++i)n[i]=t.charCodeAt(r++)|t.charCodeAt(r++)<<8|t.charCodeAt(r++)<<16|t.charCodeAt(r++)<<24;else for(i=0;i<16;++i)n[i]=t[r++]|t[r++]<<8|t[r++]<<16|t[r++]<<24;t=e.g[0],r=e.g[1];var i=e.g[2],s=e.g[3],a=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=(r=(i=(s=(t=r+((a=t+(s^r&(i^s))+n[0]+3614090360&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[1]+3905402710&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[2]+606105819&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[3]+3250441966&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[4]+4118548399&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[5]+1200080426&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[6]+2821735955&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[7]+4249261313&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[8]+1770035416&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[9]+2336552879&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[10]+4294925233&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[11]+2304563134&4294967295)<<22&4294967295|a>>>10))+((a=t+(s^r&(i^s))+n[12]+1804603682&4294967295)<<7&4294967295|a>>>25))+((a=s+(i^t&(r^i))+n[13]+4254626195&4294967295)<<12&4294967295|a>>>20))+((a=i+(r^s&(t^r))+n[14]+2792965006&4294967295)<<17&4294967295|a>>>15))+((a=r+(t^i&(s^t))+n[15]+1236535329&4294967295)<<22&4294967295|a>>>10))+((a=t+(i^s&(r^i))+n[1]+4129170786&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[6]+3225465664&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[11]+643717713&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[0]+3921069994&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[5]+3593408605&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[10]+38016083&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[15]+3634488961&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[4]+3889429448&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[9]+568446438&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[14]+3275163606&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[3]+4107603335&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[8]+1163531501&4294967295)<<20&4294967295|a>>>12))+((a=t+(i^s&(r^i))+n[13]+2850285829&4294967295)<<5&4294967295|a>>>27))+((a=s+(r^i&(t^r))+n[2]+4243563512&4294967295)<<9&4294967295|a>>>23))+((a=i+(t^r&(s^t))+n[7]+1735328473&4294967295)<<14&4294967295|a>>>18))+((a=r+(s^t&(i^s))+n[12]+2368359562&4294967295)<<20&4294967295|a>>>12))+((a=t+(r^i^s)+n[5]+4294588738&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[8]+2272392833&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[11]+1839030562&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[14]+4259657740&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[1]+2763975236&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[4]+1272893353&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[7]+4139469664&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[10]+3200236656&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[13]+681279174&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[0]+3936430074&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[3]+3572445317&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[6]+76029189&4294967295)<<23&4294967295|a>>>9))+((a=t+(r^i^s)+n[9]+3654602809&4294967295)<<4&4294967295|a>>>28))+((a=s+(t^r^i)+n[12]+3873151461&4294967295)<<11&4294967295|a>>>21))+((a=i+(s^t^r)+n[15]+530742520&4294967295)<<16&4294967295|a>>>16))+((a=r+(i^s^t)+n[2]+3299628645&4294967295)<<23&4294967295|a>>>9))+((a=t+(i^(r|~s))+n[0]+4096336452&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[7]+1126891415&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[14]+2878612391&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[5]+4237533241&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[12]+1700485571&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[3]+2399980690&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[10]+4293915773&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[1]+2240044497&4294967295)<<21&4294967295|a>>>11))+((a=t+(i^(r|~s))+n[8]+1873313359&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[15]+4264355552&4294967295)<<10&4294967295|a>>>22))+((a=i+(t^(s|~r))+n[6]+2734768916&4294967295)<<15&4294967295|a>>>17))+((a=r+(s^(i|~t))+n[13]+1309151649&4294967295)<<21&4294967295|a>>>11))+((s=(t=r+((a=t+(i^(r|~s))+n[4]+4149444226&4294967295)<<6&4294967295|a>>>26))+((a=s+(r^(t|~i))+n[11]+3174756917&4294967295)<<10&4294967295|a>>>22))^((i=s+((a=i+(t^(s|~r))+n[2]+718787259&4294967295)<<15&4294967295|a>>>17))|~t))+n[9]+3951481745&4294967295;e.g[0]=e.g[0]+t&4294967295,e.g[1]=e.g[1]+(i+(a<<21&4294967295|a>>>11))&4294967295,e.g[2]=e.g[2]+i&4294967295,e.g[3]=e.g[3]+s&4294967295}function l(e,t){this.h=t;for(var r=[],n=!0,i=e.length-1;0<=i;i--){var s=0|e[i];n&&s==t||(r[i]=s,n=!1)}this.g=r}t=r,s=function(){this.blockSize=-1},n.prototype=s.prototype,t.D=s.prototype,t.prototype=new n,(t.prototype.constructor=t).C=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)},r.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},r.prototype.u=function(e,t){for(var r=(t=void 0===t?e.length:t)-this.blockSize,n=this.B,i=this.h,s=0;s<t;){if(0==i)for(;s<=r;)a(this,e,s),s+=this.blockSize;if("string"==typeof e){for(;s<t;)if(n[i++]=e.charCodeAt(s++),i==this.blockSize){a(this,n),i=0;break}}else for(;s<t;)if(n[i++]=e[s++],i==this.blockSize){a(this,n),i=0;break}}this.h=i,this.o+=t},r.prototype.v=function(){var e=Array((this.h<56?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var t=1;t<e.length-8;++t)e[t]=0;for(var r=8*this.o,t=e.length-8;t<e.length;++t)e[t]=255&r,r/=256;for(this.u(e),e=Array(16),t=r=0;t<4;++t)for(var n=0;n<32;n+=8)e[r++]=this.g[t]>>>n&255;return e};var i={};function o(e){return-128<=e&&e<128?(t=e,r=function(e){return new l([0|e],e<0?-1:0)},n=i,Object.prototype.hasOwnProperty.call(n,t)?n[t]:n[t]=r(t)):new l([0|e],e<0?-1:0);var t,r,n}function h(e){if(isNaN(e)||!isFinite(e))return c;if(e<0)return m(h(-e));for(var t=[],r=1,n=0;r<=e;n++)t[n]=e/r|0,r*=4294967296;return new l(t,0)}var c=o(0),u=o(1),d=o(16777216);function f(e){if(0==e.h){for(var t=0;t<e.g.length;t++)if(0!=e.g[t])return;return 1}}function g(e){return-1==e.h}function m(e){for(var t=e.g.length,r=[],n=0;n<t;n++)r[n]=~e.g[n];return new l(r,~e.h).add(u)}function p(e,t){return e.add(m(t))}function y(e,t){for(;(65535&e[t])!=e[t];)e[t+1]+=e[t]>>>16,e[t]&=65535,t++}function v(e,t){this.g=e,this.h=t}function w(e,t){if(f(t))throw Error("division by zero");if(f(e))return new v(c,c);if(g(e))return t=w(m(e),t),new v(m(t.g),m(t.h));if(g(t))return t=w(e,m(t)),new v(m(t.g),t.h);if(30<e.g.length){if(g(e)||g(t))throw Error("slowDivide_ only works with positive integers.");for(var r=u,n=t;n.l(e)<=0;)r=_(r),n=_(n);for(var i=b(r,1),s=b(n,1),n=b(n,2),r=b(r,2);!f(n);){var a=s.add(n);a.l(e)<=0&&(i=i.add(r),s=a),n=b(n,1),r=b(r,1)}return t=p(e,i.j(t)),new v(i,t)}for(i=c;0<=e.l(t);){for(r=Math.max(1,Math.floor(e.m()/t.m())),n=(n=Math.ceil(Math.log(r)/Math.LN2))<=48?1:Math.pow(2,n-48),a=(s=h(r)).j(t);g(a)||0<a.l(e);)a=(s=h(r-=n)).j(t);f(s)&&(s=u),i=i.add(s),e=p(e,a)}return new v(i,e)}function _(e){for(var t=e.g.length+1,r=[],n=0;n<t;n++)r[n]=e.i(n)<<1|e.i(n-1)>>>31;return new l(r,e.h)}function b(e,t){var r=t>>5;t%=32;for(var n=e.g.length-r,i=[],s=0;s<n;s++)i[s]=0<t?e.i(s+r)>>>t|e.i(s+r+1)<<32-t:e.i(s+r);return new l(i,e.h)}(e=l.prototype).m=function(){if(g(this))return-m(this).m();for(var e=0,t=1,r=0;r<this.g.length;r++){var n=this.i(r);e+=(0<=n?n:4294967296+n)*t,t*=4294967296}return e},e.toString=function(e){if((e=e||10)<2||36<e)throw Error("radix out of range: "+e);if(f(this))return"0";if(g(this))return"-"+m(this).toString(e);for(var t=h(Math.pow(e,6)),r=this,n="";;){var i=w(r,t).g,s=((0<(r=p(r,i.j(t))).g.length?r.g[0]:r.h)>>>0).toString(e);if(f(r=i))return s+n;for(;s.length<6;)s="0"+s;n=s+n}},e.i=function(e){return e<0?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return g(e=p(this,e))?-1:f(e)?0:1},e.abs=function(){return g(this)?m(this):this},e.add=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0,i=0;i<=t;i++){var s=n+(65535&this.i(i))+(65535&e.i(i)),a=(s>>>16)+(this.i(i)>>>16)+(e.i(i)>>>16),n=a>>>16;r[i]=(a&=65535)<<16|(s&=65535)}return new l(r,-2147483648&r[r.length-1]?-1:0)},e.j=function(e){if(f(this)||f(e))return c;if(g(this))return g(e)?m(this).j(m(e)):m(m(this).j(e));if(g(e))return m(this.j(m(e)));if(this.l(d)<0&&e.l(d)<0)return h(this.m()*e.m());for(var t=this.g.length+e.g.length,r=[],n=0;n<2*t;n++)r[n]=0;for(n=0;n<this.g.length;n++)for(var i=0;i<e.g.length;i++){var s=this.i(n)>>>16,a=65535&this.i(n),o=e.i(i)>>>16,u=65535&e.i(i);r[2*n+2*i]+=a*u,y(r,2*n+2*i),r[2*n+2*i+1]+=s*u,y(r,2*n+2*i+1),r[2*n+2*i+1]+=a*o,y(r,2*n+2*i+1),r[2*n+2*i+2]+=s*o,y(r,2*n+2*i+2)}for(n=0;n<t;n++)r[n]=r[2*n+1]<<16|r[2*n];for(n=t;n<2*t;n++)r[n]=0;return new l(r,0)},e.A=function(e){return w(this,e).h},e.and=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)&e.i(n);return new l(r,this.h&e.h)},e.or=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)|e.i(n);return new l(r,this.h|e.h)},e.xor=function(e){for(var t=Math.max(this.g.length,e.g.length),r=[],n=0;n<t;n++)r[n]=this.i(n)^e.i(n);return new l(r,this.h^e.h)},r.prototype.digest=r.prototype.v,r.prototype.reset=r.prototype.s,r.prototype.update=r.prototype.u,me=r,l.prototype.multiply=l.prototype.j,l.prototype.modulo=l.prototype.A,l.prototype.compare=l.prototype.l,l.prototype.toNumber=l.prototype.m,l.prototype.getBits=l.prototype.i,l.fromNumber=h,l.fromString=function e(t,r){if(0==t.length)throw Error("number format error: empty string");if((r=r||10)<2||36<r)throw Error("radix out of range: "+r);if("-"==t.charAt(0))return m(e(t.substring(1),r));if(0<=t.indexOf("-"))throw Error('number format error: interior "-" character');for(var n=h(Math.pow(r,8)),i=c,s=0;s<t.length;s+=8)var a=Math.min(8,t.length-s),o=parseInt(t.substring(s,s+a),r),i=(a<8?(a=h(Math.pow(r,a)),i.j(a)):i=i.j(n)).add(h(o));return i},ge=l}).apply(void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{}),"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{});!(function(){var e,N="function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,r){return e!=Array.prototype&&e!=Object.prototype&&(e[t]=r.value),e};var k=(e=>{e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof Er&&Er];for(var t=0;t<e.length;++t){var r=e[t];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")})(this);var t="Array.prototype.values",r=function(e){return e||function(){return r=function(e,t){return t},(t=this)instanceof String&&(t+=""),n=0,i=!1,(e={next:function(){var e;return!i&&n<t.length?(e=n++,{value:r(e,t[e]),done:!1}):{done:i=!0,value:void 0}}})[Symbol.iterator]=function(){return e},e;var t,r,n,i,e}};if(r)e:{var n=k;t=t.split(".");for(var i=0;i<t.length-1;i++){var P=t[i];if(!(P in n))break e;n=n[P]}(r=r(i=n[t=t[t.length-1]]))!=i&&null!=r&&N(n,t,{configurable:!0,writable:!0,value:r})}var U=U||{},R=this||self;function B(e){var t=typeof e;return"array"==(t="object"!=t?t:e?Array.isArray(e)?"array":t:"null")||"object"==t&&"number"==typeof e.length}function l(e){var t=typeof e;return"object"==t&&null!=e||"function"==t}function q(e,t,r){return e.call.apply(e.bind,arguments)}function j(t,r,e){var n;if(t)return 2<arguments.length?(n=Array.prototype.slice.call(arguments,2),function(){var e=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(e,n),t.apply(r,e)}):function(){return t.apply(r,arguments)};throw Error()}function p(e,t,r){return(p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?q:j).apply(null,arguments)}function z(t){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function s(e,s){function t(){}t.prototype=s.prototype,e.aa=s.prototype,e.prototype=new t,(e.prototype.constructor=e).Qb=function(e,t,r){for(var n=Array(arguments.length-2),i=2;i<arguments.length;i++)n[i-2]=arguments[i];return s.prototype[t].apply(e,n)}}function K(t){var r=t.length;if(0<r){var n=Array(r);for(let e=0;e<r;e++)n[e]=t[e];return n}return[]}function G(t){for(let e=1;e<arguments.length;e++){var r=arguments[e];if(B(r)){var n=t.length||0,i=r.length||0;t.length=n+i;for(let e=0;e<i;e++)t[n+e]=r[e]}else t.push(r)}}function O(e){return/^[\s\xa0]*$/.test(e)}function a(){var e=R.navigator;return(e=e&&e.userAgent)||""}function $(e){return $[" "](e),e}$[" "]=function(){};var Q=!(-1==a().indexOf("Gecko")||-1!=a().toLowerCase().indexOf("webkit")&&-1==a().indexOf("Edge")||-1!=a().indexOf("Trident")||-1!=a().indexOf("MSIE")||-1!=a().indexOf("Edge"));function H(e,t,r){for(var n in e)t.call(r,e[n],n,e)}function W(e){var t,r={};for(t in e)r[t]=e[t];return r}let X="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Y(t){let r,n;for(let i=1;i<arguments.length;i++){for(r in n=arguments[i])t[r]=n[r];for(let e=0;e<X.length;e++)r=X[e],Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}}var J=new class{constructor(e,t){this.i=e,this.j=t,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}}(()=>new Z,e=>e.reset());class Z{constructor(){this.next=this.g=this.h=null}set(e,t){this.h=e,this.g=t,this.next=null}reset(){this.next=this.g=this.h=null}}let o,u=!1,ee=new class{constructor(){this.h=this.g=null}add(e,t){var r=J.get();r.set(e,t),this.h?this.h.next=r:this.g=r,this.h=r}},te=()=>{let e=R.Promise.resolve(void 0);o=()=>{e.then(re)}};var re=()=>{for(var e;e=(()=>{let e=ee,t=null;return e.g&&(t=e.g,e.g=e.g.next,e.g||(e.h=null),t.next=null),t})();){try{e.h.call(e.g)}catch(e){(e=>{R.setTimeout(()=>{throw e},0)})(e)}var t=J;t.j(e),t.h<100&&(t.h++,e.next=t.g,t.g=e)}u=!1};function h(){this.s=this.s,this.C=this.C}function c(e,t){this.type=e,this.g=this.target=t,this.defaultPrevented=!1}h.prototype.s=!1,h.prototype.ma=function(){this.s||(this.s=!0,this.N())},h.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},c.prototype.h=function(){this.defaultPrevented=!0};var ne=(()=>{if(!R.addEventListener||!Object.defineProperty)return!1;var e=!1,t=Object.defineProperty({},"passive",{get:function(){e=!0}});try{var r=()=>{};R.addEventListener("test",r,t),R.removeEventListener("test",r,t)}catch(e){}return e})();function d(e,t){if(c.call(this,e?e.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,e){var r=this.type=e.type,n=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:null;if(this.target=e.target||e.srcElement,this.g=t,t=e.relatedTarget){if(Q){e:{try{$(t.nodeName);var i=!0;break e}catch(e){}i=!1}i||(t=null)}}else"mouseover"==r?t=e.fromElement:"mouseout"==r&&(t=e.toElement);this.relatedTarget=t,n?(this.clientX=void 0!==n.clientX?n.clientX:n.pageX,this.clientY=void 0!==n.clientY?n.clientY:n.pageY,this.screenX=n.screenX||0,this.screenY=n.screenY||0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0),this.button=e.button,this.key=e.key||"",this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.pointerId=e.pointerId||0,this.pointerType="string"==typeof e.pointerType?e.pointerType:ie[e.pointerType]||"",this.state=e.state,(this.i=e).defaultPrevented&&d.aa.h.call(this)}}s(d,c);var ie={2:"touch",3:"pen",4:"mouse"},f=(d.prototype.h=function(){d.aa.h.call(this);var e=this.i;e.preventDefault?e.preventDefault():e.returnValue=!1},"closure_listenable_"+(1e6*Math.random()|0)),se=0;function ae(e,t,r,n,i){this.listener=e,this.proxy=null,this.src=t,this.type=r,this.capture=!!n,this.ha=i,this.key=++se,this.da=this.fa=!1}function oe(e){e.da=!0,e.listener=null,e.proxy=null,e.src=null,e.ha=null}function ue(e){this.src=e,this.g={},this.h=0}function le(e,t){var r,n,i,s=t.type;s in e.g&&(r=e.g[s],(i=0<=(n=Array.prototype.indexOf.call(r,t,void 0)))&&Array.prototype.splice.call(r,n,1),i)&&(oe(t),0==e.g[s].length)&&(delete e.g[s],e.h--)}function he(e,t,r,n){for(var i=0;i<e.length;++i){var s=e[i];if(!s.da&&s.listener==t&&s.capture==!!r&&s.ha==n)return i}return-1}ue.prototype.add=function(e,t,r,n,i){var s=e.toString(),a=((e=this.g[s])||(e=this.g[s]=[],this.h++),he(e,t,n,i));return-1<a?(t=e[a],r||(t.fa=!1)):((t=new ae(t,this.src,s,!!n,i)).fa=r,e.push(t)),t};var ce="closure_lm_"+(1e6*Math.random()|0),de={};function fe(e,t,r,n,i){if(n&&n.once)return function e(t,r,n,i,s){if(Array.isArray(r)){for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);return null}n=_e(n);return t&&t[f]?t.L(r,n,l(i)?!!i.capture:!!i,s):ge(t,r,n,!0,i,s)}(e,t,r,n,i);if(Array.isArray(t)){for(var s=0;s<t.length;s++)fe(e,t[s],r,n,i);return null}return r=_e(r),e&&e[f]?e.K(t,r,l(n)?!!n.capture:!!n,i):ge(e,t,r,!1,n,i)}function ge(e,t,r,n,i,s){if(!t)throw Error("Invalid event type");var a=l(i)?!!i.capture:!!i,o=ve(e);if(o||(e[ce]=o=new ue(e)),!(r=o.add(t,r,n,a,s)).proxy)if(n=(()=>{let r=ye;return function e(t){return r.call(e.src,e.listener,t)}})(),(r.proxy=n).src=e,n.listener=r,e.addEventListener)void 0===(i=ne?i:a)&&(i=!1),e.addEventListener(t.toString(),n,i);else if(e.attachEvent)e.attachEvent(pe(t.toString()),n);else{if(!e.addListener||!e.removeListener)throw Error("addEventListener and attachEvent are unavailable.");e.addListener(n)}return r}function me(e){var t,r,n;"number"!=typeof e&&e&&!e.da&&((t=e.src)&&t[f]?le(t.i,e):(r=e.type,n=e.proxy,t.removeEventListener?t.removeEventListener(r,n,e.capture):t.detachEvent?t.detachEvent(pe(r),n):t.addListener&&t.removeListener&&t.removeListener(n),(r=ve(t))?(le(r,e),0==r.h&&(r.src=null,t[ce]=null)):oe(e)))}function pe(e){return e in de?de[e]:de[e]="on"+e}function ye(e,t){var r,n;return e=!!e.da||(t=new d(t,this),r=e.listener,n=e.ha||e.src,e.fa&&me(e),r.call(n,t))}function ve(e){return(e=e[ce])instanceof ue?e:null}var we="__closure_events_fn_"+(1e9*Math.random()>>>0);function _e(t){return"function"==typeof t?t:(t[we]||(t[we]=function(e){return t.handleEvent(e)}),t[we])}function g(){h.call(this),this.i=new ue(this),(this.M=this).F=null}function m(e,t){var r,n=e.F;if(n)for(r=[];n;n=n.F)r.push(n);if(e=e.M,n=t.type||t,"string"==typeof t?t=new c(t,e):t instanceof c?t.target=t.target||e:(a=t,Y(t=new c(n,e),a)),a=!0,r)for(var i=r.length-1;0<=i;i--)var s=t.g=r[i],a=be(s,n,!0,t)&&a;if(a=be(s=t.g=e,n,!0,t)&&a,a=be(s,n,!1,t)&&a,r)for(i=0;i<r.length;i++)a=be(s=t.g=r[i],n,!1,t)&&a}function be(e,t,r,n){if(!(t=e.i.g[String(t)]))return!0;t=t.concat();for(var i=!0,s=0;s<t.length;++s){var a,o,u=t[s];u&&!u.da&&u.capture==r&&(a=u.listener,o=u.ha||u.src,u.fa&&le(e.i,u),i=!1!==a.call(o,n)&&i)}return i&&!n.defaultPrevented}function Ie(e,t,r){if("function"==typeof e)r&&(e=p(e,r));else{if(!e||"function"!=typeof e.handleEvent)throw Error("Invalid listener argument");e=p(e.handleEvent,e)}return 2147483647<Number(t)?-1:R.setTimeout(e,t||0)}s(g,h),g.prototype[f]=!0,g.prototype.removeEventListener=function(e,t,r,n){!function e(t,r,n,i,s){if(Array.isArray(r))for(var a=0;a<r.length;a++)e(t,r[a],n,i,s);else i=l(i)?!!i.capture:!!i,n=_e(n),t&&t[f]?(t=t.i,(r=String(r).toString())in t.g&&-1<(n=he(a=t.g[r],n,i,s))&&(oe(a[n]),Array.prototype.splice.call(a,n,1),0==a.length)&&(delete t.g[r],t.h--)):(t=t&&ve(t))&&(r=t.g[r.toString()],n=(t=-1)<(t=r?he(r,n,i,s):t)?r[t]:null)&&me(n)}(this,e,t,r,n)},g.prototype.N=function(){if(g.aa.N.call(this),this.i){var e,t=this.i;for(e in t.g){for(var r=t.g[e],n=0;n<r.length;n++)oe(r[n]);delete t.g[e],t.h--}}this.F=null},g.prototype.K=function(e,t,r,n){return this.i.add(String(e),t,!1,r,n)},g.prototype.L=function(e,t,r,n){return this.i.add(String(e),t,!0,r,n)};class Te extends h{constructor(e,t){super(),this.m=e,this.l=t,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:function e(t){t.g=Ie(()=>{t.g=null,t.i&&(t.i=!1,e(t))},t.l);var r=t.h;t.h=null,t.m.apply(null,r)}(this)}N(){super.N(),this.g&&(R.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function y(e){h.call(this),this.h=e,this.g={}}s(y,h);var Ee=[];function Se(e){H(e.g,function(e,t){this.g.hasOwnProperty(t)&&me(e)},e),e.g={}}y.prototype.N=function(){y.aa.N.call(this),Se(this)},y.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var xe=R.JSON.stringify,Ce=R.JSON.parse,Ae=class{stringify(e){return R.JSON.stringify(e,void 0)}parse(e){return R.JSON.parse(e,void 0)}};function De(){}function Ne(e){return e.h||(e.h=e.i())}function ke(){}De.prototype.h=null;var Re={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function Oe(){c.call(this,"d")}function Le(){c.call(this,"c")}s(Oe,c),s(Le,c);var v={},Fe=null;function Ve(){return Fe=Fe||new g}function Me(e){c.call(this,v.La,e)}function Pe(){var e=Ve();m(e,new Me(e))}function Ue(e,t){c.call(this,v.STAT_EVENT,e),this.stat=t}function L(e){var t=Ve();m(t,new Ue(t,e))}function Be(e,t){c.call(this,v.Ma,e),this.size=t}function qe(e,t){if("function"!=typeof e)throw Error("Fn must not be null and must be a function");return R.setTimeout(function(){e()},t)}function je(){this.g=!0}function F(e,t,r,n){e.info(function(){return"XMLHTTP TEXT ("+t+"): "+((e,t)=>{if(!e.g)return t;if(!t)return null;try{var r=JSON.parse(t);if(r)for(e=0;e<r.length;e++)if(Array.isArray(r[e])){var n=r[e];if(!(n.length<2)){var i=n[1];if(Array.isArray(i)&&!(i.length<1)){var s=i[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var a=1;a<i.length;a++)i[a]=""}}}return xe(r)}catch(e){return t}})(e,r)+(n?" "+n:"")})}v.La="serverreachability",s(Me,c),v.STAT_EVENT="statevent",s(Ue,c),v.Ma="timingevent",s(Be,c),je.prototype.xa=function(){this.g=!1},je.prototype.info=function(){};var ze={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Ke={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Ge(){}function w(e,t,r,n){this.j=e,this.i=t,this.l=r,this.R=n||1,this.U=new y(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new $e}function $e(){this.i=null,this.g="",this.h=!1}s(Ge,De),Ge.prototype.g=function(){return new XMLHttpRequest},Ge.prototype.i=function(){return{}};var Qe=new Ge,He={},We={};function Xe(e,t,r){e.L=1,e.v=yt(b(t)),e.m=r,e.P=!0,Ye(e,null)}function Ye(e,t){e.F=Date.now(),Ze(e),e.A=b(e.v);var r=e.A,n=e.R,i=(Array.isArray(n)||(n=[String(n)]),Nt(r.i,"t",n),e.C=0,r=e.j.J,e.h=new $e,e.g=cr(e.j,r?t:null,!e.m),0<e.O&&(e.M=new Te(p(e.Y,e,e.g),e.O)),t=e.U,r=e.g,n=e.ca,"readystatechange");Array.isArray(i)||(i&&(Ee[0]=i.toString()),i=Ee);for(var a,o,u,l,h,c,s=0;s<i.length;s++){var d=fe(r,i[s],n||t.handleEvent,!1,t.h||t);if(!d)break;t.g[d.key]=d}t=e.H?W(e.H):{},e.m?(e.u||(e.u="POST"),t["Content-Type"]="application/x-www-form-urlencoded",e.g.ea(e.A,e.u,e.m,t)):(e.u="GET",e.g.ea(e.A,e.u,null,t)),Pe(),a=e.i,o=e.u,u=e.A,l=e.l,h=e.R,c=e.m,a.info(function(){if(a.g)if(c)for(var e="",t=c.split("&"),r=0;r<t.length;r++){var n,i,s=t[r].split("=");1<s.length&&(n=s[0],s=s[1],e=2<=(i=n.split("_")).length&&"type"==i[1]?e+(n+"=")+s+"&":e+(n+"=redacted&"))}else e=null;else e=c;return"XMLHTTP REQ ("+l+") [attempt "+h+"]: "+o+"\n"+u+"\n"+e})}function Je(e){return e.g&&"GET"==e.u&&2!=e.L&&e.j.Ca}function Ze(e){e.S=Date.now()+e.I,et(e,e.I)}function et(e,t){if(null!=e.B)throw Error("WatchDog timer not null");e.B=qe(p(e.ba,e),t)}function tt(e){e.B&&(R.clearTimeout(e.B),e.B=null)}function rt(e){0==e.j.G||e.J||or(e.j,e)}function V(e){tt(e);var t=e.M;t&&"function"==typeof t.ma&&t.ma(),e.M=null,Se(e.U),e.g&&(t=e.g,e.g=null,t.abort(),t.ma())}function nt(e,t){try{var r=e.j;if(0!=r.G&&(r.g==e||ut(r.h,e)))if(!e.K&&ut(r.h,e)&&3==r.G){try{var n=r.Da.g.parse(t)}catch(e){n=null}if(Array.isArray(n)&&3==n.length){var i=n;if(0==i[0]){e:if(!r.u){if(r.g){if(!(r.g.F+3e3<e.F))break e;ar(r),Xt(r)}nr(r),L(18)}}else r.za=i[1],0<r.za-r.T&&i[2]<37500&&r.F&&0==r.v&&!r.C&&(r.C=qe(p(r.Za,r),6e3));if(ot(r.h)<=1&&r.ca){try{r.ca()}catch(e){}r.ca=void 0}}else C(r,11)}else if(!e.K&&r.g!=e||ar(r),!O(t))for(i=r.Da.g.parse(t),t=0;t<i.length;t++){var s,a,o,u,l,h,c,d,f,g,m=i[t];r.T=m[0],m=m[1],2==r.G?"c"==m[0]?(r.K=m[1],r.ia=m[2],null!=(s=m[3])&&(r.la=s,r.j.info("VER="+r.la)),null!=(a=m[4])&&(r.Aa=a,r.j.info("SVER="+r.Aa)),null!=(o=m[5])&&"number"==typeof o&&0<o&&(n=1.5*o,r.L=n,r.j.info("backChannelRequestTimeoutMs_="+n)),n=r,(u=e.g)&&(!(l=u.g?u.g.getResponseHeader("X-Client-Wire-Protocol"):null)||(h=n.h).g||-1==l.indexOf("spdy")&&-1==l.indexOf("quic")&&-1==l.indexOf("h2")||(h.j=h.l,h.g=new Set,h.h&&(lt(h,h.h),h.h=null)),n.D)&&(c=u.g?u.g.getResponseHeader("X-HTTP-Session-Id"):null)&&(n.ya=c,I(n.I,n.D,c)),r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-e.F,r.j.info("Handshake RTT: "+r.R+"ms")),d=e,(n=r).qa=hr(n,n.J?n.ia:null,n.W),d.K?(ht(n.h,d),f=d,(g=n.L)&&(f.I=g),f.B&&(tt(f),Ze(f)),n.g=d):rr(n),0<r.i.length&&Jt(r)):"stop"!=m[0]&&"close"!=m[0]||C(r,7):3==r.G&&("stop"==m[0]||"close"==m[0]?"stop"==m[0]?C(r,7):Wt(r):"noop"!=m[0]&&r.l&&r.l.ta(m),r.v=0)}Pe()}catch(e){}}w.prototype.ca=function(e){e=e.target;var t=this.M;t&&3==M(e)?t.j():this.Y(e)},w.prototype.Y=function(e){try{if(e==this.g)e:{var t=M(this.g),r=this.g.Ba();this.g.Z();if(!(t<3)&&(3!=t||this.g&&(this.h.h||this.g.oa()||$t(this.g)))){this.J||4!=t||7==r||Pe(),tt(this);var n=this.g.Z();this.X=n;t:if(Je(this)){var i=$t(this.g),s=(e="",i.length),a=4==M(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){V(this),rt(this);var o="";break t}this.h.i=new R.TextDecoder}for(r=0;r<s;r++)this.h.h=!0,e+=this.h.i.decode(i[r],{stream:!(a&&r==s-1)});i.length=0,this.h.g+=e,this.C=0,o=this.h.g}else o=this.g.oa();if(this.o=200==n,S=this.i,x=this.u,C=this.A,A=this.l,D=this.R,N=t,k=n,S.info(function(){return"XMLHTTP RESP ("+A+") [ attempt "+D+"]: "+x+"\n"+C+"\n"+N+" "+k}),this.o){if(this.T&&!this.K){t:{if(this.g){var u,l=this.g;if((u=l.g?l.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!O(u)){var h=u;break t}}h=null}if(!(n=h)){this.o=!1,this.s=3,L(12),V(this),rt(this);break e}F(this.i,this.l,n,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,nt(this,n)}if(this.P){for(var c,d,n=!0;!this.J&&this.C<o.length;){if(I=o,E=T=void 0,T=(b=this).C,(c=-1==(E=I.indexOf("\n",T))?We:(T=Number(I.substring(T,E)),isNaN(T)?He:(E+=1)+T>I.length?We:(I=I.slice(E,E+T),b.C=E+T,I)))==We){4==t&&(this.s=4,L(14),n=!1),F(this.i,this.l,null,"[Incomplete Response]");break}if(c==He){this.s=4,L(15),F(this.i,this.l,o,"[Invalid Chunk]"),n=!1;break}F(this.i,this.l,c,null),nt(this,c)}Je(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=t||0!=o.length||this.h.h||(this.s=1,L(16),n=!1),this.o=this.o&&n,n?0<o.length&&!this.W&&(this.W=!0,(d=this.j).g==this)&&d.ba&&!d.M&&(d.j.info("Great, no buffering proxy detected. Bytes received: "+o.length),ir(d),d.M=!0,L(11)):(F(this.i,this.l,o,"[Invalid Chunked Response]"),V(this),rt(this))}else F(this.i,this.l,o,null),nt(this,o);4==t&&V(this),this.o&&!this.J&&(4==t?or(this.j,this):(this.o=!1,Ze(this)))}else{{var f=this.g;var g,m,p,y={};f=(f.g&&2<=M(f)&&f.g.getAllResponseHeaders()||"").split("\r\n");for(let e=0;e<f.length;e++)O(f[e])||(g=(e=>{for(var t=1,r=(e=e.split(":"),[]);0<t&&e.length;)r.push(e.shift()),t--;return e.length&&r.push(e.join(":")),r})(f[e]),m=g[0],"string"==typeof(g=g[1])&&(g=g.trim(),p=y[m]||[],(y[m]=p).push(g)));var v,w=y,_=function(e){return e.join(", ")};for(v in w)_.call(void 0,w[v],v,w)}400==n&&0<o.indexOf("Unknown SID")?(this.s=3,L(12)):(this.s=0,L(13)),V(this),rt(this)}}}}catch(e){}var b,I,T,E,S,x,C,A,D,N,k},w.prototype.cancel=function(){this.J=!0,V(this)},w.prototype.ba=function(){this.B=null;var e,t,r=Date.now();0<=r-this.S?(e=this.i,t=this.A,e.info(function(){return"TIMEOUT: "+t}),2!=this.L&&(Pe(),L(17)),V(this),this.s=2,rt(this)):et(this,this.S-r)};var it=class{constructor(e,t){this.g=e,this.map=t}};function st(e){this.l=e||10,e=R.PerformanceNavigationTiming?0<(e=R.performance.getEntriesByType("navigation")).length&&("hq"==e[0].nextHopProtocol||"h2"==e[0].nextHopProtocol):!!(R.chrome&&R.chrome.loadTimes&&R.chrome.loadTimes()&&R.chrome.loadTimes().wasFetchedViaSpdy),this.j=e?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function at(e){return e.h||e.g&&e.g.size>=e.j}function ot(e){return e.h?1:e.g?e.g.size:0}function ut(e,t){return e.h?e.h==t:e.g&&e.g.has(t)}function lt(e,t){e.g?e.g.add(t):e.h=t}function ht(e,t){e.h&&e.h==t?e.h=null:e.g&&e.g.has(t)&&e.g.delete(t)}function ct(t){if(null!=t.h)return t.i.concat(t.h.D);if(null==t.g||0===t.g.size)return K(t.i);{let e=t.i;for(var r of t.g.values())e=e.concat(r.D);return e}}function dt(e,t){if(e.forEach&&"function"==typeof e.forEach)e.forEach(t,void 0);else if(B(e)||"string"==typeof e)Array.prototype.forEach.call(e,t,void 0);else for(var r=(e=>{if(e.na&&"function"==typeof e.na)return e.na();if(!e.V||"function"!=typeof e.V){if("undefined"!=typeof Map&&e instanceof Map)return Array.from(e.keys());if(!("undefined"!=typeof Set&&e instanceof Set)){if(B(e)||"string"==typeof e){var t=[];e=e.length;for(var r=0;r<e;r++)t.push(r)}else for(var n in t=[],r=0,e)t[r++]=n;return t}}})(e),n=(e=>{if(e.V&&"function"==typeof e.V)return e.V();if("undefined"!=typeof Map&&e instanceof Map||"undefined"!=typeof Set&&e instanceof Set)return Array.from(e.values());if("string"==typeof e)return e.split("");if(B(e))for(var t=[],r=e.length,n=0;n<r;n++)t.push(e[n]);else for(n in t=[],r=0,e)t[r++]=e[n];return t})(e),i=n.length,s=0;s<i;s++)t.call(void 0,n[s],r&&r[s],e)}st.prototype.cancel=function(){if(this.i=ct(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(var e of this.g.values())e.cancel();this.g.clear()}};var ft=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function _(e){var t,r;this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,e instanceof _?(this.h=e.h,gt(this,e.j),this.o=e.o,this.g=e.g,mt(this,e.s),this.l=e.l,t=e.i,(r=new Ct).i=t.i,t.g&&(r.g=new Map(t.g),r.h=t.h),pt(this,r),this.m=e.m):e&&(t=String(e).match(ft))?(this.h=!1,gt(this,t[1]||"",!0),this.o=vt(t[2]||""),this.g=vt(t[3]||"",!0),mt(this,t[4]),this.l=vt(t[5]||"",!0),pt(this,t[6]||"",!0),this.m=vt(t[7]||"")):(this.h=!1,this.i=new Ct(null,this.h))}function b(e){return new _(e)}function gt(e,t,r){e.j=r?vt(t,!0):t,e.j&&(e.j=e.j.replace(/:$/,""))}function mt(e,t){if(t){if(t=Number(t),isNaN(t)||t<0)throw Error("Bad port number "+t);e.s=t}else e.s=null}function pt(e,t,r){var n,i;t instanceof Ct?(e.i=t,n=e.i,(i=e.h)&&!n.j&&(T(n),n.i=null,n.g.forEach(function(e,t){var r=t.toLowerCase();t!=r&&(At(this,t),Nt(this,r,e))},n)),n.j=i):(r||(t=wt(t,St)),e.i=new Ct(t,e.h))}function I(e,t,r){e.i.set(t,r)}function yt(e){return I(e,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),e}function vt(e,t){return e?t?decodeURI(e.replace(/%25/g,"%2525")):decodeURIComponent(e):""}function wt(e,t,r){return"string"==typeof e?(e=encodeURI(e).replace(t,_t),e=r?e.replace(/%25([0-9a-fA-F]{2})/g,"%$1"):e):null}function _t(e){return"%"+((e=e.charCodeAt(0))>>4&15).toString(16)+(15&e).toString(16)}_.prototype.toString=function(){var e=[],t=this.j,r=(t&&e.push(wt(t,It,!0),":"),this.g);return!r&&"file"!=t||(e.push("//"),(t=this.o)&&e.push(wt(t,It,!0),"@"),e.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null==(r=this.s))||e.push(":",String(r)),(r=this.l)&&(this.g&&"/"!=r.charAt(0)&&e.push("/"),e.push(wt(r,"/"==r.charAt(0)?Et:Tt,!0))),(r=this.i.toString())&&e.push("?",r),(r=this.m)&&e.push("#",wt(r,xt)),e.join("")};var bt,It=/[#\/\?@]/g,Tt=/[#\?:]/g,Et=/[#\?]/g,St=/[#\?@]/g,xt=/#/g;function Ct(e,t){this.h=this.g=null,this.i=e||null,this.j=!!t}function T(r){if(!r.g&&(r.g=new Map,r.h=0,r.i)){var e=r.i,t=function(e,t){r.add(decodeURIComponent(e.replace(/\+/g," ")),t)};if(e){e=e.split("&");for(var n=0;n<e.length;n++){var i,s=e[n].indexOf("="),a=null;0<=s?(i=e[n].substring(0,s),a=e[n].substring(s+1)):i=e[n],t(i,a?decodeURIComponent(a.replace(/\+/g," ")):"")}}}}function At(e,t){T(e),t=E(e,t),e.g.has(t)&&(e.i=null,e.h-=e.g.get(t).length,e.g.delete(t))}function Dt(e,t){return T(e),t=E(e,t),e.g.has(t)}function Nt(e,t,r){At(e,t),0<r.length&&(e.i=null,e.g.set(E(e,t),K(r)),e.h+=r.length)}function E(e,t){return t=String(t),t=e.j?t.toLowerCase():t}function S(e,t,r,n,i){try{i&&(i.onload=null,i.onerror=null,i.onabort=null,i.ontimeout=null),n(r)}catch(e){}}function kt(){this.g=new Ae}function Rt(e){this.l=e.Ub||null,this.j=e.eb||!1}function Ot(e,t){g.call(this),this.D=e,this.o=t,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Lt(e){e.j.read().then(e.Pa.bind(e)).catch(e.ga.bind(e))}function Ft(e){e.readyState=4,e.l=null,e.j=null,e.v=null,Vt(e)}function Vt(e){e.onreadystatechange&&e.onreadystatechange.call(e)}function Mt(e){let r="";return H(e,function(e,t){r=(r=r+t+":")+e+"\r\n"}),r}function Pt(e,t,r){e:{for(n in r){var n=!1;break e}n=!0}n||(r=Mt(r),"string"==typeof e?null!=r&&encodeURIComponent(String(r)):I(e,t,r))}function x(e){g.call(this),this.headers=new Map,this.o=e||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(e=Ct.prototype).add=function(e,t){T(this),this.i=null,e=E(this,e);var r=this.g.get(e);return r||this.g.set(e,r=[]),r.push(t),this.h+=1,this},e.forEach=function(r,n){T(this),this.g.forEach(function(e,t){e.forEach(function(e){r.call(n,e,t,this)},this)},this)},e.na=function(){T(this);var t=Array.from(this.g.values()),r=Array.from(this.g.keys()),n=[];for(let s=0;s<r.length;s++){var i=t[s];for(let e=0;e<i.length;e++)n.push(r[s])}return n},e.V=function(t){T(this);let r=[];if("string"==typeof t)Dt(this,t)&&(r=r.concat(this.g.get(E(this,t))));else{t=Array.from(this.g.values());for(let e=0;e<t.length;e++)r=r.concat(t[e])}return r},e.set=function(e,t){return T(this),this.i=null,Dt(this,e=E(this,e))&&(this.h-=this.g.get(e).length),this.g.set(e,[t]),this.h+=1,this},e.get=function(e,t){return e&&0<(e=this.V(e)).length?String(e[0]):t},e.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var e=[],t=Array.from(this.g.keys()),r=0;r<t.length;r++)for(var n=t[r],i=encodeURIComponent(String(n)),s=this.V(n),n=0;n<s.length;n++){var a=i;""!==s[n]&&(a+="="+encodeURIComponent(String(s[n]))),e.push(a)}return this.i=e.join("&")},s(Rt,De),Rt.prototype.g=function(){return new Ot(this.l,this.j)},Rt.prototype.i=(bt={},function(){return bt}),s(Ot,g),(e=Ot.prototype).open=function(e,t){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=e,this.A=t,this.readyState=1,Vt(this)},e.send=function(e){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;var t={headers:this.u,method:this.B,credentials:this.m,cache:void 0};e&&(t.body=e),(this.D||R).fetch(new Request(this.A,t)).then(this.Sa.bind(this),this.ga.bind(this))},e.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Ft(this)),this.readyState=0},e.Sa=function(e){if(this.g&&(this.l=e,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=e.headers,this.readyState=2,Vt(this)),this.g)&&(this.readyState=3,Vt(this),this.g))if("arraybuffer"===this.responseType)e.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==R.ReadableStream&&"body"in e){if(this.j=e.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Lt(this)}else e.text().then(this.Ra.bind(this),this.ga.bind(this))},e.Pa=function(e){var t;this.g&&(this.o&&e.value?this.response.push(e.value):!this.o&&(t=e.value||new Uint8Array(0),t=this.v.decode(t,{stream:!e.done}))&&(this.response=this.responseText+=t),(e.done?Ft:Vt)(this),3==this.readyState)&&Lt(this)},e.Ra=function(e){this.g&&(this.response=this.responseText=e,Ft(this))},e.Qa=function(e){this.g&&(this.response=e,Ft(this))},e.ga=function(){this.g&&Ft(this)},e.setRequestHeader=function(e,t){this.u.append(e,t)},e.getResponseHeader=function(e){return this.h&&this.h.get(e.toLowerCase())||""},e.getAllResponseHeaders=function(){if(!this.h)return"";for(var e=[],t=this.h.entries(),r=t.next();!r.done;)r=r.value,e.push(r[0]+": "+r[1]),r=t.next();return e.join("\r\n")},Object.defineProperty(Ot.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(e){this.m=e?"include":"same-origin"}}),s(x,g);var Ut=/^https?$/i,Bt=["POST","PUT"];function qt(e,t){e.h=!1,e.g&&(e.j=!0,e.g.abort(),e.j=!1),e.l=t,e.m=5,jt(e),Kt(e)}function jt(e){e.A||(e.A=!0,m(e,"complete"),m(e,"error"))}function zt(e){if(e.h&&void 0!==U&&(!e.v[1]||4!=M(e)||2!=e.Z()))if(e.u&&4==M(e))Ie(e.Ea,0,e);else if(m(e,"readystatechange"),4==M(e)){e.h=!1;try{var t,r,n,i=e.Z();switch(i){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var s=!0;break;default:s=!1}if((t=s)||((r=0===i)&&(!(n=String(e.D).match(ft)[1]||null)&&R.self&&R.self.location&&(n=R.self.location.protocol.slice(0,-1)),r=!Ut.test(n?n.toLowerCase():"")),t=r),t)m(e,"complete"),m(e,"success");else{e.m=6;try{var a=2<M(e)?e.g.statusText:""}catch(e){a=""}e.l=a+" ["+e.Z()+"]",jt(e)}}finally{Kt(e)}}}function Kt(e,t){if(e.g){Gt(e);var r=e.g,n=e.v[0]?()=>{}:null;e.g=null,e.v=null,t||m(e,"ready");try{r.onreadystatechange=n}catch(e){}}}function Gt(e){e.I&&(R.clearTimeout(e.I),e.I=null)}function M(e){return e.g?e.g.readyState:0}function $t(e){try{if(e.g){if("response"in e.g)return e.g.response;switch(e.H){case"":case"text":return e.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in e.g)return e.g.mozResponseArrayBuffer}}return null}catch(e){return null}}function Qt(e,t,r){return r&&r.internalChannelParams&&r.internalChannelParams[e]||t}function Ht(e){this.Aa=0,this.i=[],this.j=new je,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Qt("failFast",!1,e),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Qt("baseRetryDelayMs",5e3,e),this.cb=Qt("retryDelaySeedMs",1e4,e),this.Wa=Qt("forwardChannelMaxRetries",2,e),this.wa=Qt("forwardChannelRequestTimeoutMs",2e4,e),this.pa=e&&e.xmlHttpFactory||void 0,this.Xa=e&&e.Tb||void 0,this.Ca=e&&e.useFetchStreams||!1,this.L=void 0,this.J=e&&e.supportsCrossDomainXhr||!1,this.K="",this.h=new st(e&&e.concurrentRequestLimit),this.Da=new kt,this.P=e&&e.fastHandshake||!1,this.O=e&&e.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=e&&e.Rb||!1,e&&e.xa&&this.j.xa(),e&&e.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&e&&e.detectBufferingProxy||!1,this.ja=void 0,e&&e.longPollingTimeout&&0<e.longPollingTimeout&&(this.ja=e.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function Wt(e){if(Yt(e),3==e.G){var t=e.U++,r=b(e.I);if(I(r,"SID",e.K),I(r,"RID",t),I(r,"TYPE","terminate"),er(e,r),(t=new w(e,e.j,t)).L=2,t.v=yt(b(r)),r=!1,R.navigator&&R.navigator.sendBeacon)try{r=R.navigator.sendBeacon(t.v.toString(),"")}catch(e){}!r&&R.Image&&((new Image).src=t.v,r=!0),r||(t.g=cr(t.j,null),t.g.ea(t.v)),t.F=Date.now(),Ze(t)}lr(e)}function Xt(e){e.g&&(ir(e),e.g.cancel(),e.g=null)}function Yt(e){Xt(e),e.u&&(R.clearTimeout(e.u),e.u=null),ar(e),e.h.cancel(),e.s&&("number"==typeof e.s&&R.clearTimeout(e.s),e.s=null)}function Jt(e){var t;at(e.h)||e.s||(e.s=!0,t=e.Ga,o||te(),u||(o(),u=!0),ee.add(t,e),e.B=0)}function Zt(e,t){var r=t?t.l:e.U++,n=b(e.I);I(n,"SID",e.K),I(n,"RID",r),I(n,"AID",e.T),er(e,n),e.m&&e.o&&Pt(n,e.m,e.o),r=new w(e,e.j,r,e.B+1),null===e.m&&(r.H=e.o),t&&(e.i=t.D.concat(e.i)),t=tr(e,r,1e3),r.I=Math.round(.5*e.wa)+Math.round(.5*e.wa*Math.random()),lt(e.h,r),Xe(r,n,t)}function er(e,r){e.H&&H(e.H,function(e,t){I(r,t,e)}),e.l&&dt({},function(e,t){I(r,t,e)})}function tr(e,t,i){i=Math.min(e.i.length,i);var s=e.l?p(e.l.Na,e.l,e):null;e:{let r=e.i,n=-1;for(;;){var a=["count="+i];-1==n?0<i?(n=r[0].g,a.push("ofs="+n)):n=0:a.push("ofs="+n);let e=!0;for(let t=0;t<i;t++){var o=r[t].g,u=r[t].map;if((o-=n)<0)n=Math.max(0,r[t].g-100),e=!1;else try{((e,n,t)=>{let i=t||"";try{dt(e,function(e,t){let r=e;l(e)&&(r=xe(e)),n.push(i+t+"="+encodeURIComponent(r))})}catch(e){throw n.push(i+"type="+encodeURIComponent("_badmap")),e}})(u,a,"req"+o+"_")}catch(e){s&&s(u)}}if(e){s=a.join("&");break e}}}return e=e.i.splice(0,i),t.D=e,s}function rr(e){var t;e.g||e.u||(e.Y=1,t=e.Fa,o||te(),u||(o(),u=!0),ee.add(t,e),e.v=0)}function nr(e){return!(e.g||e.u||3<=e.v)&&(e.Y++,e.u=qe(p(e.Fa,e),ur(e,e.v)),e.v++,1)}function ir(e){null!=e.A&&(R.clearTimeout(e.A),e.A=null)}function sr(e){e.g=new w(e,e.j,"rpc",e.Y),null===e.m&&(e.g.H=e.o),e.g.O=0;var t=b(e.qa),r=(I(t,"RID","rpc"),I(t,"SID",e.K),I(t,"AID",e.T),I(t,"CI",e.F?"0":"1"),!e.F&&e.ja&&I(t,"TO",e.ja),I(t,"TYPE","xmlhttp"),er(e,t),e.m&&e.o&&Pt(t,e.m,e.o),e.L&&(e.g.I=e.L),e.g);e=e.ia,r.L=1,r.v=yt(b(t)),r.m=null,r.P=!0,Ye(r,e)}function ar(e){null!=e.C&&(R.clearTimeout(e.C),e.C=null)}function or(e,t){var r,n,i,s=null;if(e.g==t){ar(e),ir(e),e.g=null;var a=2}else{if(!ut(e.h,t))return;s=t.D,ht(e.h,t),a=1}if(0!=e.G)if(t.o)(1==a?(s=t.m?t.m.length:0,t=Date.now()-t.F,r=e.B,m(a=Ve(),new Be(a,s)),Jt):rr)(e);else if(3==(r=t.s)||0==r&&0<t.X||(1!=a||(i=t,ot((n=e).h)>=n.h.j-(n.s?1:0))||(n.s?(n.i=i.D.concat(n.i),0):1==n.G||2==n.G||n.B>=(n.Va?0:n.Wa)||(n.s=qe(p(n.Ga,n,i),ur(n,n.B)),n.B++,0)))&&(2!=a||!nr(e)))switch(s&&0<s.length&&(t=e.h,t.i=t.i.concat(s)),r){case 1:C(e,5);break;case 4:C(e,10);break;case 3:C(e,6);break;default:C(e,2)}}function ur(e,t){let r=e.Ta+Math.floor(Math.random()*e.cb);return e.isActive()||(r*=2),r*t}function C(e,t){var r,n,i;e.j.info("Error code "+t),2==t?(r=p(e.fb,e),n=!(i=e.Xa),i=new _(i||"//www.google.com/images/cleardot.gif"),R.location&&"http"==R.location.protocol||gt(i,"https"),yt(i),(n?(t,r)=>{var n=new je;if(R.Image){let e=new Image;e.onload=z(S,n,"TestLoadImage: loaded",!0,r,e),e.onerror=z(S,n,"TestLoadImage: error",!1,r,e),e.onabort=z(S,n,"TestLoadImage: abort",!1,r,e),e.ontimeout=z(S,n,"TestLoadImage: timeout",!1,r,e),R.setTimeout(function(){e.ontimeout&&e.ontimeout()},1e4),e.src=t}else r(!1)}:(e,t)=>{let r=new je,n=new AbortController,i=setTimeout(()=>{n.abort(),S(r,0,!1,t)},1e4);fetch(e,{signal:n.signal}).then(e=>{clearTimeout(i),e.ok?S(r,0,!0,t):S(r,0,!1,t)}).catch(()=>{clearTimeout(i),S(r,0,!1,t)})})(i.toString(),r)):L(2),e.G=0,e.l&&e.l.sa(t),lr(e),Yt(e)}function lr(e){var t;e.G=0,e.ka=[],e.l&&(0==(t=ct(e.h)).length&&0==e.i.length||(G(e.ka,t),G(e.ka,e.i),e.h.i.length=0,K(e.i),e.i.length=0),e.l.ra())}function hr(e,t,r){var n,i,s=r instanceof _?b(r):new _(r);return""!=s.g?(t&&(s.g=t+"."+s.g),mt(s,s.s)):(s=(n=R.location).protocol,t=t?t+"."+n.hostname:n.hostname,n=+n.port,i=new _(null),s&&gt(i,s),t&&(i.g=t),n&&mt(i,n),r&&(i.l=r),s=i),r=e.D,t=e.ya,r&&t&&I(s,r,t),I(s,"VER",e.la),er(e,s),s}function cr(e,t,r){if(t&&!e.J)throw Error("Can't create secondary domain capable XhrIo object.");return(t=e.Ca&&!e.pa?new x(new Rt({eb:r})):new x(e.pa)).Ha(e.J),t}function dr(){}function fr(){}function A(e,t){g.call(this),this.g=new Ht(t),this.l=e,this.h=t&&t.messageUrlParams||null,e=t&&t.messageHeaders||null,t&&t.clientProtocolHeaderRequired&&(e?e["X-Client-Protocol"]="webchannel":e={"X-Client-Protocol":"webchannel"}),this.g.o=e,e=t&&t.initMessageHeaders||null,t&&t.messageContentType&&(e?e["X-WebChannel-Content-Type"]=t.messageContentType:e={"X-WebChannel-Content-Type":t.messageContentType}),t&&t.va&&(e?e["X-WebChannel-Client-Profile"]=t.va:e={"X-WebChannel-Client-Profile":t.va}),this.g.S=e,(e=t&&t.Sb)&&!O(e)&&(this.g.m=e),this.v=t&&t.supportsCrossDomainXhr||!1,this.u=t&&t.sendRawJson||!1,(t=t&&t.httpSessionIdParam)&&!O(t)&&(this.g.D=t,null!==(e=this.h))&&t in e&&t in(e=this.h)&&delete e[t],this.j=new D(this)}function gr(e){Oe.call(this),e.__headers__&&(this.headers=e.__headers__,this.statusCode=e.__status__,delete e.__headers__,delete e.__status__);var t=e.__sm__;if(t){e:{for(var r in t){e=r;break e}e=void 0}(this.i=e)&&(e=this.i,t=null!==t&&e in t?t[e]:void 0),this.data=t}else this.data=e}function mr(){Le.call(this),this.status=1}function D(e){this.g=e}(e=x.prototype).Ha=function(e){this.J=e},e.ea=function(e,t,r,n){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+e);t=t?t.toUpperCase():"GET",this.D=e,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=(this.o||Qe).g(),this.v=this.o?Ne(this.o):Ne(Qe),this.g.onreadystatechange=p(this.Ea,this);try{this.B=!0,this.g.open(t,String(e),!0),this.B=!1}catch(e){return void qt(this,e)}if(e=r||"",r=new Map(this.headers),n)if(Object.getPrototypeOf(n)===Object.prototype)for(var i in n)r.set(i,n[i]);else{if("function"!=typeof n.keys||"function"!=typeof n.get)throw Error("Unknown input type for opt_headers: "+String(n));for(var s of n.keys())r.set(s,n.get(s))}n=Array.from(r.keys()).find(e=>"content-type"==e.toLowerCase()),i=R.FormData&&e instanceof R.FormData,0<=Array.prototype.indexOf.call(Bt,t,void 0)&&!n&&!i&&r.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(var[a,o]of r)this.g.setRequestHeader(a,o);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Gt(this),this.u=!0,this.g.send(e),this.u=!1}catch(e){qt(this,e)}},e.abort=function(e){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=e||7,m(this,"complete"),m(this,"abort"),Kt(this))},e.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Kt(this,!0)),x.aa.N.call(this)},e.Ea=function(){this.s||(this.B||this.u||this.j?zt(this):this.bb())},e.bb=function(){zt(this)},e.isActive=function(){return!!this.g},e.Z=function(){try{return 2<M(this)?this.g.status:-1}catch(e){return-1}},e.oa=function(){try{return this.g?this.g.responseText:""}catch(e){return""}},e.Oa=function(e){var t;if(this.g)return t=this.g.responseText,e&&0==t.indexOf(e)&&(t=t.substring(e.length)),Ce(t)},e.Ba=function(){return this.m},e.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(e=Ht.prototype).la=8,e.G=1,e.connect=function(e,t,r,n){L(0),this.W=e,this.H=t||{},r&&void 0!==n&&(this.H.OSID=r,this.H.OAID=n),this.F=this.X,this.I=hr(this,null,this.W),Jt(this)},e.Ga=function(t){if(this.s)if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;var r=new w(this,this.j,t);let e=this.o;if(this.S&&(e?Y(e=W(e),this.S):e=this.S),null!==this.m||this.O||(r.H=e,e=null),this.P)e:{for(var n=0,i=0;i<this.i.length;i++){var s=this.i[i];if("__data__"in s.map&&"string"==typeof(s=s.map.__data__)?s=s.length:s=void 0,void 0===s)break;if(4096<(n+=s)){n=i;break e}if(4096===n||i===this.i.length-1){n=i+1;break e}}n=1e3}else n=1e3;n=tr(this,r,n),I(i=b(this.I),"RID",t),I(i,"CVER",22),this.D&&I(i,"X-HTTP-Session-Id",this.D),er(this,i),e&&(this.O?n="headers="+encodeURIComponent(String(Mt(e)))+"&"+n:this.m&&Pt(i,this.m,e)),lt(this.h,r),this.Ua&&I(i,"TYPE","init"),this.P?(I(i,"$req",n),I(i,"SID","null"),r.T=!0,Xe(r,i,null)):Xe(r,i,n),this.G=2}}else 3==this.G&&(t?Zt(this,t):0==this.i.length||at(this.h)||Zt(this))},e.Fa=function(){var e;this.u=null,sr(this),this.ba&&!(this.M||null==this.g||this.R<=0)&&(e=2*this.R,this.j.info("BP detection timer enabled: "+e),this.A=qe(p(this.ab,this),e))},e.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,L(10),Xt(this),sr(this))},e.Za=function(){null!=this.C&&(this.C=null,Xt(this),nr(this),L(19))},e.fb=function(e){e?(this.j.info("Successfully pinged google.com"),L(2)):(this.j.info("Failed to ping google.com"),L(1))},e.isActive=function(){return!!this.l&&this.l.isActive(this)},(e=dr.prototype).ua=function(){},e.ta=function(){},e.sa=function(){},e.ra=function(){},e.isActive=function(){return!0},e.Na=function(){},fr.prototype.g=function(e,t){return new A(e,t)},s(A,g),A.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},A.prototype.close=function(){Wt(this.g)},A.prototype.o=function(e){var t,r=this.g;"string"==typeof e?((t={}).__data__=e,e=t):this.u&&((t={}).__data__=xe(e),e=t),r.i.push(new it(r.Ya++,e)),3==r.G&&Jt(r)},A.prototype.N=function(){this.g.l=null,delete this.j,Wt(this.g),delete this.g,A.aa.N.call(this)},s(gr,Oe),s(mr,Le),s(D,dr),D.prototype.ua=function(){m(this.g,"a")},D.prototype.ta=function(e){m(this.g,new gr(e))},D.prototype.sa=function(e){m(this.g,new mr)},D.prototype.ra=function(){m(this.g,"b")},fr.prototype.createWebChannel=fr.prototype.g,A.prototype.send=A.prototype.o,A.prototype.open=A.prototype.m,Tr=function(){return new fr},Ir=Ve,br=v,_r={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},ze.NO_ERROR=0,ze.TIMEOUT=8,ze.HTTP_ERROR=6,wr=ze,Ke.COMPLETE="complete",vr=Ke,(ke.EventType=Re).OPEN="a",Re.CLOSE="b",Re.ERROR="c",Re.MESSAGE="d",g.prototype.listen=g.prototype.K,yr=ke,x.prototype.listenOnce=x.prototype.L,x.prototype.getLastError=x.prototype.Ka,x.prototype.getLastErrorCode=x.prototype.Ba,x.prototype.getStatus=x.prototype.Z,x.prototype.getResponseJson=x.prototype.Oa,x.prototype.getResponseText=x.prototype.oa,x.prototype.send=x.prototype.ea,x.prototype.setWithCredentials=x.prototype.Ha,pr=x}).apply(void 0!==Er?Er:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});let ye="@firebase/firestore";class l{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}l.UNAUTHENTICATED=new l(null),l.GOOGLE_CREDENTIALS=new l("google-credentials-uid"),l.FIRST_PARTY=new l("first-party-uid"),l.MOCK_USER=new l("mock-user");let ve="11.10.0",we=new class{constructor(e){this.name=e,this._logLevel=ce,this._logHandler=fe,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in c))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?he[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,c.DEBUG,...e),this._logHandler(this,c.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,c.VERBOSE,...e),this._logHandler(this,c.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,c.INFO,...e),this._logHandler(this,c.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,c.WARN,...e),this._logHandler(this,c.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,c.ERROR,...e),this._logHandler(this,c.ERROR,...e)}}("@firebase/firestore");function _e(){return we.logLevel}function p(e,...t){var r;we.logLevel<=c.DEBUG&&(r=t.map(Ie),we.debug(`Firestore (${ve}): `+e,...r))}function d(e,...t){var r;we.logLevel<=c.ERROR&&(r=t.map(Ie),we.error(`Firestore (${ve}): `+e,...r))}function be(e,...t){var r;we.logLevel<=c.WARN&&(r=t.map(Ie),we.warn(`Firestore (${ve}): `+e,...r))}function Ie(t){if("string"==typeof t)return t;try{return JSON.stringify(t)}catch(e){return t}}function E(e,t,r){let n="Unexpected state";"string"==typeof t?n=t:r=t,Te(e,n,r)}function Te(e,t,r){let n=`FIRESTORE (${ve}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==r)try{n+=" CONTEXT: "+JSON.stringify(r)}catch(e){n+=" CONTEXT: "+r}throw d(n),new Error(n)}function y(e,t,r,n){let i="Unexpected state";"string"==typeof r?i=r:n=r,e||Te(t,i,n)}let b={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class I extends ie{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: `+this.message}}class f{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class Ee{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization","Bearer "+e)}}class Se{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(l.UNAUTHENTICATED))}shutdown(){}}class xe{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class Ce{constructor(e){this.t=e,this.currentUser=l.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(t,r){y(void 0===this.o,42304);let n=this.i,i=e=>this.i!==n?(n=this.i,r(e)):Promise.resolve(),s=new f,a=(this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new f,t.enqueueRetryable(()=>i(this.currentUser))},()=>{let e=s;t.enqueueRetryable(async()=>{await e.promise,await i(this.currentUser)})}),o=e=>{p("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),a())};this.t.onInit(e=>o(e)),setTimeout(()=>{var e;this.auth||((e=this.t.getImmediate({optional:!0}))?o(e):(p("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new f))},0),a()}getToken(){let t=this.i,e=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(e).then(e=>this.i!==t?(p("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):e?(y("string"==typeof e.accessToken,31837,{l:e}),new Ee(e.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){var e=this.auth&&this.auth.getUid();return y(null===e||"string"==typeof e,2055,{h:e}),new l(e)}}class Ae{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=l.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);var e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class De{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new Ae(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(l.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class Ne{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&0<e.length&&this.headers.set("x-firebase-appcheck",this.value)}}class ke{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,Hd._isFirebaseServerApp(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(t,r){y(void 0===this.o,3512);let n=e=>{null!=e.error&&p("FirebaseAppCheckTokenProvider","Error getting App Check token; using placeholder token instead. Error: "+e.error.message);var t=e.token!==this.m;return this.m=e.token,p("FirebaseAppCheckTokenProvider",`Received ${t?"new":"existing"} token.`),t?r(e.token):Promise.resolve()},i=(this.o=e=>{t.enqueueRetryable(()=>n(e))},e=>{p("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)});this.V.onInit(e=>i(e)),setTimeout(()=>{var e;this.appCheck||((e=this.V.getImmediate({optional:!0}))?i(e):p("FirebaseAppCheckTokenProvider","AppCheck not yet detected"))},0)}getToken(){var e;return this.p?Promise.resolve(new Ne(this.p)):(e=this.forceRefresh,this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(y("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new Ne(e.token)):null):Promise.resolve(null))}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}function Re(){return new TextEncoder}class Oe{static newId(){var t=62*Math.floor(256/62);let r="";for(;r.length<20;){var n=(t=>{var r="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(t);if(r&&"function"==typeof r.getRandomValues)r.getRandomValues(n);else for(let e=0;e<t;e++)n[e]=Math.floor(256*Math.random());return n})(40);for(let e=0;e<n.length;++e)r.length<20&&n[e]<t&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[e]%62))}return r}}function S(e,t){return e<t?-1:t<e?1:0}function Le(e,t){let r=0;for(;r<e.length&&r<t.length;){var n,i=e.codePointAt(r),s=t.codePointAt(r);if(i!==s)return!(i<128&&s<128)&&0!==(n=((e,t)=>{for(let r=0;r<e.length&&r<t.length;++r)if(e[r]!==t[r])return S(e[r],t[r]);return S(e.length,t.length)})((n=Re()).encode(Fe(e,r)),n.encode(Fe(t,r))))?n:S(i,s);r+=65535<i?2:1}return S(e.length,t.length)}function Fe(e,t){return 65535<e.codePointAt(t)?e.substring(t,t+2):e.substring(t,t+1)}function Ve(e,r,n){return e.length===r.length&&e.every((e,t)=>n(e,r[t]))}function Me(e){return e+"\0"}let Pe="__name__";class Ue{constructor(e,t,r){void 0===t?t=0:t>e.length&&E(637,{offset:t,range:e.length}),void 0===r?r=e.length-t:r>e.length-t&&E(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return 0===Ue.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof Ue?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return this.construct(this.segments,this.offset+(e=void 0===e?1:e),this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(t,r){let e=Math.min(t.length,r.length);for(let n=0;n<e;n++){let e=Ue.compareSegments(t.get(n),r.get(n));if(0!==e)return e}return S(t.length,r.length)}static compareSegments(e,t){var r=Ue.isNumericId(e),n=Ue.isNumericId(t);return r&&!n?-1:!r&&n?1:r&&n?Ue.extractNumericId(e).compare(Ue.extractNumericId(t)):Le(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return ge.fromString(e.substring(4,e.length-2))}}class T extends Ue{construct(e,t,r){return new T(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){var t,r=[];for(t of e){if(0<=t.indexOf("//"))throw new I(b.INVALID_ARGUMENT,`Invalid segment (${t}). Paths must not contain // in them.`);r.push(...t.split("/").filter(e=>0<e.length))}return new T(r)}static emptyPath(){return new T([])}}let Be=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class h extends Ue{construct(e,t,r){return new h(e,t,r)}static isValidIdentifier(e){return Be.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),e=h.isValidIdentifier(e)?e:"`"+e+"`")).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===Pe}static keyField(){return new h([Pe])}static fromServerFormat(t){let e=[],r="",n=0;var i=()=>{if(0===r.length)throw new I(b.INVALID_ARGUMENT,`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);e.push(r),r=""};let s=!1;for(;n<t.length;){let e=t[n];if("\\"===e){if(n+1===t.length)throw new I(b.INVALID_ARGUMENT,"Path has trailing escape character: "+t);let e=t[n+1];if("\\"!==e&&"."!==e&&"`"!==e)throw new I(b.INVALID_ARGUMENT,"Path has invalid escape sequence: "+t);r+=e,n+=2}else"`"===e?s=!s:"."!==e||s?r+=e:i(),n++}if(i(),s)throw new I(b.INVALID_ARGUMENT,"Unterminated ` in path: "+t);return new h(e)}static emptyPath(){return new h([])}}class x{constructor(e){this.path=e}static fromPath(e){return new x(T.fromString(e))}static fromName(e){return new x(T.fromString(e).popFirst(5))}static empty(){return new x(T.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return 2<=this.path.length&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===T.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return T.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new x(new T(e.slice()))}}function qe(e,t,r){if(!r)throw new I(b.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function je(e,t,r,n){if(!0===t&&!0===n)throw new I(b.INVALID_ARGUMENT,e+` and ${r} cannot be used together.`)}function ze(e){if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function Ke(e){if(x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function Ge(e){return"object"==typeof e&&null!==e&&(Object.getPrototypeOf(e)===Object.prototype||null===Object.getPrototypeOf(e))}function $e(e){var t,r;return void 0===e?"undefined":null===e?"null":"string"==typeof e?(20<e.length&&(e=e.substring(0,20)+"..."),JSON.stringify(e)):"number"==typeof e||"boolean"==typeof e?""+e:"object"==typeof e?e instanceof Array?"an array":(t=(r=e).constructor?r.constructor.name:null)?`a custom ${t} object`:"an object":"function"==typeof e?"a function":E(12329,{type:typeof e})}function g(e,t){if((e="_delegate"in e?e._delegate:e)instanceof t)return e;if(t.name===e.constructor.name)throw new I(b.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");var r=$e(e);throw new I(b.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: `+r)}function Qe(e,t){if(t<=0)throw new I(b.INVALID_ARGUMENT,`Function ${e}() requires a positive number, but it was: ${t}.`)}function t(e,t){var r={typeString:e};return t&&(r.value=t),r}function He(e,t){if(!Ge(e))throw new I(b.INVALID_ARGUMENT,"JSON must be an object");let r;for(var n in t)if(t[n]){var i=t[n].typeString,s="value"in t[n]?{value:t[n].value}:void 0;if(!(n in e)){r=`JSON missing required field: '${n}'`;break}var a=e[n];if(i&&typeof a!==i){r=`JSON field '${n}' must be a ${i}.`;break}if(void 0!==s&&a!==s.value){r=`Expected '${n}' field to equal '${s.value}'`;break}}if(r)throw new I(b.INVALID_ARGUMENT,r);return 1}let We=-62135596800;class v{static now(){return v.fromMillis(Date.now())}static fromDate(e){return v.fromMillis(e.getTime())}static fromMillis(e){var t=Math.floor(e/1e3),r=Math.floor(1e6*(e-1e3*t));return new v(t,r)}constructor(e,t){if(this.seconds=e,(this.nanoseconds=t)<0)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(1e9<=t)throw new I(b.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<We)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e);if(253402300800<=e)throw new I(b.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?S(this.nanoseconds,e.nanoseconds):S(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{type:v._jsonSchemaVersion,seconds:this.seconds,nanoseconds:this.nanoseconds}}static fromJSON(e){if(He(e,v._jsonSchema))return new v(e.seconds,e.nanoseconds)}valueOf(){var e=this.seconds-We;return String(e).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}v._jsonSchemaVersion="firestore/timestamp/1.0",v._jsonSchema={type:t("string",v._jsonSchemaVersion),seconds:t("number"),nanoseconds:t("number")};class w{static fromTimestamp(e){return new w(e)}static min(){return new w(new v(0,0))}static max(){return new w(new v(253402300799,999999999))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}let Xe=-1;class Ye{constructor(e,t,r,n){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=n}}function Je(e){return e.fields.find(e=>2===e.kind)}function Ze(e){return e.fields.filter(e=>2!==e.kind)}Ye.UNKNOWN_ID=-1;class et{constructor(e,t){this.fieldPath=e,this.kind=t}}class tt{constructor(e,t){this.sequenceNumber=e,this.offset=t}static empty(){return new tt(0,it.min())}}function rt(e,t){var r=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1,r=w.fromTimestamp(1e9===n?new v(r+1,0):new v(r,n));return new it(r,x.empty(),t)}function nt(e){return new it(e.readTime,e.key,Xe)}class it{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new it(w.min(),x.empty(),Xe)}static max(){return new it(w.max(),x.empty(),Xe)}}function st(e,t){var r=e.readTime.compareTo(t.readTime);return 0!==r||0!==(r=x.comparator(e.documentKey,t.documentKey))?r:S(e.largestBatchId,t.largestBatchId)}let at="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";class ot{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function ut(e){if(e.code!==b.FAILED_PRECONDITION||e.message!==at)throw e;p("LocalStore","Unexpectedly lost primary lease")}class C{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(n,i){return this.callbackAttached&&E(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(i,this.error):this.wrapSuccess(n,this.result):new C((t,r)=>{this.nextCallback=e=>{this.wrapSuccess(n,e).next(t,r)},this.catchCallback=e=>{this.wrapFailure(i,e).next(t,r)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{var t=e();return t instanceof C?t:C.resolve(t)}catch(e){return C.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):C.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):C.reject(t)}static resolve(r){return new C((e,t)=>{e(r)})}static reject(r){return new C((e,t)=>{t(r)})}static waitFor(e){return new C((t,r)=>{let n=0,i=0,s=!1;e.forEach(e=>{++n,e.next(()=>{++i,s&&i===n&&t()},e=>r(e))}),s=!0,i===n&&t()})}static or(e){let t=C.resolve(!1);for(let r of e)t=t.next(e=>e?C.resolve(e):r());return t}static forEach(e,r){let n=[];return e.forEach((e,t)=>{n.push(r.call(this,e,t))}),this.waitFor(n)}static mapArray(o,u){return new C((r,n)=>{let i=o.length,s=new Array(i),a=0;for(let e=0;e<i;e++){let t=e;u(o[t]).next(e=>{s[t]=e,++a===i&&r(s)},e=>n(e))}})}static doWhile(n,i){return new C((e,t)=>{let r=()=>{!0===n()?i().next(()=>{r()},t):e()};r()})}}let lt="SimpleDb";class ht{static open(e,t,r,n){try{return new ht(t,e.transaction(n,r))}catch(e){throw new gt(t,e)}}constructor(r,e){this.action=r,this.transaction=e,this.aborted=!1,this.S=new f,this.transaction.oncomplete=()=>{this.S.resolve()},this.transaction.onabort=()=>{e.error?this.S.reject(new gt(r,e.error)):this.S.resolve()},this.transaction.onerror=e=>{var t=wt(e.target.error);this.S.reject(new gt(r,t))}}get D(){return this.S.promise}abort(e){e&&this.S.reject(e),this.aborted||(p(lt,"Aborting transaction:",e?e.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())}v(){var e=this.transaction;this.aborted||"function"!=typeof e.commit||e.commit()}store(e){var t=this.transaction.objectStore(e);return new pt(t)}}class ct{static delete(e){return p(lt,"Removing database:",e),yt(G().indexedDB.deleteDatabase(e)).toPromise()}static C(){var e,t,r;return!(!(()=>{try{return"object"==typeof indexedDB}catch(e){}})()||!ct.F()&&(e=ee(),t=0<(t=ct.M(e))&&t<10,r=0<(r=dt(e))&&r<4.5,0<e.indexOf("MSIE ")||0<e.indexOf("Trident/")||0<e.indexOf("Edge/")||t||r))}static F(){var e;return"undefined"!=typeof process&&"YES"===(null==(e=process.__PRIVATE_env)?void 0:e.O)}static N(e,t){return e.store(t)}static M(e){var t=e.match(/i(?:phone|pad|pod) os ([\d_]+)/i),t=t?t[1].split("_").slice(0,2).join("."):"-1";return Number(t)}constructor(e,t,r){this.name=e,this.version=t,this.B=r,this.L=null,12.2===ct.M(ee())&&d("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}async k(s){return this.db||(p(lt,"Opening database:",this.name),this.db=await new Promise((r,n)=>{let i=indexedDB.open(this.name,this.version);i.onsuccess=e=>{var t=e.target.result;r(t)},i.onblocked=()=>{n(new gt(s,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},i.onerror=e=>{var t=e.target.error;"VersionError"===t.name?n(new I(b.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):"InvalidStateError"===t.name?n(new I(b.FAILED_PRECONDITION,"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: "+t)):n(new gt(s,t))},i.onupgradeneeded=e=>{p(lt,'Database "'+this.name+'" requires upgrade from version:',e.oldVersion);var t=e.target.result;if(null!==this.L&&this.L!==e.oldVersion)throw new Error(`refusing to open IndexedDB database due to potential corruption of the IndexedDB database data; this corruption could be caused by clicking the "clear site data" button in a web browser; try reloading the web page to re-initialize the IndexedDB database: lastClosedDbVersion=${this.L}, event.oldVersion=${e.oldVersion}, event.newVersion=${e.newVersion}, db.version=`+t.version);this.B.q(t,i.transaction,e.oldVersion,this.version).next(()=>{p(lt,"Database upgrade to version "+this.version+" complete")})}}),this.db.addEventListener("close",e=>{var t=e.target;this.L=t.version},{passive:!0})),this.db.addEventListener("versionchange",e=>{var t;null===e.newVersion&&(be('Received "versionchange" event with newVersion===null; notifying the registered DatabaseDeletedListener, if any'),null!=(t=this.databaseDeletedListener))&&t.call(this)},{passive:!0}),this.db}setDatabaseDeletedListener(e){if(this.databaseDeletedListener)throw new Error("setDatabaseDeletedListener() may only be called once, and it has already been called");this.databaseDeletedListener=e}async runTransaction(r,e,n,i){var s="readonly"===e;let a=0;for(;;){++a;try{this.db=await this.k(r);let t=ht.open(this.db,r,s?"readonly":"readwrite",n),e=i(t).next(e=>(t.v(),e)).catch(e=>(t.abort(e),C.reject(e))).toPromise();return e.catch(()=>{}),await t.D,e}catch(r){let e=r,t="FirebaseError"!==e.name&&a<3;if(p(lt,"Transaction failed with error:",e.message,"Retrying:",t),this.close(),!t)return Promise.reject(e)}}}close(){this.db&&this.db.close(),this.db=void 0}}function dt(e){var t=e.match(/Android ([\d.]+)/i),t=t?t[1].split(".").slice(0,2).join("."):"-1";return Number(t)}class ft{constructor(e){this.$=e,this.U=!1,this.K=null}get isDone(){return this.U}get W(){return this.K}set cursor(e){this.$=e}done(){this.U=!0}G(e){this.K=e}delete(){return yt(this.$.delete())}}class gt extends I{constructor(e,t){super(b.UNAVAILABLE,`IndexedDB transaction '${e}' failed: `+t),this.name="IndexedDbTransactionError"}}function mt(e){return"IndexedDbTransactionError"===e.name}class pt{constructor(e){this.store=e}put(e,t){let r;return yt(r=void 0!==t?(p(lt,"PUT",this.store.name,e,t),this.store.put(t,e)):(p(lt,"PUT",this.store.name,"<auto-key>",e),this.store.put(e)))}add(e){return p(lt,"ADD",this.store.name,e,e),yt(this.store.add(e))}get(t){return yt(this.store.get(t)).next(e=>(void 0===e&&(e=null),p(lt,"GET",this.store.name,t,e),e))}delete(e){return p(lt,"DELETE",this.store.name,e),yt(this.store.delete(e))}count(){return p(lt,"COUNT",this.store.name),yt(this.store.count())}j(e,t){var n=this.options(e,t),r=n.index?this.store.index(n.index):this.store;if("function"==typeof r.getAll){let e=r.getAll(n.range);return new C((t,r)=>{e.onerror=e=>{r(e.target.error)},e.onsuccess=e=>{t(e.target.result)}})}{let e=this.cursor(n),r=[];return this.J(e,(e,t)=>{r.push(t)}).next(()=>r)}}H(e,t){let n=this.store.getAll(e,null===t?void 0:t);return new C((t,r)=>{n.onerror=e=>{r(e.target.error)},n.onsuccess=e=>{t(e.target.result)}})}Y(e,t){p(lt,"DELETE ALL",this.store.name);var r=this.options(e,t),r=(r.Z=!1,this.cursor(r));return this.J(r,(e,t,r)=>r.delete())}X(e,t){let r;t?r=e:(r={},t=e);var n=this.cursor(r);return this.J(n,t)}ee(i){let e=this.cursor({});return new C((r,n)=>{e.onerror=e=>{var t=wt(e.target.error);n(t)},e.onsuccess=e=>{let t=e.target.result;t?i(t.primaryKey,t.value).next(e=>{e?t.continue():r()}):r()}})}J(e,s){let a=[];return new C((i,t)=>{e.onerror=e=>{t(e.target.error)},e.onsuccess=e=>{var n=e.target.result;if(n){let t=new ft(n),r=s(n.primaryKey,n.value,t);if(r instanceof C){let e=r.catch(e=>(t.done(),C.reject(e)));a.push(e)}t.isDone?i():null===t.W?n.continue():n.continue(t.W)}else i()}}).next(()=>C.waitFor(a))}options(e,t){let r;return void 0!==e&&("string"==typeof e?r=e:t=e),{index:r,range:t}}cursor(e){let t="next";var r;return e.reverse&&(t="prev"),e.index?(r=this.store.index(e.index),e.Z?r.openKeyCursor(e.range,t):r.openCursor(e.range,t)):this.store.openCursor(e.range,t)}}function yt(e){return new C((r,n)=>{e.onsuccess=e=>{var t=e.target.result;r(t)},e.onerror=e=>{var t=wt(e.target.error);n(t)}})}let vt=!1;function wt(e){let t=ct.M(ee());if(12.2<=t&&t<13){let t="An internal error was encountered in the Indexed Database server";if(0<=e.message.indexOf(t)){let e=new I("internal",`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`);return vt||(vt=!0,setTimeout(()=>{throw e},0)),e}}return e}let _t="IndexBackfiller";class bt{constructor(e,t){this.asyncQueue=e,this.te=t,this.task=null}start(){this.ne(15e3)}stop(){this.task&&(this.task.cancel(),this.task=null)}get started(){return null!==this.task}ne(e){p(_t,`Scheduled in ${e}ms`),this.task=this.asyncQueue.enqueueAfterDelay("index_backfill",e,async()=>{this.task=null;try{var e=await this.te.re();p(_t,"Documents written: "+e)}catch(e){mt(e)?p(_t,"Ignoring IndexedDB error during index backfill: ",e):await ut(e)}await this.ne(6e4)})}}class It{constructor(e,t){this.localStore=e,this.persistence=t}async re(t=50){return this.persistence.runTransaction("Backfill Indexes","readwrite-primary",e=>this.ie(e,t))}ie(e,t){let r=new Set,n=t,i=!0;return C.doWhile(()=>!0===i&&0<n,()=>this.localStore.indexManager.getNextCollectionGroupToUpdate(e).next(t=>{if(null!==t&&!r.has(t))return p(_t,"Processing collection: "+t),this.se(e,t,n).next(e=>{n-=e,r.add(t)});i=!1})).next(()=>t-n)}se(n,i,e){return this.localStore.indexManager.getMinOffsetFromCollectionGroup(n,i).next(r=>this.localStore.localDocuments.getNextDocuments(n,i,r,e).next(e=>{let t=e.changes;return this.localStore.indexManager.updateIndexEntries(n,t).next(()=>this.oe(r,e)).next(e=>(p(_t,"Updating offset: "+e),this.localStore.indexManager.updateCollectionGroup(n,i,e))).next(()=>t.size)}))}oe(e,t){let n=e;return t.changes.forEach((e,t)=>{var r=nt(t);0<st(r,n)&&(n=r)}),new it(n.readTime,n.documentKey,Math.max(t.batchId,e.largestBatchId))}}class Tt{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this._e(e),this.ae=e=>t.writeSequenceNumber(e))}_e(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){var e=++this.previousValue;return this.ae&&this.ae(e),e}}let Et=Tt.ue=-1;function St(e){return null==e}function xt(e){return 0===e&&1/e==-1/0}function Ct(e){return"number"==typeof e&&Number.isInteger(e)&&!xt(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}let At="";function Dt(e){let t="";for(let r=0;r<e.length;r++)0<t.length&&(t=Nt(t)),t=((t,e)=>{let r=e,n=t.length;for(let i=0;i<n;i++){let e=t.charAt(i);switch(e){case"\0":r+="";break;case At:r+="";break;default:r+=e}}return r})(e.get(r),t);return Nt(t)}function Nt(e){return e+At+""}function kt(r){let e=r.length;if(y(2<=e,64408,{path:r}),2===e)return y(r.charAt(0)===At&&""===r.charAt(1),56145,{path:r}),T.emptyPath();var __PRIVATE_lastReasonableEscapeIndex=e-2,n=[];let i="";for(let a=0;a<e;){let t=r.indexOf(At,a);switch((t<0||t>__PRIVATE_lastReasonableEscapeIndex)&&E(50515,{path:r}),r.charAt(t+1)){case"":var s=r.substring(a,t);let e;0===i.length?e=s:(i+=s,e=i,i=""),n.push(e);break;case"":i=i+r.substring(a,t)+"\0";break;case"":i+=r.substring(a,t+1);break;default:E(61167,{path:r})}a=t+2}return new T(n)}let Rt="remoteDocuments",Ot="owner",Lt="owner",Ft="mutationQueues",Vt="mutations",Mt="batchId",Pt="userMutationsIndex",Ut=["userId","batchId"];function Bt(e,t){return[e,Dt(t)]}function qt(e,t,r){return[e,Dt(t),r]}let jt={},zt="documentMutations",Kt="remoteDocumentsV14",Gt=["prefixPath","collectionGroup","readTime","documentId"],$t="documentKeyIndex",Qt=["prefixPath","collectionGroup","documentId"],Ht="collectionGroupIndex",Wt=["collectionGroup","readTime","prefixPath","documentId"],Xt="remoteDocumentGlobal",Yt="remoteDocumentGlobalKey",Jt="targets",Zt="queryTargetsIndex",er=["canonicalId","targetId"],tr="targetDocuments",rr=["targetId","path"],nr="documentTargetsIndex",ir=["path","targetId"],sr="targetGlobalKey",ar="targetGlobal",or="collectionParents",ur=["collectionId","parent"],lr="clientMetadata",hr="bundles",cr="namedQueries",dr="indexConfiguration",fr="collectionGroupIndex",gr="indexState",mr=["indexId","uid"],Sr="sequenceNumberIndex",xr=["uid","sequenceNumber"],Cr="indexEntries",Ar=["indexId","uid","arrayValue","directionalValue","orderedDocumentKey","documentKey"],Dr="documentKeyIndex",Nr=["indexId","uid","orderedDocumentKey"],kr="documentOverlays",Rr=["userId","collectionPath","documentId"],Or="collectionPathOverlayIndex",Lr=["userId","collectionPath","largestBatchId"],Fr="collectionGroupOverlayIndex",Vr=["userId","collectionGroup","largestBatchId"],Mr="globals",Pr=[Ft,Vt,zt,Rt,Jt,Ot,ar,tr,lr,Xt,or,hr,cr],Ur=[...Pr,kr],Br=[Ft,Vt,zt,Kt,Jt,Ot,ar,tr,lr,Xt,or,hr,cr,kr],qr=Br,jr=[...qr,dr,gr,Cr],zr=jr,Kr=[...jr,Mr],Gr=Kr;class $r extends ot{constructor(e,t){super(),this.ce=e,this.currentSequenceNumber=t}}function r(e,t){var r=e;return ct.N(r.ce,t)}function Qr(e){let t=0;for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}function Hr(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t(r,e[r])}function Wr(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class A{constructor(e,t){this.comparator=e,this.root=t||Yr.EMPTY}insert(e,t){return new A(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,Yr.BLACK,null,null))}remove(e){return new A(this.comparator,this.root.remove(e,this.comparator).copy(null,null,Yr.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){var r=this.comparator(e,t.key);if(0===r)return t.value;r<0?t=t.left:0<r&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){var n=this.comparator(e,r.key);if(0===n)return t+r.left.size;r=n<0?r.left:(t+=r.left.size+1,r.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(r){this.inorderTraversal((e,t)=>(r(e,t),!1))}toString(){let r=[];return this.inorderTraversal((e,t)=>(r.push(e+":"+t),!1)),`{${r.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new Xr(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new Xr(this.root,e,this.comparator,!1)}getReverseIterator(){return new Xr(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new Xr(this.root,e,this.comparator,!0)}}class Xr{constructor(e,t,r,n){this.isReverse=n,this.nodeStack=[];let i=1;for(;!e.isEmpty();)if(i=t?r(e.key,t):1,t&&n&&(i*=-1),i<0)e=this.isReverse?e.left:e.right;else{if(0===i){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop();var t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return 0<this.nodeStack.length}peek(){var e;return 0===this.nodeStack.length?null:{key:(e=this.nodeStack[this.nodeStack.length-1]).key,value:e.value}}}class Yr{constructor(e,t,r,n,i){this.key=e,this.value=t,this.color=null!=r?r:Yr.RED,this.left=null!=n?n:Yr.EMPTY,this.right=null!=i?i:Yr.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,n,i){return new Yr(null!=e?e:this.key,null!=t?t:this.value,null!=r?r:this.color,null!=n?n:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){var n=this,i=r(e,n.key);return(n=i<0?n.copy(null,null,null,n.left.insert(e,t,r),null):0===i?n.copy(null,t,null,null,null):n.copy(null,null,null,null,n.right.insert(e,t,r))).fixUp()}removeMin(){if(this.left.isEmpty())return Yr.EMPTY;let e=this;return(e=(e=e.left.isRed()||e.left.left.isRed()?e:e.moveRedLeft()).copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let r,n=this;if(t(e,n.key)<0)n=(n=n.left.isEmpty()||n.left.isRed()||n.left.left.isRed()?n:n.moveRedLeft()).copy(null,null,null,n.left.remove(e,t),null);else{if(0===t(e,(n=(n=n.left.isRed()?n.rotateRight():n).right.isEmpty()||n.right.isRed()||n.right.left.isRed()?n:n.moveRedRight()).key)){if(n.right.isEmpty())return Yr.EMPTY;r=n.right.min(),n=n.copy(r.key,r.value,null,null,n.right.removeMin())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e=(e=(e=e.right.isRed()&&!e.left.isRed()?e.rotateLeft():e).left.isRed()&&e.left.left.isRed()?e.rotateRight():e).left.isRed()&&e.right.isRed()?e.colorFlip():e}moveRedLeft(){let e=this.colorFlip();return e=e.right.left.isRed()?(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip():e}moveRedRight(){let e=this.colorFlip();return e=e.left.left.isRed()?(e=e.rotateRight()).colorFlip():e}rotateLeft(){var e=this.copy(null,null,Yr.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){var e=this.copy(null,null,Yr.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){var e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){var e=this.check();return Math.pow(2,e)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw E(43730,{key:this.key,value:this.value});if(this.right.isRed())throw E(14113,{key:this.key,value:this.value});var e=this.left.check();if(e!==this.right.check())throw E(27949);return e+(this.isRed()?0:1)}}Yr.EMPTY=null,Yr.RED=!0,Yr.BLACK=!1,Yr.EMPTY=new class{constructor(){this.size=0}get key(){throw E(57766)}get value(){throw E(16141)}get color(){throw E(16727)}get left(){throw E(29726)}get right(){throw E(36894)}copy(e,t,r,n,i){return this}insert(e,t,r){return new Yr(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class D{constructor(e){this.comparator=e,this.data=new A(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(r){this.data.inorderTraversal((e,t)=>(r(e),!1))}forEachInRange(e,t){for(var r=this.data.getIteratorFrom(e[0]);r.hasNext();){var n=r.getNext();if(0<=this.comparator(n.key,e[1]))return;t(n.key)}}forEachWhile(e,t){for(var r=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){var t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new Jr(this.data.getIterator())}getIteratorFrom(e){return new Jr(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof D))return!1;if(this.size!==e.size)return!1;for(var r=this.data.getIterator(),n=e.data.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(0!==this.comparator(e,t))return!1}return!0}toArray(){let t=[];return this.forEach(e=>{t.push(e)}),t}toString(){let t=[];return this.forEach(e=>t.push(e)),"SortedSet("+t.toString()+")"}copy(e){var t=new D(this.comparator);return t.data=e,t}}class Jr{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}function Zr(e){return e.hasNext()?e.getNext():void 0}class en{constructor(e){(this.fields=e).sort(h.comparator)}static empty(){return new en([])}unionWith(e){let t=new D(h.comparator);for(let e of this.fields)t=t.add(e);for(var r of e)t=t.add(r);return new en(t.toArray())}covers(e){for(var t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return Ve(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class tn extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class N{constructor(e){this.binaryString=e}static fromBase64String(e){var t=(e=>{try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new tn("Invalid base64 string: "+e):e}})(e);return new N(t)}static fromUint8Array(e){var t=(e=>{let t="";for(let r=0;r<e.length;++r)t+=String.fromCharCode(e[r]);return t})(e);return new N(t)}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return e=this.binaryString,btoa(e);var e}toUint8Array(){var e=this.binaryString,t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return S(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}N.EMPTY_BYTE_STRING=new N("");let rn=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function nn(t){if(y(!!t,39018),"string"!=typeof t)return{seconds:k(t.seconds),nanos:k(t.nanos)};{let e=0;var r=rn.exec(t),r=(y(!!r,46558,{timestamp:t}),r[1]&&(r=((r=r[1])+"000000000").substr(0,9),e=Number(r)),new Date(t));return{seconds:Math.floor(r.getTime()/1e3),nanos:e}}}function k(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function sn(e){return"string"==typeof e?N.fromBase64String(e):N.fromUint8Array(e)}let an="server_timestamp",on="__type__",un="__previous_value__",ln="__local_write_time__";function hn(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[on])?void 0:t.stringValue)===an}function cn(e){var t=e.mapValue.fields[un];return hn(t)?cn(t):t}function dn(e){var t=nn(e.mapValue.fields[ln].timestampValue);return new v(t.seconds,t.nanos)}class fn{constructor(e,t,r,n,i,s,a,o,u,l){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=n,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=u,this.isUsingEmulator=l}}let gn="(default)";class mn{constructor(e,t){this.projectId=e,this.database=t||gn}static empty(){return new mn("","")}get isDefaultDatabase(){return this.database===gn}isEqual(e){return e instanceof mn&&e.projectId===this.projectId&&e.database===this.database}}let pn="__type__",yn="__max__",vn={mapValue:{fields:{__type__:{stringValue:yn}}}},wn="__vector__",_n="value",bn={nullValue:"NULL_VALUE"};function In(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?hn(e)?4:Mn(e)?9007199254740991:Fn(e)?10:11:E(28295,{value:e})}function Tn(t,r){if(t===r)return!0;var n,i,s,a,o,e=In(t);if(e!==In(r))return!1;switch(e){case 0:case 9007199254740991:return!0;case 1:return t.booleanValue===r.booleanValue;case 4:return dn(t).isEqual(dn(r));case 3:return s=r,"string"==typeof(i=t).timestampValue&&"string"==typeof s.timestampValue&&i.timestampValue.length===s.timestampValue.length?i.timestampValue===s.timestampValue:(a=nn(i.timestampValue),o=nn(s.timestampValue),a.seconds===o.seconds&&a.nanos===o.nanos);case 5:return t.stringValue===r.stringValue;case 6:return i=r,sn(t.bytesValue).isEqual(sn(i.bytesValue));case 7:return t.referenceValue===r.referenceValue;case 8:return s=r,k((n=t).geoPointValue.latitude)===k(s.geoPointValue.latitude)&&k(n.geoPointValue.longitude)===k(s.geoPointValue.longitude);case 2:return n=r,"integerValue"in(l=t)&&"integerValue"in n?k(l.integerValue)===k(n.integerValue):"doubleValue"in l&&"doubleValue"in n&&((a=k(l.doubleValue))===(o=k(n.doubleValue))?xt(a)===xt(o):isNaN(a)&&isNaN(o));case 9:return Ve(t.arrayValue.values||[],r.arrayValue.values||[],Tn);case 10:case 11:var u=t,l=r,h=u.mapValue.fields||{},c=l.mapValue.fields||{};if(Qr(h)!==Qr(c))return!1;for(let e in h)if(h.hasOwnProperty(e)&&(void 0===c[e]||!Tn(h[e],c[e])))return!1;return!0;default:return E(52216,{left:t})}}function En(e,t){return void 0!==(e.values||[]).find(e=>Tn(e,t))}function Sn(e,n){if(e===n)return 0;var i,s,a,o,u,l,h,c,d=In(e),t=In(n);if(d!==t)return S(d,t);switch(d){case 0:case 9007199254740991:return 0;case 1:return S(e.booleanValue,n.booleanValue);case 2:return l=n,h=k((u=e).integerValue||u.doubleValue),c=k(l.integerValue||l.doubleValue),h<c?-1:c<h?1:h===c?0:isNaN(h)?isNaN(c)?0:-1:1;case 3:return xn(e.timestampValue,n.timestampValue);case 4:return xn(dn(e),dn(n));case 5:return Le(e.stringValue,n.stringValue);case 6:return u=e.bytesValue,l=n.bytesValue,h=sn(u),c=sn(l),h.compareTo(c);case 7:var f=e.referenceValue,g=n.referenceValue,m=f.split("/"),p=g.split("/");for(let t=0;t<m.length&&t<p.length;t++){let e=S(m[t],p[t]);if(0!==e)return e}return S(m.length,p.length);case 8:return f=e.geoPointValue,g=n.geoPointValue,0!==(o=S(k(f.latitude),k(g.latitude)))?o:S(k(f.longitude),k(g.longitude));case 9:return Cn(e.arrayValue,n.arrayValue);case 10:return y=e.mapValue,i=n.mapValue,o=y.fields||{},s=i.fields||{},o=null==(o=o[_n])?void 0:o.arrayValue,s=null==(s=s[_n])?void 0:s.arrayValue,0!==(a=S((null==(a=null==o?void 0:o.values)?void 0:a.length)||0,(null==(a=null==s?void 0:s.values)?void 0:a.length)||0))?a:Cn(o,s);case 11:var y=e.mapValue,v=n.mapValue;if(y===vn.mapValue&&v===vn.mapValue)return 0;if(y===vn.mapValue)return 1;if(v===vn.mapValue)return-1;var w=y.fields||{},_=Object.keys(w),b=v.fields||{},I=Object.keys(b);_.sort(),I.sort();for(let r=0;r<_.length&&r<I.length;++r){let e=Le(_[r],I[r]);if(0!==e)return e;var T=Sn(w[_[r]],b[I[r]]);if(0!==T)return T}return S(_.length,I.length);default:throw E(23264,{le:d})}}function xn(e,t){var r,n,i;return"string"==typeof e&&"string"==typeof t&&e.length===t.length?S(e,t):(r=nn(e),n=nn(t),0!==(i=S(r.seconds,n.seconds))?i:S(r.nanos,n.nanos))}function Cn(e,t){var r=e.values||[],n=t.values||[];for(let i=0;i<r.length&&i<n.length;++i){let e=Sn(r[i],n[i]);if(e)return e}return S(r.length,n.length)}function An(e){return function s(e){return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?(e=>{let t=nn(e);return`time(${t.seconds},${t.nanos})`})(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?(e=>sn(e).toBase64())(e.bytesValue):"referenceValue"in e?(e=>x.fromName(e).toString())(e.referenceValue):"geoPointValue"in e?(e=>`geo(${e.latitude},${e.longitude})`)(e.geoPointValue):"arrayValue"in e?(e=>{let t="[",r=!0;for(var n of e.values||[])r?r=!1:t+=",",t+=s(n);return t+"]"})(e.arrayValue):"mapValue"in e?(e=>{let t=Object.keys(e.fields||{}).sort(),r="{",n=!0;for(var i of t)n?n=!1:r+=",",r+=i+":"+s(e.fields[i]);return r+"}"})(e.mapValue):E(61005,{value:e})}(e)}function Dn(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/`+t.path.canonicalString()}}function Nn(e){return!!e&&"integerValue"in e}function kn(e){return!!e&&"arrayValue"in e}function Rn(e){return e&&"nullValue"in e}function On(e){return e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function Ln(e){return e&&"mapValue"in e}function Fn(e){var t;return(null==(t=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[pn])?void 0:t.stringValue)===wn}function Vn(t){if(t.geoPointValue)return{geoPointValue:Object.assign({},t.geoPointValue)};if(t.timestampValue&&"object"==typeof t.timestampValue)return{timestampValue:Object.assign({},t.timestampValue)};if(t.mapValue){let r={mapValue:{fields:{}}};return Hr(t.mapValue.fields,(e,t)=>r.mapValue.fields[e]=Vn(t)),r}if(t.arrayValue){var r={arrayValue:{values:[]}};for(let e=0;e<(t.arrayValue.values||[]).length;++e)r.arrayValue.values[e]=Vn(t.arrayValue.values[e]);return r}return Object.assign({},t)}function Mn(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===yn}let Pn={mapValue:{fields:{[pn]:{stringValue:wn},[_n]:{arrayValue:{}}}}};function Un(e,t){var r=Sn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?-1:!e.inclusive&&t.inclusive?1:0}function Bn(e,t){var r=Sn(e.value,t.value);return 0!==r?r:e.inclusive&&!t.inclusive?1:!e.inclusive&&t.inclusive?-1:0}class qn{constructor(e){this.value=e}static empty(){return new qn({mapValue:{}})}field(r){if(r.isEmpty())return this.value;{let e=this.value;for(let t=0;t<r.length-1;++t)if(!Ln(e=(e.mapValue.fields||{})[r.get(t)]))return null;return(e=(e.mapValue.fields||{})[r.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=Vn(t)}setAll(e){let r=h.emptyPath(),n={},i=[];e.forEach((e,t)=>{if(!r.isImmediateParentOf(t)){let e=this.getFieldsMap(r);this.applyChanges(e,n,i),n={},i=[],r=t.popLast()}e?n[t.lastSegment()]=Vn(e):i.push(t.lastSegment())});var t=this.getFieldsMap(r);this.applyChanges(t,n,i)}delete(e){var t=this.field(e.popLast());Ln(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return Tn(this.value,e.value)}getFieldsMap(t){let r=this.value;r.mapValue.fields||(r.mapValue={fields:{}});for(let n=0;n<t.length;++n){let e=r.mapValue.fields[t.get(n)];Ln(e)&&e.mapValue.fields||(e={mapValue:{fields:{}}},r.mapValue.fields[t.get(n)]=e),r=e}return r.mapValue.fields}applyChanges(r,e,t){Hr(e,(e,t)=>r[e]=t);for(let e of t)delete r[e]}clone(){return new qn(Vn(this.value))}}class R{constructor(e,t,r,n,i,s,a){this.key=e,this.documentType=t,this.version=r,this.readTime=n,this.createTime=i,this.data=s,this.documentState=a}static newInvalidDocument(e){return new R(e,0,w.min(),w.min(),w.min(),qn.empty(),0)}static newFoundDocument(e,t,r,n){return new R(e,1,t,w.min(),r,n,0)}static newNoDocument(e,t){return new R(e,2,t,w.min(),w.min(),qn.empty(),0)}static newUnknownDocument(e,t){return new R(e,3,t,w.min(),w.min(),qn.empty(),2)}convertToFoundDocument(e,t){return!this.createTime.isEqual(w.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=qn.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=qn.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=w.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof R&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new R(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class jn{constructor(e,t){this.position=e,this.inclusive=t}}function zn(e,t,r){let n=0;for(let a=0;a<e.position.length;a++){var i=t[a],s=e.position[a];if(n=i.field.isKeyField()?x.comparator(x.fromName(s.referenceValue),r.key):Sn(s,r.data.field(i.field)),"desc"===i.dir&&(n*=-1),0!==n)break}return n}function Kn(e,t){if(null===e)return null===t;if(null===t)return!1;if(e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let r=0;r<e.position.length;r++)if(!Tn(e.position[r],t.position[r]))return!1;return!0}class Gn{constructor(e,t="asc"){this.field=e,this.dir=t}}class $n{}class O extends $n{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,r):new Zn(e,t,r):"array-contains"===t?new ni(e,r):"in"===t?new ii(e,r):"not-in"===t?new si(e,r):"array-contains-any"===t?new ai(e,r):new O(e,t,r)}static createKeyFieldInFilter(e,t,r){return new("in"===t?ei:ti)(e,r)}matches(e){var t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(Sn(t,this.value)):null!==t&&In(this.value)===In(t)&&this.matchesComparison(Sn(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return 0<e;case">=":return 0<=e;default:return E(47266,{operator:this.op})}}isInequality(){return 0<=["<","<=",">",">=","!=","not-in"].indexOf(this.op)}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class L extends $n{constructor(e,t){super(),this.filters=e,this.op=t,this.he=null}static create(e,t){return new L(e,t)}matches(t){return Qn(this)?void 0===this.filters.find(e=>!e.matches(t)):void 0!==this.filters.find(e=>e.matches(t))}getFlattenedFilters(){return null===this.he&&(this.he=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.he}getFilters(){return Object.assign([],this.filters)}}function Qn(e){return"and"===e.op}function Hn(e){return"or"===e.op}function Wn(e){return Xn(e)&&Qn(e)}function Xn(e){for(var t of e.filters)if(t instanceof L)return!1;return!0}function Yn(e,t){var r=e.filters.concat(t);return L.create(r,e.op)}function Jn(e){return e instanceof O?`${(t=e).field.canonicalString()} ${t.op} `+An(t.value):e instanceof L?(t=e).op.toString()+" {"+t.getFilters().map(Jn).join(" ,")+"}":"Filter";var t}class Zn extends O{constructor(e,t,r){super(e,t,r),this.key=x.fromName(r.referenceValue)}matches(e){var t=x.comparator(e.key,this.key);return this.matchesComparison(t)}}class ei extends O{constructor(e,t){super(e,"in",t),this.keys=ri(0,t)}matches(t){return this.keys.some(e=>e.isEqual(t.key))}}class ti extends O{constructor(e,t){super(e,"not-in",t),this.keys=ri(0,t)}matches(t){return!this.keys.some(e=>e.isEqual(t.key))}}function ri(e,t){var r;return((null==(r=t.arrayValue)?void 0:r.values)||[]).map(e=>x.fromName(e.referenceValue))}class ni extends O{constructor(e,t){super(e,"array-contains",t)}matches(e){var t=e.data.field(this.field);return kn(t)&&En(t.arrayValue,this.value)}}class ii extends O{constructor(e,t){super(e,"in",t)}matches(e){var t=e.data.field(this.field);return null!==t&&En(this.value.arrayValue,t)}}class si extends O{constructor(e,t){super(e,"not-in",t)}matches(e){var t;return!En(this.value.arrayValue,{nullValue:"NULL_VALUE"})&&null!==(t=e.data.field(this.field))&&void 0===t.nullValue&&!En(this.value.arrayValue,t)}}class ai extends O{constructor(e,t){super(e,"array-contains-any",t)}matches(e){var t=e.data.field(this.field);return!(!kn(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>En(this.value.arrayValue,e))}}class oi{constructor(e,t=null,r=[],n=[],i=null,s=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=n,this.limit=i,this.startAt=s,this.endAt=a,this.Pe=null}}function ui(e,t=null,r=[],n=[],i=null,s=null,a=null){return new oi(e,t,r,n,i,s,a)}function li(e){var t=e;if(null===t.Pe){let e=t.path.canonicalString();null!==t.collectionGroup&&(e+="|cg:"+t.collectionGroup),e=(e=(e+="|f:")+t.filters.map(e=>function t(e){var r;return e instanceof O?e.field.canonicalString()+e.op.toString()+An(e.value):Wn(e)?e.filters.map(e=>t(e)).join(","):(r=e.filters.map(e=>t(e)).join(","),e.op+`(${r})`)}(e)).join(",")+"|ob:")+t.orderBy.map(e=>(e=e).field.canonicalString()+e.dir).join(","),St(t.limit)||(e=(e+="|l:")+t.limit),t.startAt&&(e=(e=(e+="|lb:")+(t.startAt.inclusive?"b:":"a:"))+t.startAt.position.map(e=>An(e)).join(",")),t.endAt&&(e=(e=(e+="|ub:")+(t.endAt.inclusive?"a:":"b:"))+t.endAt.position.map(e=>An(e)).join(",")),t.Pe=e}return t.Pe}function hi(e,t){if(e.limit!==t.limit)return!1;if(e.orderBy.length!==t.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++)if(r=e.orderBy[i],n=t.orderBy[i],r.dir!==n.dir||!r.field.isEqual(n.field))return!1;var r,n;if(e.filters.length!==t.filters.length)return!1;for(let s=0;s<e.filters.length;s++)if(!function n(e,t){return e instanceof O?(r=e,(s=t)instanceof O&&r.op===s.op&&r.field.isEqual(s.field)&&Tn(r.value,s.value)):e instanceof L?(r=e,(i=t)instanceof L&&r.op===i.op&&r.filters.length===i.filters.length&&r.filters.reduce((e,t,r)=>e&&n(t,i.filters[r]),!0)):void E(19439);var i,r,s}(e.filters[s],t.filters[s]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!Kn(e.startAt,t.startAt)&&Kn(e.endAt,t.endAt)}function ci(e){return x.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}function di(e,t){return e.filters.filter(e=>e instanceof O&&e.field.isEqual(t))}function fi(e,r,n){let i=bn,s=!0;for(let n of di(e,r)){let e=bn,t=!0;switch(n.op){case"<":case"<=":e="nullValue"in(a=n.value)?bn:"booleanValue"in a?{booleanValue:!1}:"integerValue"in a||"doubleValue"in a?{doubleValue:NaN}:"timestampValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"stringValue"in a?{stringValue:""}:"bytesValue"in a?{bytesValue:""}:"referenceValue"in a?Dn(mn.empty(),x.empty()):"geoPointValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"arrayValue"in a?{arrayValue:{}}:"mapValue"in a?Fn(a)?Pn:{mapValue:{}}:E(35942,{value:a});break;case"==":case"in":case">=":e=n.value;break;case">":e=n.value,t=!1;break;case"!=":case"not-in":e=bn}Un({value:i,inclusive:s},{value:e,inclusive:t})<0&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];Un({value:i,inclusive:s},{value:e,inclusive:n.inclusive})<0&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}function gi(e,r,n){let i=vn,s=!0;for(let n of di(e,r)){let e=vn,t=!0;switch(n.op){case">=":case">":e="nullValue"in(a=n.value)?{booleanValue:!1}:"booleanValue"in a?{doubleValue:NaN}:"integerValue"in a||"doubleValue"in a?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"timestampValue"in a?{stringValue:""}:"stringValue"in a?{bytesValue:""}:"bytesValue"in a?Dn(mn.empty(),x.empty()):"referenceValue"in a?{geoPointValue:{latitude:-90,longitude:-180}}:"geoPointValue"in a?{arrayValue:{}}:"arrayValue"in a?Pn:"mapValue"in a?Fn(a)?{mapValue:{}}:vn:E(61959,{value:a}),t=!1;break;case"==":case"in":case"<=":e=n.value;break;case"<":e=n.value,t=!1;break;case"!=":case"not-in":e=vn}0<Bn({value:i,inclusive:s},{value:e,inclusive:t})&&(i=e,s=t)}var a;if(null!==n)for(let t=0;t<e.orderBy.length;++t)if(e.orderBy[t].field.isEqual(r)){let e=n.position[t];0<Bn({value:i,inclusive:s},{value:e,inclusive:n.inclusive})&&(i=e,s=n.inclusive);break}return{value:i,inclusive:s}}class mi{constructor(e,t=null,r=[],n=[],i=null,s="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=n,this.limit=i,this.limitType=s,this.startAt=a,this.endAt=o,this.Te=null,this.Ie=null,this.de=null,this.startAt,this.endAt}}function pi(e,t,r,n,i,s,a,o){return new mi(e,t,r,n,i,s,a,o)}function yi(e){return new mi(e)}function vi(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function wi(e){return null!==e.collectionGroup}function _i(e){let n=e;if(null===n.Te){n.Te=[];let t=new Set;for(var i of n.explicitOrderBy)n.Te.push(i),t.add(i.field.canonicalString());let r=0<n.explicitOrderBy.length?n.explicitOrderBy[n.explicitOrderBy.length-1].dir:"asc",e=(e=>{let t=new D(h.comparator);return e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t})(n);e.forEach(e=>{t.has(e.canonicalString())||e.isKeyField()||n.Te.push(new Gn(e,r))}),t.has(h.keyField().canonicalString())||n.Te.push(new Gn(h.keyField(),r))}return n.Te}function bi(e){var t=e;return t.Ie||(t.Ie=((e,t)=>{if("F"===e.limitType)return ui(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);t=t.map(e=>{var t="desc"===e.dir?"asc":"desc";return new Gn(e.field,t)});var r=e.endAt?new jn(e.endAt.position,e.endAt.inclusive):null,n=e.startAt?new jn(e.startAt.position,e.startAt.inclusive):null;return ui(e.path,e.collectionGroup,t,e.filters,e.limit,r,n)})(t,_i(e))),t.Ie}function Ii(e,t){var r=e.filters.concat([t]);return new mi(e.path,e.collectionGroup,e.explicitOrderBy.slice(),r,e.limit,e.limitType,e.startAt,e.endAt)}function Ti(e,t,r){return new mi(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,r,e.startAt,e.endAt)}function Ei(e,t){return hi(bi(e),bi(t))&&e.limitType===t.limitType}function Si(e){return li(bi(e))+"|lt:"+e.limitType}function xi(e){return`Query(target=${(e=>{let t=e.path.canonicalString();return null!==e.collectionGroup&&(t+=" collectionGroup="+e.collectionGroup),0<e.filters.length&&(t+=`, filters: [${e.filters.map(e=>Jn(e)).join(", ")}]`),St(e.limit)||(t+=", limit: "+e.limit),0<e.orderBy.length&&(t+=`, orderBy: [${e.orderBy.map(e=>`${(e=e).field.canonicalString()} (${e.dir})`).join(", ")}]`),e.startAt&&(t=(t=(t+=", startAt: ")+(e.startAt.inclusive?"b:":"a:"))+e.startAt.position.map(e=>An(e)).join(",")),`Target(${t=e.endAt?(t=(t+=", endAt: ")+(e.endAt.inclusive?"a:":"b:"))+e.endAt.position.map(e=>An(e)).join(","):t})`})(bi(e))}; limitType=${e.limitType})`}function Ci(e,t){return t.isFoundDocument()&&(s=e,o=(a=t).key.path,null!==s.collectionGroup?a.key.hasCollectionId(s.collectionGroup)&&s.path.isPrefixOf(o):x.isDocumentKey(s.path)?s.path.isEqual(o):s.path.isImmediateParentOf(o))&&((e,t)=>{for(var r of _i(e))if(!r.field.isKeyField()&&null===t.data.field(r.field))return;return 1})(e,t)&&((e,t)=>{for(var r of e.filters)if(!r.matches(t))return;return 1})(e,t)&&(a=t,!(s=e).startAt||(n=s.startAt,r=_i(s),i=zn(n,r,a),n.inclusive?i<=0:i<0))&&(!s.endAt||(r=s.endAt,n=_i(s),i=zn(r,n,a),r.inclusive?0<=i:0<i));var r,n,i,s,a,o}function Ai(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}function Di(e){return(t,r)=>{let n=!1;for(var i of _i(e)){let e=((e,t,r)=>{var n=e.field.isKeyField()?x.comparator(t.key,r.key):((e,t,r)=>{var n=t.data.field(e),i=r.data.field(e);return null!==n&&null!==i?Sn(n,i):E(42886)})(e.field,t,r);switch(e.dir){case"asc":return n;case"desc":return-1*n;default:return E(19790,{direction:e.dir})}})(i,t,r);if(0!==e)return e;n=n||i.field.isKeyField()}return 0}}class Ni{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(r){let e=this.mapKeyFn(r),n=this.inner[e];if(void 0!==n)for(let[e,t]of n)if(this.equalsFn(e,r))return t}has(e){return void 0!==this.get(e)}set(t,r){var e=this.mapKeyFn(t),n=this.inner[e];if(void 0===n)this.inner[e]=[[t,r]];else{for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return void(n[e]=[t,r]);n.push([t,r])}this.innerSize++}delete(t){var r=this.mapKeyFn(t),n=this.inner[r];if(void 0!==n)for(let e=0;e<n.length;e++)if(this.equalsFn(n[e][0],t))return 1===n.length?delete this.inner[r]:n.splice(e,1),this.innerSize--,!0;return!1}forEach(n){Hr(this.inner,(e,t)=>{for(let[e,r]of t)n(e,r)})}isEmpty(){return Wr(this.inner)}size(){return this.innerSize}}let ki=new A(x.comparator);let Ri=new A(x.comparator);function Oi(...e){let t=Ri;for(var r of e)t=t.insert(r.key,r);return t}function Li(e){let r=Ri;return e.forEach((e,t)=>r=r.insert(e,t.overlayedDocument)),r}function Fi(){return new Ni(e=>e.toString(),(e,t)=>e.isEqual(t))}let Vi=new A(x.comparator),Mi=new D(x.comparator);function F(...e){let t=Mi;for(var r of e)t=t.add(r);return t}let Pi=new D(S);function Ui(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:xt(t)?"-0":t}}function Bi(e){return{integerValue:""+e}}function qi(e,t){return Ct(t)?Bi(t):Ui(e,t)}class ji{constructor(){this._=void 0}}function zi(e,t){return e instanceof Wi?Nn(e=t)||(e=e)&&"doubleValue"in e?t:{integerValue:0}:null}class Ki extends ji{}class Gi extends ji{constructor(e){super(),this.elements=e}}function $i(e,t){var r=Yi(t);for(let t of e.elements)r.some(e=>Tn(e,t))||r.push(t);return{arrayValue:{values:r}}}class Qi extends ji{constructor(e){super(),this.elements=e}}function Hi(e,t){let r=Yi(t);for(let t of e.elements)r=r.filter(e=>!Tn(e,t));return{arrayValue:{values:r}}}class Wi extends ji{constructor(e,t){super(),this.serializer=e,this.Ee=t}}function Xi(e){return k(e.integerValue||e.doubleValue)}function Yi(e){return kn(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class Ji{constructor(e,t){this.field=e,this.transform=t}}class Zi{constructor(e,t){this.version=e,this.transformResults=t}}class V{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new V}static exists(e){return new V(void 0,e)}static updateTime(e){return new V(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function es(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class ts{}function rs(e,r){if(!e.hasLocalMutations||r&&0===r.fields.length)return null;if(null===r)return e.isNoDocument()?new hs(e.key,V.none()):new ss(e.key,e.data,V.none());{var n,i=e.data,s=qn.empty();let t=new D(h.comparator);for(n of r.fields)if(!t.has(n)){let e=i.field(n);null===e&&1<n.length&&(n=n.popLast(),e=i.field(n)),null===e?s.delete(n):s.set(n,e),t=t.add(n)}return new as(e.key,s,new en(t.toArray()),V.none())}}function ns(e,t,r,n){return e instanceof ss?(s=t,a=r,o=n,es((i=e).precondition,s)?(u=i.value.clone(),l=ls(i.fieldTransforms,o,s),u.setAll(l),s.convertToFoundDocument(s.version,u).setHasLocalMutations(),null):a):e instanceof as?(i=t,o=r,s=n,es((a=e).precondition,i)?(l=ls(a.fieldTransforms,s,i),(u=i.data).setAll(os(a)),u.setAll(l),i.convertToFoundDocument(i.version,u).setHasLocalMutations(),null===o?null:o.unionWith(a.fieldMask.fields).unionWith(a.fieldTransforms.map(e=>e.field))):o):(n=t,t=r,es(e.precondition,n)?(n.convertToNoDocument(n.version).setHasLocalMutations(),null):t);var i,s,a,o,u,l}function is(e,t){return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(r=e.fieldTransforms,n=t.fieldTransforms,!!(void 0===r&&void 0===n||r&&n&&Ve(r,n,(e,t)=>(t=t,(e=e).field.isEqual(t.field)&&(e=e.transform,t=t.transform,e instanceof Gi&&t instanceof Gi||e instanceof Qi&&t instanceof Qi?Ve(e.elements,t.elements,Tn):e instanceof Wi&&t instanceof Wi?Tn(e.Ee,t.Ee):e instanceof Ki&&t instanceof Ki)))))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask));var r,n}class ss extends ts{constructor(e,t,r,n=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=n,this.type=0}getFieldMask(){return null}}class as extends ts{constructor(e,t,r,n,i=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=n,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function os(r){let n=new Map;return r.fieldMask.fields.forEach(e=>{var t;e.isEmpty()||(t=r.data.field(e),n.set(e,t))}),n}function us(e,t,r){var n,i,s,a=new Map;y(e.length===r.length,32656,{Ae:r.length,Re:e.length});for(let h=0;h<r.length;h++){var o=e[h],u=o.transform,l=t.data.field(o.field);a.set(o.field,(n=u,i=l,s=r[h],n instanceof Gi?$i(n,i):n instanceof Qi?Hi(n,i):s))}return a}function ls(e,r,n){var i,s,a,o,u,l,h,c=new Map;for(i of e){let e=i.transform,t=n.data.field(i.field);c.set(i.field,(s=e,a=t,o=r,h=l=u=void 0,s instanceof Ki?(o=o,l=a,h={fields:{[on]:{stringValue:an},[ln]:{timestampValue:{seconds:o.seconds,nanos:o.nanoseconds}}}},(l=l&&hn(l)?cn(l):l)&&(h.fields[un]=l),{mapValue:h}):s instanceof Gi?$i(s,a):s instanceof Qi?Hi(s,a):(h=zi(o=s,a),u=Xi(h)+Xi(o.Ee),Nn(h)&&Nn(o.Ee)?Bi(u):Ui(o.serializer,u))))}return c}class hs extends ts{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class cs extends ts{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class ds{constructor(e,t,r,n){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=n}applyToRemoteDocument(e,t){var r,n,i,s,a,o,u,l=t.mutationResults;for(let c=0;c<this.mutations.length;c++){var h=this.mutations[c];h.key.isEqual(e.key)&&(r=h,n=e,i=l[c],u=h=o=a=s=void 0,r instanceof ss?(a=n,o=i,h=(s=r).value.clone(),u=us(s.fieldTransforms,a,o.transformResults),h.setAll(u),a.convertToFoundDocument(o.version,h).setHasCommittedMutations()):r instanceof as?(s=n,a=i,es((o=r).precondition,s)?(u=us(o.fieldTransforms,s,a.transformResults),(h=s.data).setAll(os(o)),h.setAll(u),s.convertToFoundDocument(a.version,h).setHasCommittedMutations()):s.convertToUnknownDocument(a.version)):n.convertToNoDocument(i.version).setHasCommittedMutations())}}applyToLocalView(e,t){for(var r of this.baseMutations)r.key.isEqual(e.key)&&(t=ns(r,e,t,this.localWriteTime));for(var n of this.mutations)n.key.isEqual(e.key)&&(t=ns(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(n,i){let s=Fi();return this.mutations.forEach(e=>{var t=n.get(e.key),r=t.overlayedDocument,t=this.applyToLocalView(r,t.mutatedFields),t=rs(r,i.has(e.key)?null:t);null!==t&&s.set(e.key,t),r.isValidDocument()||r.convertToNoDocument(w.min())}),s}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),F())}isEqual(e){return this.batchId===e.batchId&&Ve(this.mutations,e.mutations,(e,t)=>is(e,t))&&Ve(this.baseMutations,e.baseMutations,(e,t)=>is(e,t))}}class fs{constructor(e,t,r,n){this.batch=e,this.commitVersion=t,this.mutationResults=r,this.docVersions=n}static from(e,t,r){y(e.mutations.length===r.length,58842,{Ve:e.mutations.length,me:r.length});let n=Vi;var i=e.mutations;for(let s=0;s<i.length;s++)n=n.insert(i[s].key,r[s].version);return new fs(e,t,r,n)}}class gs{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class ms{constructor(e,t){this.count=e,this.unchangedNames=t}}function ps(e){switch(e){case b.OK:return E(64938);case b.CANCELLED:case b.UNKNOWN:case b.DEADLINE_EXCEEDED:case b.RESOURCE_EXHAUSTED:case b.INTERNAL:case b.UNAVAILABLE:case b.UNAUTHENTICATED:return!1;case b.INVALID_ARGUMENT:case b.NOT_FOUND:case b.ALREADY_EXISTS:case b.PERMISSION_DENIED:case b.FAILED_PRECONDITION:case b.ABORTED:case b.OUT_OF_RANGE:case b.UNIMPLEMENTED:case b.DATA_LOSS:return!0;default:return E(15467,{code:e})}}function ys(e){if(void 0===e)return d("GRPC error has no .code"),b.UNKNOWN;switch(e){case m.OK:return b.OK;case m.CANCELLED:return b.CANCELLED;case m.UNKNOWN:return b.UNKNOWN;case m.DEADLINE_EXCEEDED:return b.DEADLINE_EXCEEDED;case m.RESOURCE_EXHAUSTED:return b.RESOURCE_EXHAUSTED;case m.INTERNAL:return b.INTERNAL;case m.UNAVAILABLE:return b.UNAVAILABLE;case m.UNAUTHENTICATED:return b.UNAUTHENTICATED;case m.INVALID_ARGUMENT:return b.INVALID_ARGUMENT;case m.NOT_FOUND:return b.NOT_FOUND;case m.ALREADY_EXISTS:return b.ALREADY_EXISTS;case m.PERMISSION_DENIED:return b.PERMISSION_DENIED;case m.FAILED_PRECONDITION:return b.FAILED_PRECONDITION;case m.ABORTED:return b.ABORTED;case m.OUT_OF_RANGE:return b.OUT_OF_RANGE;case m.UNIMPLEMENTED:return b.UNIMPLEMENTED;case m.DATA_LOSS:return b.DATA_LOSS;default:return E(39323,{code:e})}}(e=m=m||{})[e.OK=0]="OK",e[e.CANCELLED=1]="CANCELLED",e[e.UNKNOWN=2]="UNKNOWN",e[e.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",e[e.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",e[e.NOT_FOUND=5]="NOT_FOUND",e[e.ALREADY_EXISTS=6]="ALREADY_EXISTS",e[e.PERMISSION_DENIED=7]="PERMISSION_DENIED",e[e.UNAUTHENTICATED=16]="UNAUTHENTICATED",e[e.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",e[e.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",e[e.ABORTED=10]="ABORTED",e[e.OUT_OF_RANGE=11]="OUT_OF_RANGE",e[e.UNIMPLEMENTED=12]="UNIMPLEMENTED",e[e.INTERNAL=13]="INTERNAL",e[e.UNAVAILABLE=14]="UNAVAILABLE",e[e.DATA_LOSS=15]="DATA_LOSS";let vs=new ge([4294967295,4294967295],0);function ws(e){var t=Re().encode(e),r=new me;return r.update(t),new Uint8Array(r.digest())}function _s(e){var t=new DataView(e.buffer),r=t.getUint32(0,!0),n=t.getUint32(4,!0),i=t.getUint32(8,!0),t=t.getUint32(12,!0);return[new ge([r,n],0),new ge([i,t],0)]}class bs{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||8<=t)throw new Is("Invalid padding: "+t);if(r<0)throw new Is("Invalid hash count: "+r);if(0<e.length&&0===this.hashCount)throw new Is("Invalid hash count: "+r);if(0===e.length&&0!==t)throw new Is("Invalid padding when bitmap length is 0: "+t);this.fe=8*e.length-t,this.ge=ge.fromNumber(this.fe)}pe(e,t,r){let n=e.add(t.multiply(ge.fromNumber(r)));return(n=1===n.compare(vs)?new ge([n.getBits(0),n.getBits(1)],0):n).modulo(this.ge).toNumber()}ye(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.fe)return!1;let t=ws(e),[r,n]=_s(t);for(let i=0;i<this.hashCount;i++){let e=this.pe(r,n,i);if(!this.ye(e))return!1}return!0}static create(e,t,r){let n=e%8==0?0:8-e%8,i=new Uint8Array(Math.ceil(e/8)),s=new bs(i,n,t);return r.forEach(e=>s.insert(e)),s}insert(i){if(0!==this.fe){let e=ws(i),[t,r]=_s(e);for(let n=0;n<this.hashCount;n++){let e=this.pe(t,r,n);this.we(e)}}}we(e){var t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class Is extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class Ts{constructor(e,t,r,n,i){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=n,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(e,t,r){var n=new Map;return n.set(e,Es.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new Ts(w.min(),n,new A(S),ki,F())}}class Es{constructor(e,t,r,n,i){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=n,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new Es(r,t,F(),F(),F())}}class Ss{constructor(e,t,r,n){this.Se=e,this.removedTargetIds=t,this.key=r,this.be=n}}class xs{constructor(e,t){this.targetId=e,this.De=t}}class Cs{constructor(e,t,r=N.EMPTY_BYTE_STRING,n=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=n}}class As{constructor(){this.ve=0,this.Ce=ks(),this.Fe=N.EMPTY_BYTE_STRING,this.Me=!1,this.xe=!0}get current(){return this.Me}get resumeToken(){return this.Fe}get Oe(){return 0!==this.ve}get Ne(){return this.xe}Be(e){0<e.approximateByteSize()&&(this.xe=!0,this.Fe=e)}Le(){let r=F(),n=F(),i=F();return this.Ce.forEach((e,t)=>{switch(t){case 0:r=r.add(e);break;case 2:n=n.add(e);break;case 1:i=i.add(e);break;default:E(38017,{changeType:t})}}),new Es(this.Fe,this.Me,r,n,i)}ke(){this.xe=!1,this.Ce=ks()}qe(e,t){this.xe=!0,this.Ce=this.Ce.insert(e,t)}Qe(e){this.xe=!0,this.Ce=this.Ce.remove(e)}$e(){this.ve+=1}Ue(){--this.ve,y(0<=this.ve,3241,{ve:this.ve})}Ke(){this.xe=!0,this.Me=!0}}class Ds{constructor(e){this.We=e,this.Ge=new Map,this.ze=ki,this.je=Ns(),this.Je=Ns(),this.He=new A(S)}Ye(e){for(var t of e.Se)e.be&&e.be.isFoundDocument()?this.Ze(t,e.be):this.Xe(t,e.key,e.be);for(var r of e.removedTargetIds)this.Xe(r,e.key,e.be)}et(r){this.forEachTarget(r,e=>{var t=this.tt(e);switch(r.state){case 0:this.nt(e)&&t.Be(r.resumeToken);break;case 1:t.Ue(),t.Oe||t.ke(),t.Be(r.resumeToken);break;case 2:t.Ue(),t.Oe||this.removeTarget(e);break;case 3:this.nt(e)&&(t.Ke(),t.Be(r.resumeToken));break;case 4:this.nt(e)&&(this.rt(e),t.Be(r.resumeToken));break;default:E(56790,{state:r.state})}})}forEachTarget(e,r){0<e.targetIds.length?e.targetIds.forEach(r):this.Ge.forEach((e,t)=>{this.nt(t)&&r(t)})}it(n){let i=n.targetId,e=n.De.count,t=this.st(i);if(t){var r=t.target;if(ci(r))if(0===e){let e=new x(r.path);this.Xe(i,e,R.newNoDocument(e,w.min()))}else y(1===e,20013,{expectedCount:e});else{let r=this.ot(i);if(r!==e){let e=this._t(n),t=e?this.ut(e,n,r):1;if(0!==t){this.rt(i);let e=2===t?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.He=this.He.insert(i,e)}}}}}_t(e){var t=e.De.unchangedNames;if(!t||!t.bits)return null;var{bits:{bitmap:t="",padding:r=0},hashCount:n=0}=t;let i,s;try{i=sn(t).toUint8Array()}catch(e){if(e instanceof tn)return be("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{s=new bs(i,r,n)}catch(e){return be(e instanceof Is?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===s.fe?null:s}ut(e,t,r){return t.De.count===r-this.ht(e,t.targetId)?0:2}ht(r,n){var e=this.We.getRemoteKeysForTarget(n);let i=0;return e.forEach(e=>{var t=this.We.lt(),t=`projects/${t.projectId}/databases/${t.database}/documents/`+e.path.canonicalString();r.mightContain(t)||(this.Xe(n,e,null),i++)}),i}Pt(n){let i=new Map,s=(this.Ge.forEach((e,t)=>{var r=this.st(t);if(r){if(e.current&&ci(r.target)){let e=new x(r.target.path);this.Tt(e).has(t)||this.It(t,e)||this.Xe(t,e,R.newNoDocument(e,n))}e.Ne&&(i.set(t,e.Le()),e.ke())}}),F());this.Je.forEach((e,t)=>{let r=!0;t.forEachWhile(e=>{var t=this.st(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(r=!1)}),r&&(s=s.add(e))}),this.ze.forEach((e,t)=>t.setReadTime(n));var e=new Ts(n,i,this.He,this.ze,s);return this.ze=ki,this.je=Ns(),this.Je=Ns(),this.He=new A(S),e}Ze(e,t){var r;this.nt(e)&&(r=this.It(e,t.key)?2:0,this.tt(e).qe(t.key,r),this.ze=this.ze.insert(t.key,t),this.je=this.je.insert(t.key,this.Tt(t.key).add(e)),this.Je=this.Je.insert(t.key,this.dt(t.key).add(e)))}Xe(e,t,r){var n;this.nt(e)&&(n=this.tt(e),this.It(e,t)?n.qe(t,1):n.Qe(t),this.Je=this.Je.insert(t,this.dt(t).delete(e)),this.Je=this.Je.insert(t,this.dt(t).add(e)),r)&&(this.ze=this.ze.insert(t,r))}removeTarget(e){this.Ge.delete(e)}ot(e){var t=this.tt(e).Le();return this.We.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}$e(e){this.tt(e).$e()}tt(e){let t=this.Ge.get(e);return t||(t=new As,this.Ge.set(e,t)),t}dt(e){let t=this.Je.get(e);return t||(t=new D(S),this.Je=this.Je.insert(e,t)),t}Tt(e){let t=this.je.get(e);return t||(t=new D(S),this.je=this.je.insert(e,t)),t}nt(e){var t=null!==this.st(e);return t||p("WatchChangeAggregator","Detected inactive target",e),t}st(e){var t=this.Ge.get(e);return t&&t.Oe?null:this.We.Et(e)}rt(t){this.Ge.set(t,new As),this.We.getRemoteKeysForTarget(t).forEach(e=>{this.Xe(t,e,null)})}It(e,t){return this.We.getRemoteKeysForTarget(e).has(t)}}function Ns(){return new A(x.comparator)}function ks(){return new A(x.comparator)}let Rs={asc:"ASCENDING",desc:"DESCENDING"},Os={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},Ls={and:"AND",or:"OR"};class Fs{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function Vs(e,t){return e.useProto3Json||St(t)?t:{value:t}}function Ms(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function Ps(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function M(e){return y(!!e,49232),w.fromTimestamp((t=nn(e),new v(t.seconds,t.nanos)));var t}function Us(e,t){return Bs(e,t).canonicalString()}function Bs(e,t){e=e;var r=new T(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?r:r.child(t)}function qs(e){var t=T.fromString(e);return y(oa(t),10190,{key:t.toString()}),t}function js(e,t){return Us(e.databaseId,t.path)}function zs(e,t){var r=qs(t);if(r.get(1)!==e.databaseId.projectId)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+r.get(1)+" vs "+e.databaseId.projectId);if(r.get(3)!==e.databaseId.database)throw new I(b.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+r.get(3)+" vs "+e.databaseId.database);return new x(Qs(r))}function Ks(e,t){return Us(e.databaseId,t)}function Gs(e){var t=qs(e);return 4===t.length?T.emptyPath():Qs(t)}function $s(e){return new T(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function Qs(e){return y(4<e.length&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function Hs(e,t,r){return{name:js(e,t),fields:r.value.mapValue.fields}}function Ws(e,t,r){var n=zs(e,t.name),i=M(t.updateTime),s=t.createTime?M(t.createTime):w.min(),a=new qn({mapValue:{fields:t.fields}}),n=R.newFoundDocument(n,i,s,a);return r&&n.setHasCommittedMutations(),r?n.setHasCommittedMutations():n}function Xs(e,t){let r;if(t instanceof ss)r={update:Hs(e,t.key,t.value)};else if(t instanceof hs)r={delete:js(e,t.key)};else if(t instanceof as)r={update:Hs(e,t.key,t.data),updateMask:(e=>{let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}})(t.fieldMask)};else{if(!(t instanceof cs))return E(16599,{Rt:t.type});r={verify:js(e,t.key)}}return 0<t.fieldTransforms.length&&(r.updateTransforms=t.fieldTransforms.map(e=>{var t=e.transform;if(t instanceof Ki)return{fieldPath:e.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(t instanceof Gi)return{fieldPath:e.field.canonicalString(),appendMissingElements:{values:t.elements}};if(t instanceof Qi)return{fieldPath:e.field.canonicalString(),removeAllFromArray:{values:t.elements}};if(t instanceof Wi)return{fieldPath:e.field.canonicalString(),increment:t.Ee};throw E(20930,{transform:e.transform})})),t.precondition.isNone||(r.currentDocument=(e=e,void 0!==(t=t.precondition).updateTime?{updateTime:(n=t.updateTime,Ms(e,n.toTimestamp()))}:void 0!==t.exists?{exists:t.exists}:E(27497))),r;var n}function Ys(i,t){let r=t.currentDocument?void 0!==(s=t.currentDocument).updateTime?V.updateTime(M(s.updateTime)):void 0!==s.exists?V.exists(s.exists):V.none():V.none(),n=t.updateTransforms?t.updateTransforms.map(r=>{{var e=i;let t=null;if("setToServerValue"in r)y("REQUEST_TIME"===r.setToServerValue,16630,{proto:r}),t=new Ki;else if("appendMissingElements"in r){let e=r.appendMissingElements.values||[];t=new Gi(e)}else if("removeAllFromArray"in r){let e=r.removeAllFromArray.values||[];t=new Qi(e)}else"increment"in r?t=new Wi(e,r.increment):E(16584,{proto:r});var n=h.fromServerFormat(r.fieldPath);return new Ji(n,t)}}):[];var s,a;if(t.update){t.update.name;var o=zs(i,t.update.name),u=new qn({mapValue:{fields:t.update.fields}});if(t.updateMask){s=t.updateMask,a=s.fieldPaths||[];let e=new en(a.map(e=>h.fromServerFormat(e)));return new as(o,u,e,r,n)}return new ss(o,u,r,n)}if(t.delete){let e=zs(i,t.delete);return new hs(e,r)}if(t.verify){let e=zs(i,t.verify);return new cs(e,r)}return E(1463,{proto:t})}function Js(e,n){return e&&0<e.length?(y(void 0!==n,14353),e.map(t=>{{var r=n;let e=t.updateTime?M(t.updateTime):M(r);return e.isEqual(w.min())&&(e=M(r)),new Zi(e,t.transformResults||[])}})):[]}function Zs(e,t){return{documents:[Ks(e,t.path)]}}function ea(e,t){var r={structuredQuery:{}},n=t.path;let i;null!==t.collectionGroup?(i=n,r.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(i=n.popLast(),r.structuredQuery.from=[{collectionId:n.lastSegment()}]),r.parent=Ks(e,i);n=(e=>{if(0!==e.length)return function r(e){return e instanceof O?(e=>{if("=="===e.op){if(On(e.value))return{unaryFilter:{field:sa(e.field),op:"IS_NAN"}};if(Rn(e.value))return{unaryFilter:{field:sa(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(On(e.value))return{unaryFilter:{field:sa(e.field),op:"IS_NOT_NAN"}};if(Rn(e.value))return{unaryFilter:{field:sa(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:sa(e.field),op:na(e.op),value:e.value}}})(e):e instanceof L?(e=>{let t=e.getFilters().map(e=>r(e));return 1===t.length?t[0]:{compositeFilter:{op:ia(e.op),filters:t}}})(e):E(54877,{filter:e})}(L.create(e,"and"))})(t.filters),n&&(r.structuredQuery.where=n),n=(e=>{if(0!==e.length)return e.map(e=>({field:sa((e=e).field),direction:(e=e.dir,Rs[e])}))})(t.orderBy),n&&(r.structuredQuery.orderBy=n),n=Vs(e,t.limit);return null!==n&&(r.structuredQuery.limit=n),t.startAt&&(r.structuredQuery.startAt={before:(e=t.startAt).inclusive,values:e.position}),t.endAt&&(r.structuredQuery.endAt={before:!(e=t.endAt).inclusive,values:e.position}),{Vt:r,parent:i}}function ta(e){let t=Gs(e.parent);var r,n=e.structuredQuery,i=n.from?n.from.length:0;let s=null;if(0<i){y(1===i,65062);let e=n.from[0];e.allDescendants?s=e.collectionId:t=t.child(e.collectionId)}let a=[],o=(n.where&&(a=(e=n.where,(i=function t(e){return void 0!==e.unaryFilter?(i=>{switch(i.unaryFilter.op){case"IS_NAN":let e=aa(i.unaryFilter.field);return O.create(e,"==",{doubleValue:NaN});case"IS_NULL":let t=aa(i.unaryFilter.field);return O.create(t,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let r=aa(i.unaryFilter.field);return O.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let n=aa(i.unaryFilter.field);return O.create(n,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return E(61313);default:return E(60726)}})(e):void 0!==e.fieldFilter?(e=>O.create(aa(e.fieldFilter.field),(e=>{switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return E(58110);default:return E(50506)}})(e.fieldFilter.op),e.fieldFilter.value))(e):void 0!==e.compositeFilter?(e=>L.create(e.compositeFilter.filters.map(e=>t(e)),(e=>{switch(e){case"AND":return"and";case"OR":return"or";default:return E(1026)}})(e.compositeFilter.op)))(e):E(30097,{filter:e})}(e))instanceof L&&Wn(i)?i.getFilters():[i])),[]),u=(n.orderBy&&(o=n.orderBy.map(e=>(e=e,new Gn(aa(e.field),(e=>{switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}})(e.direction))))),null),l=(n.limit&&(u=(e=n.limit,St(i="object"==typeof e?e.value:e)?null:i)),null),h=(n.startAt&&(l=(e=n.startAt,i=!!e.before,r=e.values||[],new jn(r,i))),null);return n.endAt&&(h=(e=n.endAt,r=!e.before,i=e.values||[],new jn(i,r))),pi(t,s,o,a,u,"F",l,h)}function ra(e,t){var r=(e=>{switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return E(28987,{purpose:e})}})(t.purpose);return null==r?null:{"goog-listen-tags":r}}function na(e){return Os[e]}function ia(e){return Ls[e]}function sa(e){return{fieldPath:e.canonicalString()}}function aa(e){return h.fromServerFormat(e.fieldPath)}function oa(e){return 4<=e.length&&"projects"===e.get(0)&&"databases"===e.get(2)}class ua{constructor(e,t,r,n,i=w.min(),s=w.min(),a=N.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=n,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new ua(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new ua(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new ua(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new ua(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class la{constructor(e){this.gt=e}}function ha(e,t){var r,n=t.key,i={prefixPath:n.getCollectionPath().popLast().toArray(),collectionGroup:n.collectionGroup,documentId:n.path.lastSegment(),readTime:ca(t.readTime),hasCommittedMutations:t.hasCommittedMutations};if(t.isFoundDocument())i.document={name:js(e=e.gt,(r=t).key),fields:r.data.value.mapValue.fields,updateTime:Ms(e,r.version.toTimestamp()),createTime:Ms(e,r.createTime.toTimestamp())};else if(t.isNoDocument())i.noDocument={path:n.path.toArray(),readTime:da(t.version)};else{if(!t.isUnknownDocument())return E(57904,{document:t});i.unknownDocument={path:n.path.toArray(),version:da(t.version)}}return i}function ca(e){var t=e.toTimestamp();return[t.seconds,t.nanoseconds]}function da(e){var t=e.toTimestamp();return{seconds:t.seconds,nanoseconds:t.nanoseconds}}function fa(e){var t=new v(e.seconds,e.nanoseconds);return w.fromTimestamp(t)}function ga(t,r){let e=(r.baseMutations||[]).map(e=>Ys(t.gt,e));for(let s=0;s<r.mutations.length-1;++s){let t=r.mutations[s];if(s+1<r.mutations.length&&void 0!==r.mutations[s+1].transform){let e=r.mutations[s+1];t.updateTransforms=e.transform.fieldTransforms,r.mutations.splice(s+1,1),++s}}let n=r.mutations.map(e=>Ys(t.gt,e)),i=v.fromMillis(r.localWriteTimeMs);return new ds(r.batchId,i,e,n)}function ma(e){var t,r=fa(e.readTime),n=void 0!==e.lastLimboFreeSnapshotVersion?fa(e.lastLimboFreeSnapshotVersion):w.min(),i=void 0!==e.query.documents?(t=e.query,y(1===(i=t.documents.length),1966,{count:i}),bi(yi(Gs(t.documents[0])))):bi(ta(e.query));return new ua(i,e.targetId,"TargetPurposeListen",e.lastListenSequenceNumber,r,n,N.fromBase64String(e.resumeToken))}function pa(e,t){var r=da(t.snapshotVersion),n=da(t.lastLimboFreeSnapshotVersion),i=ci(t.target)?Zs(e.gt,t.target):ea(e.gt,t.target).Vt,s=t.resumeToken.toBase64();return{targetId:t.targetId,canonicalId:li(t.target),readTime:r,resumeToken:s,lastListenSequenceNumber:t.sequenceNumber,lastLimboFreeSnapshotVersion:n,query:i}}function ya(e){var t=ta({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?Ti(t,t.limit,"L"):t}function va(e,t){return new gs(t.largestBatchId,Ys(e.gt,t.overlayMutation))}function wa(e,t){var r=t.path.lastSegment();return[e,Dt(t.path.popLast()),r]}function _a(e,t,r,n){return{indexId:e,uid:t,sequenceNumber:r,readTime:da(n.readTime),documentKey:Dt(n.documentKey.path),largestBatchId:n.largestBatchId}}class ba{getBundleMetadata(e,t){return Ia(e).get(t).next(e=>{if(e)return{id:(e=e).bundleId,createTime:fa(e.createTime),version:e.version}})}saveBundleMetadata(e,t){return Ia(e).put({bundleId:(e=t).id,createTime:da(M(e.createTime)),version:e.version})}getNamedQuery(e,t){return Ta(e).get(t).next(e=>{if(e)return{name:(e=e).name,query:ya(e.bundledQuery),readTime:fa(e.readTime)}})}saveNamedQuery(e,t){return Ta(e).put({name:(e=t).name,readTime:da(M(e.readTime)),bundledQuery:e.bundledQuery})}}function Ia(e){return r(e,hr)}function Ta(e){return r(e,cr)}class Ea{constructor(e,t){this.serializer=e,this.userId=t}static yt(e,t){var r=t.uid||"";return new Ea(e,r)}getOverlay(e,t){return Sa(e).get(wa(this.userId,t)).next(e=>e?va(this.serializer,e):null)}getOverlays(e,t){let r=Fi();return C.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(n,i,e){let s=[];return e.forEach((e,t)=>{var r=new gs(i,t);s.push(this.wt(n,r))}),C.waitFor(s)}removeOverlaysForBatchId(r,e,n){let t=new Set,i=(e.forEach(e=>t.add(Dt(e.getCollectionPath()))),[]);return t.forEach(e=>{var t=IDBKeyRange.bound([this.userId,e,n],[this.userId,e,n+1],!1,!0);i.push(Sa(r).Y(Or,t))}),C.waitFor(i)}getOverlaysForCollection(e,t,r){let n=Fi(),i=Dt(t),s=IDBKeyRange.bound([this.userId,i,r],[this.userId,i,Number.POSITIVE_INFINITY],!0);return Sa(e).j(Or,s).next(e=>{for(var t of e){let e=va(this.serializer,t);n.set(e.getKey(),e)}return n})}getOverlaysForCollectionGroup(e,t,r,i){let s=Fi(),a;var n=IDBKeyRange.bound([this.userId,t,r],[this.userId,t,Number.POSITIVE_INFINITY],!0);return Sa(e).X({index:Fr,range:n},(e,t,r)=>{var n=va(this.serializer,t);s.size()<i||n.largestBatchId===a?(s.set(n.getKey(),n),a=n.largestBatchId):r.done()}).next(()=>s)}wt(e,t){return Sa(e).put(((e,t,r)=>{var[,n,i]=wa(t,r.mutation.key);return{userId:t,collectionPath:n,documentId:i,collectionGroup:r.mutation.key.getCollectionGroup(),largestBatchId:r.largestBatchId,overlayMutation:Xs(e.gt,r.mutation)}})(this.serializer,this.userId,t))}}function Sa(e){return r(e,kr)}class xa{St(e){return r(e,Mr)}getSessionToken(e){return this.St(e).get("sessionToken").next(e=>{var t=null==e?void 0:e.value;return t?N.fromUint8Array(t):N.EMPTY_BYTE_STRING})}setSessionToken(e,t){return this.St(e).put({name:"sessionToken",value:t.toUint8Array()})}}class Ca{constructor(){}bt(e,t){this.Dt(e,t),t.vt()}Dt(t,r){if("nullValue"in t)this.Ct(r,5);else if("booleanValue"in t)this.Ct(r,10),r.Ft(t.booleanValue?1:0);else if("integerValue"in t)this.Ct(r,15),r.Ft(k(t.integerValue));else if("doubleValue"in t){var e=k(t.doubleValue);isNaN(e)?this.Ct(r,13):(this.Ct(r,15),xt(e)?r.Ft(0):r.Ft(e))}else if("timestampValue"in t){let e=t.timestampValue;this.Ct(r,20),"string"==typeof e&&(e=nn(e)),r.Mt(""+(e.seconds||"")),r.Ft(e.nanos||0)}else"stringValue"in t?(this.xt(t.stringValue,r),this.Ot(r)):"bytesValue"in t?(this.Ct(r,30),r.Nt(sn(t.bytesValue)),this.Ot(r)):"referenceValue"in t?this.Bt(t.referenceValue,r):"geoPointValue"in t?(e=t.geoPointValue,this.Ct(r,45),r.Ft(e.latitude||0),r.Ft(e.longitude||0)):"mapValue"in t?Mn(t)?this.Ct(r,Number.MAX_SAFE_INTEGER):Fn(t)?this.Lt(t.mapValue,r):(this.kt(t.mapValue,r),this.Ot(r)):"arrayValue"in t?(this.qt(t.arrayValue,r),this.Ot(r)):E(19022,{Qt:t})}xt(e,t){this.Ct(t,25),this.$t(e,t)}$t(e,t){t.Mt(e)}kt(e,t){var r=e.fields||{};this.Ct(t,55);for(let e of Object.keys(r))this.xt(e,t),this.Dt(r[e],t)}Lt(e,t){var r=e.fields||{},n=(this.Ct(t,53),_n),i=(null==(i=null==(i=r[n].arrayValue)?void 0:i.values)?void 0:i.length)||0;this.Ct(t,15),t.Ft(k(i)),this.xt(n,t),this.Dt(r[n],t)}qt(e,t){var r=e.values||[];this.Ct(t,50);for(let e of r)this.Dt(e,t)}Bt(e,t){this.Ct(t,37),x.fromName(e).path.forEach(e=>{this.Ct(t,60),this.$t(e,t)})}Ct(e,t){e.Ft(t)}Ot(e){e.Ft(2)}}Ca.Ut=new Ca;function Aa(e){var t=64-(e=>{let t=0;for(let n=0;n<8;++n){var r=(e=>{if(0===e)return 8;let t=0;return e>>4||(t+=4,e<<=4),e>>6||(t+=2,e<<=2),e>>7||(t+=1),t})(255&e[n]);if(t+=r,8!==r)break}return t})(e);return Math.ceil(t/8)}class Da{constructor(){this.buffer=new Uint8Array(1024),this.position=0}Kt(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.Wt(r.value),r=t.next();this.Gt()}zt(e){var t=e[Symbol.iterator]();let r=t.next();for(;!r.done;)this.jt(r.value),r=t.next();this.Jt()}Ht(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.Wt(e);else if(e<2048)this.Wt(960|e>>>6),this.Wt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Wt(480|e>>>12),this.Wt(128|63&e>>>6),this.Wt(128|63&e);else{let e=t.codePointAt(0);this.Wt(240|e>>>18),this.Wt(128|63&e>>>12),this.Wt(128|63&e>>>6),this.Wt(128|63&e)}}this.Gt()}Yt(e){for(var t of e){let e=t.charCodeAt(0);if(e<128)this.jt(e);else if(e<2048)this.jt(960|e>>>6),this.jt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.jt(480|e>>>12),this.jt(128|63&e>>>6),this.jt(128|63&e);else{let e=t.codePointAt(0);this.jt(240|e>>>18),this.jt(128|63&e>>>12),this.jt(128|63&e>>>6),this.jt(128|63&e)}}this.Jt()}Zt(e){var t=this.Xt(e),r=Aa(t);this.en(1+r),this.buffer[this.position++]=255&r;for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=255&t[n]}tn(e){var t=this.Xt(e),r=Aa(t);this.en(1+r),this.buffer[this.position++]=~(255&r);for(let n=t.length-r;n<t.length;++n)this.buffer[this.position++]=~(255&t[n])}nn(){this.rn(255),this.rn(255)}sn(){this._n(255),this._n(255)}reset(){this.position=0}seed(e){this.en(e.length),this.buffer.set(e,this.position),this.position+=e.length}an(){return this.buffer.slice(0,this.position)}Xt(e){e=e,(t=new DataView(new ArrayBuffer(8))).setFloat64(0,e,!1);var t,r=new Uint8Array(t.buffer),n=!!(128&r[0]);r[0]^=n?255:128;for(let i=1;i<r.length;++i)r[i]^=n?255:0;return r}Wt(e){var t=255&e;0==t?(this.rn(0),this.rn(255)):255==t?(this.rn(255),this.rn(0)):this.rn(t)}jt(e){var t=255&e;0==t?(this._n(0),this._n(255)):255==t?(this._n(255),this._n(0)):this._n(e)}Gt(){this.rn(0),this.rn(1)}Jt(){this._n(0),this._n(1)}rn(e){this.en(1),this.buffer[this.position++]=e}_n(e){this.en(1),this.buffer[this.position++]=~e}en(e){var t=e+this.position;if(!(t<=this.buffer.length)){let e=2*this.buffer.length;e<t&&(e=t);t=new Uint8Array(e);t.set(this.buffer),this.buffer=t}}}class Na{constructor(e){this.un=e}Nt(e){this.un.Kt(e)}Mt(e){this.un.Ht(e)}Ft(e){this.un.Zt(e)}vt(){this.un.nn()}}class ka{constructor(e){this.un=e}Nt(e){this.un.zt(e)}Mt(e){this.un.Yt(e)}Ft(e){this.un.tn(e)}vt(){this.un.sn()}}class Ra{constructor(){this.un=new Da,this.cn=new Na(this.un),this.ln=new ka(this.un)}seed(e){this.un.seed(e)}hn(e){return 0===e?this.cn:this.ln}an(){return this.un.an()}reset(){this.un.reset()}}class Oa{constructor(e,t,r,n){this.Pn=e,this.Tn=t,this.In=r,this.dn=n}En(){var e=this.dn.length,t=0===e||255===this.dn[e-1]?e+1:e,r=new Uint8Array(t);return r.set(this.dn,0),t!==e?r.set([0],this.dn.length):++r[r.length-1],new Oa(this.Pn,this.Tn,this.In,r)}An(e,t,r){return{indexId:this.Pn,uid:e,arrayValue:Va(this.In),directionalValue:Va(this.dn),orderedDocumentKey:Va(t),documentKey:r.path.toArray()}}Rn(e,t,r){var n=this.An(e,t,r);return[n.indexId,n.uid,n.arrayValue,n.directionalValue,n.orderedDocumentKey,n.documentKey]}}function La(e,t){var r=e.Pn-t.Pn;return 0!=r||0!==(r=Fa(e.In,t.In))||0!==(r=Fa(e.dn,t.dn))?r:x.comparator(e.Tn,t.Tn)}function Fa(e,t){for(let n=0;n<e.length&&n<t.length;++n){var r=e[n]-t[n];if(0!=r)return r}return e.length-t.length}function Va(r){if(ne()){var n=r;let e="";for(let t=0;t<n.length;t++)e+=String.fromCharCode(n[t]);return e}return r}function Ma(e){if("string"!=typeof e)return e;var t=e,r=new Uint8Array(t.length);for(let n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}class Pa{constructor(e){this.Vn=new D((e,t)=>h.comparator(e.field,t.field)),this.collectionId=null!=e.collectionGroup?e.collectionGroup:e.path.lastSegment(),this.mn=e.orderBy,this.fn=[];for(var t of e.filters){let e=t;e.isInequality()?this.Vn=this.Vn.add(e):this.fn.push(e)}}get gn(){return 1<this.Vn.size}pn(e){if(y(e.collectionGroup===this.collectionId,49279),this.gn)return!1;let t=Je(e);if(void 0!==t&&!this.yn(t))return!1;var r=Ze(e);let n=new Set,i=0,s=0;for(;i<r.length&&this.yn(r[i]);++i)n=n.add(r[i].fieldPath.canonicalString());if(i!==r.length){if(0<this.Vn.size){let t=this.Vn.getIterator().getNext();if(!n.has(t.field.canonicalString())){let e=r[i];if(!this.wn(t,e)||!this.Sn(this.mn[s++],e))return!1}++i}for(;i<r.length;++i){let e=r[i];if(s>=this.mn.length||!this.Sn(this.mn[s++],e))return!1}}return!0}bn(){if(this.gn)return null;let e=new D(h.comparator);var t,r,n=[];for(t of this.fn)t.field.isKeyField()||("array-contains"===t.op||"array-contains-any"===t.op?n.push(new et(t.field,2)):e.has(t.field)||(e=e.add(t.field),n.push(new et(t.field,0))));for(r of this.mn)r.field.isKeyField()||e.has(r.field)||(e=e.add(r.field),n.push(new et(r.field,"asc"===r.dir?0:1)));return new Ye(Ye.UNKNOWN_ID,this.collectionId,n,tt.empty())}yn(e){for(var t of this.fn)if(this.wn(t,e))return!0;return!1}wn(e,t){var r;return!(void 0===e||!e.field.isEqual(t.fieldPath))&&(r="array-contains"===e.op||"array-contains-any"===e.op,2===t.kind==r)}Sn(e,t){return!!e.field.isEqual(t.fieldPath)&&(0===t.kind&&"asc"===e.dir||1===t.kind&&"desc"===e.dir)}}function Ua(e){var t;return 0===e.getFilters().length?[]:(t=function t(e){if(y(e instanceof O||e instanceof L,34018),e instanceof O)return e;if(1===e.filters.length)return t(e.filters[0]);let r=e.filters.map(e=>t(e));let n=L.create(r,e.op);return n=Ga(n),ja(n)?n:(y(n instanceof L,64498),y(Qn(n),40251),y(1<n.filters.length,57927),n.filters.reduce((e,t)=>za(e,t)))}(function t(r){var n;if(y(r instanceof O||r instanceof L,20012),r instanceof O){if(r instanceof ii){let e=(null==(n=null==(n=r.value.arrayValue)?void 0:n.values)?void 0:n.map(e=>O.create(r.field,"==",e)))||[];return L.create(e,"or")}return r}let e=r.filters.map(e=>t(e));return L.create(e,r.op)}(e)),y(ja(t),7391),Ba(t)||qa(t)?[t]:t.getFilters())}function Ba(e){return e instanceof O}function qa(e){return e instanceof L&&Wn(e)}function ja(e){return Ba(e)||qa(e)||(e=>{if(e instanceof L&&Hn(e)){for(var t of e.getFilters())if(!Ba(t)&&!qa(t))return!1;return!0}return!1})(e)}function za(e,t){var r,n;return y(e instanceof O||e instanceof L,38388),y(t instanceof O||t instanceof L,25473),Ga(e instanceof O?t instanceof O?(r=e,n=t,L.create([r,n],"and")):Ka(e,t):t instanceof O?Ka(t,e):((e,t)=>{if(y(0<e.filters.length&&0<t.filters.length,48005),Qn(e)&&Qn(t))return Yn(e,t.getFilters());let r=Hn(e)?e:t,n=Hn(e)?t:e,i=r.filters.map(e=>za(e,n));return L.create(i,"or")})(e,t))}function Ka(t,e){var r;return Qn(e)?Yn(e,t.getFilters()):(r=e.filters.map(e=>za(t,e)),L.create(r,"or"))}function Ga(t){if(y(t instanceof O||t instanceof L,11850),t instanceof O)return t;var e=t.getFilters();if(1===e.length)return Ga(e[0]);if(Xn(t))return t;let r=e.map(e=>Ga(e)),n=[];return r.forEach(e=>{e instanceof O?n.push(e):e instanceof L&&(e.op===t.op?n.push(...e.filters):n.push(e))}),1===n.length?n[0]:L.create(n,t.op)}class $a{constructor(){this.Dn=new Qa}addToCollectionParentIndex(e,t){return this.Dn.add(t),C.resolve()}getCollectionParents(e,t){return C.resolve(this.Dn.getEntries(t))}addFieldIndex(e,t){return C.resolve()}deleteFieldIndex(e,t){return C.resolve()}deleteAllFieldIndexes(e){return C.resolve()}createTargetIndexes(e,t){return C.resolve()}getDocumentsMatchingTarget(e,t){return C.resolve(null)}getIndexType(e,t){return C.resolve(0)}getFieldIndexes(e,t){return C.resolve([])}getNextCollectionGroupToUpdate(e){return C.resolve(null)}getMinOffset(e,t){return C.resolve(it.min())}getMinOffsetFromCollectionGroup(e,t){return C.resolve(it.min())}updateCollectionGroup(e,t,r){return C.resolve()}updateIndexEntries(e,t){return C.resolve()}}class Qa{constructor(){this.index={}}add(e){var t=e.lastSegment(),r=e.popLast(),n=this.index[t]||new D(T.comparator),i=!n.has(r);return this.index[t]=n.add(r),i}has(e){var t=e.lastSegment(),r=e.popLast(),t=this.index[t];return t&&t.has(r)}getEntries(e){return(this.index[e]||new D(T.comparator)).toArray()}}let Ha="IndexedDbIndexManager",Wa=new Uint8Array(0);class Xa{constructor(e,t){this.databaseId=t,this.vn=new Qa,this.Cn=new Ni(e=>li(e),(e,t)=>hi(e,t)),this.uid=e.uid||""}addToCollectionParentIndex(e,t){var r,n;return this.vn.has(t)?C.resolve():(n=t.lastSegment(),r=t.popLast(),e.addOnCommittedListener(()=>{this.vn.add(t)}),n={collectionId:n,parent:Dt(r)},Ya(e).put(n))}getCollectionParents(e,r){let n=[],t=IDBKeyRange.bound([r,""],[Me(r),""],!1,!0);return Ya(e).j(t).next(e=>{for(var t of e){if(t.collectionId!==r)break;n.push(kt(t.parent))}return n})}addFieldIndex(e,r){let t=Za(e),n={indexId:r.indexId,collectionGroup:r.collectionGroup,fields:r.fields.map(e=>[e.fieldPath.canonicalString(),e.kind])};delete n.indexId;var i=t.add(n);if(r.indexState){let t=eo(e);return i.next(e=>{t.put(_a(e,this.uid,r.indexState.sequenceNumber,r.indexState.offset))})}return i.next()}deleteFieldIndex(e,t){let r=Za(e),n=eo(e),i=Ja(e);return r.delete(t.indexId).next(()=>n.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0))).next(()=>i.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0)))}deleteAllFieldIndexes(e){let t=Za(e),r=Ja(e),n=eo(e);return t.Y().next(()=>r.Y()).next(()=>n.Y())}createTargetIndexes(r,e){return C.forEach(this.Fn(e),t=>this.getIndexType(r,t).next(e=>{if(0===e||1===e){let e=new Pa(t).bn();if(null!=e)return this.addFieldIndex(r,e)}}))}getDocumentsMatchingTarget(e,c){let d=Ja(e),r=!0,n=new Map;return C.forEach(this.Fn(c),t=>this.Mn(e,t).next(e=>{r=r&&!!e,n.set(t,e)})).next(()=>{if(r){let l=F(),h=[];return C.forEach(n,(e,t)=>{p(Ha,`Using index ${r=e,`id=${r.indexId}|cg=${r.collectionGroup}|f=`+r.fields.map(e=>e.fieldPath+":"+e.kind).join(",")} to execute `+li(c));var r,n=((t,e)=>{var r=Je(e);if(void 0!==r)for(let e of di(t,r.fieldPath))switch(e.op){case"array-contains-any":return e.value.arrayValue.values||[];case"array-contains":return[e.value]}return null})(t,e),i=((t,r)=>{var n,i=new Map;for(n of Ze(r))for(let e of di(t,n.fieldPath))switch(e.op){case"==":case"in":i.set(n.fieldPath.canonicalString(),e.value);break;case"not-in":case"!=":return i.set(n.fieldPath.canonicalString(),e.value),Array.from(i.values())}return null})(t,e),s=((t,e)=>{var r,n=[];let i=!0;for(r of Ze(e)){let e=(0===r.kind?fi:gi)(t,r.fieldPath,t.startAt);n.push(e.value),i=i&&e.inclusive}return new jn(n,i)})(t,e),a=((t,e)=>{var r,n=[];let i=!0;for(r of Ze(e)){let e=(0===r.kind?gi:fi)(t,r.fieldPath,t.endAt);n.push(e.value),i=i&&e.inclusive}return new jn(n,i)})(t,e),o=this.xn(e,t,s),u=this.xn(e,t,a),i=this.On(e,t,i),n=this.Nn(e.indexId,n,o,s.inclusive,u,a.inclusive,i);return C.forEach(n,e=>d.H(e,c.limit).next(e=>{e.forEach(e=>{var t=x.fromSegments(e.documentKey);l.has(t)||(l=l.add(t),h.push(t))})}))}).next(()=>h)}return C.resolve(null)})}Fn(t){let e=this.Cn.get(t);return e||(e=0===t.filters.length?[t]:Ua(L.create(t.filters,"and")).map(e=>ui(t.path,t.collectionGroup,t.orderBy,e.getFilters(),t.limit,t.startAt,t.endAt)),this.Cn.set(t,e)),e}Nn(i,s,a,o,u,l,h){let e=(null!=s?s.length:1)*Math.max(a.length,u.length),c=e/(null!=s?s.length:1),d=[];for(let f=0;f<e;++f){let t=s?this.Bn(s[f/c]):Wa,e=this.Ln(i,t,a[f%c],o),r=this.kn(i,t,u[f%c],l),n=h.map(e=>this.Ln(i,t,e,!0));d.push(...this.createRange(e,r,n))}return d}Ln(e,t,r,n){var i=new Oa(e,x.empty(),t,r);return n?i:i.En()}kn(e,t,r,n){var i=new Oa(e,x.empty(),t,r);return n?i.En():i}Mn(e,t){let n=new Pa(t),r=null!=t.collectionGroup?t.collectionGroup:t.path.lastSegment();return this.getFieldIndexes(e,r).next(e=>{let t=null;for(var r of e)n.pn(r)&&(!t||r.fields.length>t.fields.length)&&(t=r);return t})}getIndexType(e,t){let r=2,n=this.Fn(t);return C.forEach(n,t=>this.Mn(e,t).next(e=>{e?0!==r&&e.fields.length<(t=>{let r=new D(h.comparator),n=!1;for(var i of t.filters)for(let e of i.getFlattenedFilters())e.field.isKeyField()||("array-contains"===e.op||"array-contains-any"===e.op?n=!0:r=r.add(e.field));for(let n of t.orderBy)n.field.isKeyField()||(r=r.add(n.field));return r.size+(n?1:0)})(t)&&(r=1):r=0})).next(()=>null!==t.limit&&1<n.length&&2===r?1:r)}qn(e,t){var r,n=new Ra;for(r of Ze(e)){let e=t.data.field(r.fieldPath);if(null==e)return null;var i=n.hn(r.kind);Ca.Ut.bt(e,i)}return n.an()}Bn(e){var t=new Ra;return Ca.Ut.bt(e,t.hn(0)),t.an()}Qn(e,t){var r,n=new Ra;return Ca.Ut.bt(Dn(this.databaseId,t),n.hn(0===(r=Ze(e)).length?0:r[r.length-1].kind)),n.an()}On(e,n,i){if(null===i)return[];let s=[],a=(s.push(new Ra),0);for(var o of Ze(e)){let t=i[a++];for(let r of s)if(this.$n(n,o.fieldPath)&&kn(t))s=this.Un(s,o,t);else{let e=r.hn(o.kind);Ca.Ut.bt(t,e)}}return this.Kn(s)}xn(e,t,r){return this.On(e,t,r.position)}Kn(e){var t=[];for(let r=0;r<e.length;++r)t[r]=e[r].an();return t}Un(r,n,e){let i=[...r],s=[];for(let r of e.arrayValue.values||[])for(let t of i){let e=new Ra;e.seed(t.an()),Ca.Ut.bt(r,e.hn(n.kind)),s.push(e)}return s}$n(e,t){return!!e.filters.find(e=>e instanceof O&&e.field.isEqual(t)&&("in"===e.op||"not-in"===e.op))}getFieldIndexes(e,t){let r=Za(e),n=eo(e);return(t?r.j(fr,IDBKeyRange.bound(t,t)):r.j()).next(e=>{let s=[];return C.forEach(e,i=>n.get([i.indexId,this.uid]).next(e=>{var t,r,n;s.push((t=i,r=(e=e)?new tt(e.sequenceNumber,new it(fa(e.readTime),new x(kt(e.documentKey)),e.largestBatchId)):tt.empty(),n=t.fields.map(([e,t])=>new et(h.fromServerFormat(e),t)),new Ye(t.indexId,t.collectionGroup,n,r)))})).next(()=>s)})}getNextCollectionGroupToUpdate(e){return this.getFieldIndexes(e).next(e=>0===e.length?null:(e.sort((e,t)=>{var r=e.indexState.sequenceNumber-t.indexState.sequenceNumber;return 0!=r?r:S(e.collectionGroup,t.collectionGroup)}),e[0].collectionGroup))}updateCollectionGroup(e,r,n){let i=Za(e),s=eo(e);return this.Wn(e).next(t=>i.j(fr,IDBKeyRange.bound(r,r)).next(e=>C.forEach(e,e=>s.put(_a(e.indexId,this.uid,t,n)))))}updateIndexEntries(i,e){let r=new Map;return C.forEach(e,(t,n)=>{var e=r.get(t.collectionGroup);return(e?C.resolve(e):this.getFieldIndexes(i,t.collectionGroup)).next(e=>(r.set(t.collectionGroup,e),C.forEach(e,r=>this.Gn(i,t,r).next(e=>{var t=this.zn(n,r);return e.isEqual(t)?C.resolve():this.jn(i,n,r,e,t)}))))})}Jn(e,t,r,n){return Ja(e).put(n.An(this.uid,this.Qn(r,t.key),t.key))}Hn(e,t,r,n){return Ja(e).delete(n.Rn(this.uid,this.Qn(r,t.key),t.key))}Gn(e,r,n){var t=Ja(e);let i=new D(La);return t.X({index:Dr,range:IDBKeyRange.only([n.indexId,this.uid,Va(this.Qn(n,r))])},(e,t)=>{i=i.add(new Oa(n.indexId,r,Ma(t.arrayValue),Ma(t.directionalValue)))}).next(()=>i)}zn(t,r){let n=new D(La);var i=this.qn(r,t);if(null!=i){let e=Je(r);if(null!=e){var s=t.data.field(e.fieldPath);if(kn(s))for(let e of s.arrayValue.values||[])n=n.add(new Oa(r.indexId,t.key,this.Bn(e),i))}else n=n.add(new Oa(r.indexId,t.key,Wa,i))}return n}jn(t,r,s,e,a){p(Ha,"Updating index entries for document '%s'",r.key);let o=[];{var u=La,l=e=>{o.push(this.Jn(t,r,s,e))},h=e=>{o.push(this.Hn(t,r,s,e))},c=e.getIterator(),d=a.getIterator();let n=Zr(c),i=Zr(d);for(;n||i;){let t=!1,r=!1;if(n&&i){let e=u(n,i);e<0?r=!0:0<e&&(t=!0)}else null!=n?r=!0:t=!0;t?(l(i),i=Zr(d)):r?(h(n),n=Zr(c)):(n=Zr(c),i=Zr(d))}}return C.waitFor(o)}Wn(e){let n=1;return eo(e).X({index:Sr,reverse:!0,range:IDBKeyRange.upperBound([this.uid,Number.MAX_SAFE_INTEGER])},(e,t,r)=>{r.done(),n=t.sequenceNumber+1}).next(()=>n)}createRange(r,n,e){e=e.sort((e,t)=>La(e,t)).filter((e,t,r)=>!t||0!==La(e,r[t-1]));var i=[];i.push(r);for(let s of e){let e=La(s,r),t=La(s,n);if(0===e)i[0]=r.En();else if(0<e&&t<0)i.push(s),i.push(s.En());else if(0<t)break}i.push(n);let s=[];for(let a=0;a<i.length;a+=2){if(this.Yn(i[a],i[a+1]))return[];let e=i[a].Rn(this.uid,Wa,x.empty()),t=i[a+1].Rn(this.uid,Wa,x.empty());s.push(IDBKeyRange.bound(e,t))}return s}Yn(e,t){return 0<La(e,t)}getMinOffsetFromCollectionGroup(e,t){return this.getFieldIndexes(e,t).next(to)}getMinOffset(t,e){return C.mapArray(this.Fn(e),e=>this.Mn(t,e).next(e=>e||E(44426))).next(to)}}function Ya(e){return r(e,or)}function Ja(e){return r(e,Cr)}function Za(e){return r(e,dr)}function eo(e){return r(e,gr)}function to(e){y(0!==e.length,28825);let t=e[0].indexState.offset,r=t.largestBatchId;for(let i=1;i<e.length;i++){var n=e[i].indexState.offset;st(n,t)<0&&(t=n),r<n.largestBatchId&&(r=n.largestBatchId)}return new it(t.readTime,t.documentKey,r)}let ro={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class no{static withCacheSize(e){return new no(e,no.DEFAULT_COLLECTION_PERCENTILE,no.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}}function io(t,r,n){let e=t.store(Vt),i=t.store(zt),s=[],a=IDBKeyRange.only(n.batchId),o=0;var u=e.X({range:a},(e,t,r)=>(o++,r.delete()));s.push(u.next(()=>{y(1===o,47070,{batchId:n.batchId})}));let l=[];for(let t of n.mutations){let e=qt(r,t.key.path,n.batchId);s.push(i.delete(e)),l.push(t.key)}return C.waitFor(s).next(()=>l)}function so(e){if(!e)return 0;let t;if(e.document)t=e.document;else if(e.unknownDocument)t=e.unknownDocument;else{if(!e.noDocument)throw E(14731);t=e.noDocument}return JSON.stringify(t).length}no.DEFAULT_COLLECTION_PERCENTILE=10,no.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,no.DEFAULT=new no(41943040,no.DEFAULT_COLLECTION_PERCENTILE,no.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),no.DISABLED=new no(-1,0,0);class ao{constructor(e,t,r,n){this.userId=e,this.serializer=t,this.indexManager=r,this.referenceDelegate=n,this.Zn={}}static yt(e,t,r,n){y(""!==e.uid,64387);var i=e.isAuthenticated()?e.uid:"";return new ao(i,t,r,n)}checkEmpty(e){let n=!0;var t=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return uo(e).X({index:Pt,range:t},(e,t,r)=>{n=!1,r.done()}).next(()=>n)}addMutationBatch(c,d,f,g){let m=lo(c),p=uo(c);return p.add({}).next(t=>{y("number"==typeof t,49019);let e=new ds(t,d,f,g),r=(i=this.serializer,s=this.userId,a=e,o=a.baseMutations.map(e=>Xs(i.gt,e)),u=a.mutations.map(e=>Xs(i.gt,e)),{userId:s,batchId:a.batchId,localWriteTimeMs:a.localWriteTime.toMillis(),baseMutations:o,mutations:u}),n=[];var i,s,a,o,u;let l=new D((e,t)=>S(e.canonicalString(),t.canonicalString()));for(let h of g){let e=qt(this.userId,h.key.path,t);l=l.add(h.key.path.popLast()),n.push(p.put(r)),n.push(m.put(e,jt))}return l.forEach(e=>{n.push(this.indexManager.addToCollectionParentIndex(c,e))}),c.addOnCommittedListener(()=>{this.Zn[t]=e.keys()}),C.waitFor(n).next(()=>e)})}lookupMutationBatch(e,t){return uo(e).get(t).next(e=>e?(y(e.userId===this.userId,48,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),ga(this.serializer,e)):null)}Xn(e,r){return this.Zn[r]?C.resolve(this.Zn[r]):this.lookupMutationBatch(e,r).next(e=>{var t;return e?(t=e.keys(),this.Zn[r]=t):null})}getNextMutationBatchAfterBatchId(e,t){let n=t+1,r=IDBKeyRange.lowerBound([this.userId,n]),i=null;return uo(e).X({index:Pt,range:r},(e,t,r)=>{t.userId===this.userId&&(y(t.batchId>=n,47524,{er:n}),i=ga(this.serializer,t)),r.done()}).next(()=>i)}getHighestUnacknowledgedBatchId(e){var t=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]);let n=Et;return uo(e).X({index:Pt,range:t,reverse:!0},(e,t,r)=>{n=t.batchId,r.done()}).next(()=>n)}getAllMutationBatches(e){var t=IDBKeyRange.bound([this.userId,Et],[this.userId,Number.POSITIVE_INFINITY]);return uo(e).j(Pt,t).next(e=>e.map(e=>ga(this.serializer,e)))}getAllMutationBatchesAffectingDocumentKey(o,u){let e=Bt(this.userId,u.path),t=IDBKeyRange.lowerBound(e),l=[];return lo(o).X({range:t},(t,e,r)=>{let[n,i,s]=t,a=kt(i);if(n===this.userId&&u.path.isEqual(a))return uo(o).get(s).next(e=>{if(!e)throw E(61480,{tr:t,batchId:s});y(e.userId===this.userId,10503,"Unexpected user for mutation batch",{userId:e.userId,batchId:s}),l.push(ga(this.serializer,e))});r.done()}).next(()=>l)}getAllMutationBatchesAffectingDocumentKeys(t,e){let o=new D(S),r=[];return e.forEach(a=>{var e=Bt(this.userId,a.path),e=IDBKeyRange.lowerBound(e),e=lo(t).X({range:e},(e,t,r)=>{var[n,i,s]=e,i=kt(i);n===this.userId&&a.path.isEqual(i)?o=o.add(s):r.done()});r.push(e)}),C.waitFor(r).next(()=>this.nr(t,o))}getAllMutationBatchesAffectingQuery(e,t){let a=t.path,o=a.length+1,r=Bt(this.userId,a),n=IDBKeyRange.lowerBound(r),u=new D(S);return lo(e).X({range:n},(e,t,r)=>{var[n,i,s]=e,i=kt(i);n===this.userId&&a.isPrefixOf(i)?i.length===o&&(u=u.add(s)):r.done()}).next(()=>this.nr(e,u))}nr(e,t){let r=[],n=[];return t.forEach(t=>{n.push(uo(e).get(t).next(e=>{if(null===e)throw E(35274,{batchId:t});y(e.userId===this.userId,9748,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),r.push(ga(this.serializer,e))}))}),C.waitFor(n).next(()=>r)}removeMutationBatch(t,r){return io(t.ce,this.userId,r).next(e=>(t.addOnCommittedListener(()=>{this.rr(r.batchId)}),C.forEach(e,e=>this.referenceDelegate.markPotentiallyOrphaned(t,e))))}rr(e){delete this.Zn[e]}performConsistencyCheck(r){return this.checkEmpty(r).next(e=>{if(!e)return C.resolve();let t=IDBKeyRange.lowerBound([this.userId]),n=[];return lo(r).X({range:t},(t,e,r)=>{if(t[0]===this.userId){let e=kt(t[1]);n.push(e)}else r.done()}).next(()=>{y(0===n.length,56720,{ir:n.map(e=>e.canonicalString())})})})}containsKey(e,t){return oo(e,this.userId,t)}sr(e){return ho(e).get(this.userId).next(e=>e||{userId:this.userId,lastAcknowledgedBatchId:Et,lastStreamToken:""})}}function oo(e,s,t){let r=Bt(s,t.path),a=r[1],n=IDBKeyRange.lowerBound(r),o=!1;return lo(e).X({range:n,Z:!0},(e,t,r)=>{var[n,i,,]=e;n===s&&i===a&&(o=!0),r.done()}).next(()=>o)}function uo(e){return r(e,Vt)}function lo(e){return r(e,zt)}function ho(e){return r(e,Ft)}class co{constructor(e){this._r=e}next(){return this._r+=2,this._r}static ar(){return new co(0)}static ur(){return new co(-1)}}class fo{constructor(e,t){this.referenceDelegate=e,this.serializer=t}allocateTargetId(r){return this.cr(r).next(e=>{var t=new co(e.highestTargetId);return e.highestTargetId=t.next(),this.lr(r,e).next(()=>e.highestTargetId)})}getLastRemoteSnapshotVersion(e){return this.cr(e).next(e=>w.fromTimestamp(new v(e.lastRemoteSnapshotVersion.seconds,e.lastRemoteSnapshotVersion.nanoseconds)))}getHighestSequenceNumber(e){return this.cr(e).next(e=>e.highestListenSequenceNumber)}setTargetsMetadata(t,r,n){return this.cr(t).next(e=>(e.highestListenSequenceNumber=r,n&&(e.lastRemoteSnapshotVersion=n.toTimestamp()),e.highestListenSequenceNumber<r&&(e.highestListenSequenceNumber=r),this.lr(t,e)))}addTargetData(t,r){return this.hr(t,r).next(()=>this.cr(t).next(e=>(e.targetCount+=1,this.Pr(r,e),this.lr(t,e))))}updateTargetData(e,t){return this.hr(e,t)}removeTargetData(t,e){return this.removeMatchingKeysForTargetId(t,e.targetId).next(()=>go(t).delete(e.targetId)).next(()=>this.cr(t)).next(e=>(y(0<e.targetCount,8065),--e.targetCount,this.lr(t,e)))}removeTargets(n,i,s){let a=0,o=[];return go(n).X((e,t)=>{var r=ma(t);r.sequenceNumber<=i&&null===s.get(r.targetId)&&(a++,o.push(this.removeTargetData(n,r)))}).next(()=>C.waitFor(o)).next(()=>a)}forEachTarget(e,n){return go(e).X((e,t)=>{var r=ma(t);n(r)})}cr(e){return mo(e).get(sr).next(e=>(y(null!==e,2888),e))}lr(e,t){return mo(e).put(sr,t)}hr(e,t){return go(e).put(pa(this.serializer,t))}Pr(e,t){let r=!1;return e.targetId>t.highestTargetId&&(t.highestTargetId=e.targetId,r=!0),t.highestListenSequenceNumber<e.sequenceNumber&&(t.highestListenSequenceNumber=e.sequenceNumber,r=!0),r}getTargetCount(e){return this.cr(e).next(e=>e.targetCount)}getTargetData(e,i){var t=li(i),t=IDBKeyRange.bound([t,Number.NEGATIVE_INFINITY],[t,Number.POSITIVE_INFINITY]);let s=null;return go(e).X({range:t,index:Zt},(e,t,r)=>{var n=ma(t);hi(i,n.target)&&(s=n,r.done())}).next(()=>s)}addMatchingKeys(r,e,n){let i=[],s=po(r);return e.forEach(e=>{var t=Dt(e.path);i.push(s.put({targetId:n,path:t})),i.push(this.referenceDelegate.addReference(r,n,e))}),C.waitFor(i)}removeMatchingKeys(r,e,n){let i=po(r);return C.forEach(e,e=>{var t=Dt(e.path);return C.waitFor([i.delete([n,t]),this.referenceDelegate.removeReference(r,n,e)])})}removeMatchingKeysForTargetId(e,t){var r=po(e),n=IDBKeyRange.bound([t],[t+1],!1,!0);return r.delete(n)}getMatchingKeysForTargetId(e,t){var r=IDBKeyRange.bound([t],[t+1],!1,!0),n=po(e);let i=F();return n.X({range:r,Z:!0},(e,t,r)=>{var n=kt(e[1]),n=new x(n);i=i.add(n)}).next(()=>i)}containsKey(e,t){var r=Dt(t.path),r=IDBKeyRange.bound([r],[Me(r)],!1,!0);let n=0;return po(e).X({index:nr,Z:!0,range:r},([e],t,r)=>{0!==e&&(n++,r.done())}).next(()=>0<n)}Et(e,t){return go(e).get(t).next(e=>e?ma(e):null)}}function go(e){return r(e,Jt)}function mo(e){return r(e,ar)}function po(e){return r(e,tr)}let yo="LruGarbageCollector";function vo([e,t],[r,n]){var i=S(e,r);return 0===i?S(t,n):i}class wo{constructor(e){this.Tr=e,this.buffer=new D(vo),this.Ir=0}dr(){return++this.Ir}Er(e){var t=[e,this.dr()];if(this.buffer.size<this.Tr)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();vo(t,e)<0&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class _o{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Ar=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Rr(6e4)}stop(){this.Ar&&(this.Ar.cancel(),this.Ar=null)}get started(){return null!==this.Ar}Rr(e){p(yo,`Garbage collection scheduled in ${e}ms`),this.Ar=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Ar=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){mt(e)?p(yo,"Ignoring IndexedDB error during garbage collection: ",e):await ut(e)}await this.Rr(3e5)})}}class bo{constructor(e,t){this.Vr=e,this.params=t}calculateTargetCount(e,t){return this.Vr.mr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return C.resolve(Tt.ue);let r=new wo(t);return this.Vr.forEachTarget(e,e=>r.Er(e.sequenceNumber)).next(()=>this.Vr.gr(e,e=>r.Er(e))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.Vr.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.Vr.removeOrphanedDocuments(e,t)}collect(t,r){return-1===this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector","Garbage collection skipped; disabled"),C.resolve(ro)):this.getCacheSize(t).next(e=>e<this.params.cacheSizeCollectionThreshold?(p("LruGarbageCollector",`Garbage collection skipped; Cache size ${e} is lower than threshold `+this.params.cacheSizeCollectionThreshold),ro):this.pr(t,r))}getCacheSize(e){return this.Vr.getCacheSize(e)}pr(t,r){let n,i,s,a,o,u,l,h=Date.now();return this.calculateTargetCount(t,this.params.percentileToCollect).next(e=>(i=e>this.params.maximumSequenceNumbersToCollect?(p("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from `+e),this.params.maximumSequenceNumbersToCollect):e,a=Date.now(),this.nthSequenceNumber(t,i))).next(e=>(n=e,o=Date.now(),this.removeTargets(t,n,r))).next(e=>(s=e,u=Date.now(),this.removeOrphanedDocuments(t,n))).next(e=>(l=Date.now(),_e()<=c.DEBUG&&p("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${a-h}ms
	Determined least recently used ${i} in `+(o-a)+"ms\n"+`	Removed ${s} targets in `+(u-o)+"ms\n"+`	Removed ${e} documents in `+(l-u)+"ms\n"+`Total Duration: ${l-h}ms`),C.resolve({didRun:!0,sequenceNumbersCollected:i,targetsRemoved:s,documentsRemoved:e})))}}function Io(e,t){return new bo(e,t)}class To{constructor(e,t){this.db=e,this.garbageCollector=Io(this,t)}mr(e){let r=this.yr(e);return this.db.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}yr(e){let t=0;return this.gr(e,e=>{t++}).next(()=>t)}forEachTarget(e,t){return this.db.getTargetCache().forEachTarget(e,t)}gr(e,r){return this.wr(e,(e,t)=>r(t))}addReference(e,t,r){return Eo(e,r)}removeReference(e,t,r){return Eo(e,r)}removeTargets(e,t,r){return this.db.getTargetCache().removeTargets(e,t,r)}markPotentiallyOrphaned(e,t){return Eo(e,t)}Sr(e,r){{var n=e,i=r;let t=!1;return ho(n).ee(e=>oo(n,e,i).next(e=>(e&&(t=!0),C.resolve(!e)))).next(()=>t)}}removeOrphanedDocuments(r,n){let i=this.db.getRemoteDocumentCache().newChangeBuffer(),s=[],a=0;return this.wr(r,(t,e)=>{if(e<=n){let e=this.Sr(r,t).next(e=>{if(!e)return a++,i.getEntry(r,t).next(()=>(i.removeEntry(t,w.min()),po(r).delete([0,Dt(t.path)])))});s.push(e)}}).next(()=>C.waitFor(s)).next(()=>i.apply(r)).next(()=>a)}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(e,r)}updateLimboDocument(e,t){return Eo(e,t)}wr(e,n){var t=po(e);let i,s=Tt.ue;return t.X({index:nr},([e],{path:t,sequenceNumber:r})=>{0===e?(s!==Tt.ue&&n(new x(kt(i)),s),s=r,i=t):s=Tt.ue}).next(()=>{s!==Tt.ue&&n(new x(kt(i)),s)})}getCacheSize(e){return this.db.getRemoteDocumentCache().getSize(e)}}function Eo(e,t){return po(e).put((e=e.currentSequenceNumber,{targetId:0,path:Dt(t.path),sequenceNumber:e}))}class So{constructor(){this.changes=new Ni(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,R.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();var r=this.changes.get(t);return void 0!==r?C.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class xo{constructor(e){this.serializer=e}setIndexManager(e){this.indexManager=e}addEntry(e,t,r){return No(e).put(r)}removeEntry(e,t,r){return No(e).delete((e=r,[(n=t.path.toArray()).slice(0,n.length-2),n[n.length-2],ca(e),n[n.length-1]]));var n}updateMetadata(t,r){return this.getMetadata(t).next(e=>(e.byteSize+=r,this.br(t,e)))}getEntry(e,r){let n=R.newInvalidDocument(r);return No(e).X({index:$t,range:IDBKeyRange.only(ko(r))},(e,t)=>{n=this.Dr(r,t)}).next(()=>n)}vr(e,r){let n={size:0,document:R.newInvalidDocument(r)};return No(e).X({index:$t,range:IDBKeyRange.only(ko(r))},(e,t)=>{n={document:this.Dr(r,t),size:so(t)}}).next(()=>n)}getEntries(e,t){let n=ki;return this.Cr(e,t,(e,t)=>{var r=this.Dr(e,t);n=n.insert(e,r)}).next(()=>n)}Fr(e,t){let n=ki,i=new A(x.comparator);return this.Cr(e,t,(e,t)=>{var r=this.Dr(e,t);n=n.insert(e,r),i=i.insert(e,so(t))}).next(()=>({documents:n,Mr:i}))}Cr(e,t,i){if(t.isEmpty())return C.resolve();let r=new D(Oo),n=(t.forEach(e=>r=r.add(e)),IDBKeyRange.bound(ko(r.first()),ko(r.last()))),s=r.getIterator(),a=s.getNext();return No(e).X({index:$t,range:n},(e,t,r)=>{for(var n=x.fromSegments([...t.prefixPath,t.collectionGroup,t.documentId]);a&&Oo(a,n)<0;)i(a,null),a=s.getNext();a&&a.isEqual(n)&&(i(a,t),a=s.hasNext()?s.getNext():null),a?r.G(ko(a)):r.done()}).next(()=>{for(;a;)i(a,null),a=s.hasNext()?s.getNext():null})}getDocumentsMatchingQuery(e,n,t,i,s){var r=n.path,a=[r.popLast().toArray(),r.lastSegment(),ca(t.readTime),t.documentKey.path.isEmpty()?"":t.documentKey.path.lastSegment()],r=[r.popLast().toArray(),r.lastSegment(),[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],""];return No(e).j(IDBKeyRange.bound(a,r,!0)).next(e=>{null!=s&&s.incrementDocumentReadCount(e.length);let t=ki;for(let r of e){let e=this.Dr(x.fromSegments(r.prefixPath.concat(r.collectionGroup,r.documentId)),r);e.isFoundDocument()&&(Ci(n,e)||i.has(e.key))&&(t=t.insert(e.key,e))}return t})}getAllFromCollectionGroup(e,t,r,i){let s=ki;var n=Ro(t,r),a=Ro(t,it.max());return No(e).X({index:Ht,range:IDBKeyRange.bound(n,a,!0)},(e,t,r)=>{var n=this.Dr(x.fromSegments(t.prefixPath.concat(t.collectionGroup,t.documentId)),t);(s=s.insert(n.key,n)).size===i&&r.done()}).next(()=>s)}newChangeBuffer(e){return new Ao(this,!!e&&e.trackRemovals)}getSize(e){return this.getMetadata(e).next(e=>e.byteSize)}getMetadata(e){return Do(e).get(Yt).next(e=>(y(!!e,20021),e))}br(e,t){return Do(e).put(Yt,t)}Dr(e,t){if(t){let e=((e,r)=>{let n;if(r.document)n=Ws(e.gt,r.document,!!r.hasCommittedMutations);else if(r.noDocument){let e=x.fromSegments(r.noDocument.path),t=fa(r.noDocument.readTime);n=R.newNoDocument(e,t),r.hasCommittedMutations&&n.setHasCommittedMutations()}else{if(!r.unknownDocument)return E(56709);{let e=x.fromSegments(r.unknownDocument.path),t=fa(r.unknownDocument.version);n=R.newUnknownDocument(e,t)}}return r.readTime&&n.setReadTime((e=r.readTime,t=new v(e[0],e[1]),w.fromTimestamp(t))),n;var t})(this.serializer,t);if(!e.isNoDocument()||!e.version.isEqual(w.min()))return e}return R.newInvalidDocument(e)}}function Co(e){return new xo(e)}class Ao extends So{constructor(e,t){super(),this.Or=e,this.trackRemovals=t,this.Nr=new Ni(e=>e.toString(),(e,t)=>e.isEqual(t))}applyChanges(s){let a=[],o=0,u=new D((e,t)=>S(e.canonicalString(),t.canonicalString()));return this.changes.forEach((t,r)=>{var e=this.Nr.get(t);if(a.push(this.Or.removeEntry(s,t,e.readTime)),r.isValidDocument()){var n=ha(this.Or.serializer,r),i=(u=u.add(t.path.popLast()),so(n));o+=i-e.size,a.push(this.Or.addEntry(s,t,n))}else if(o-=e.size,this.trackRemovals){let e=ha(this.Or.serializer,r.convertToNoDocument(w.min()));a.push(this.Or.addEntry(s,t,e))}}),u.forEach(e=>{a.push(this.Or.indexManager.addToCollectionParentIndex(s,e))}),a.push(this.Or.updateMetadata(s,o)),C.waitFor(a)}getFromCache(e,t){return this.Or.vr(e,t).next(e=>(this.Nr.set(t,{size:e.size,readTime:e.document.readTime}),e.document))}getAllFromCache(e,t){return this.Or.Fr(e,t).next(({documents:r,Mr:e})=>(e.forEach((e,t)=>{this.Nr.set(e,{size:t,readTime:r.get(e).readTime})}),r))}}function Do(e){return r(e,Xt)}function No(e){return r(e,Kt)}function ko(e){var t=e.path.toArray();return[t.slice(0,t.length-2),t[t.length-2],t[t.length-1]]}function Ro(e,t){var r=t.documentKey.path.toArray();return[e,ca(t.readTime),r.slice(0,r.length-2),0<r.length?r[r.length-1]:""]}function Oo(e,t){var r=e.path.toArray(),n=t.path.toArray();let i=0;for(let s=0;s<r.length-2&&s<n.length-2;++s)if(i=S(r[s],n[s]))return i;return(i=S(r.length,n.length))||(i=S(r[r.length-2],n[n.length-2]))||S(r[r.length-1],n[n.length-1])}class Lo{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class Fo{constructor(e,t,r,n){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=n}getDocument(t,r){let n=null;return this.documentOverlayCache.getOverlay(t,r).next(e=>(n=e,this.remoteDocumentCache.getEntry(t,r))).next(e=>(null!==n&&ns(n.mutation,e,en.empty(),v.now()),e))}getDocuments(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.getLocalViewOfDocuments(t,e,F()).next(()=>e))}getLocalViewOfDocuments(e,t,r=F()){let n=Fi();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,r).next(e=>{let r=Oi();return e.forEach((e,t)=>{r=r.insert(e,t.overlayedDocument)}),r}))}getOverlayedDocuments(e,t){let r=Fi();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,F()))}populateOverlays(e,r,t){let n=[];return t.forEach(e=>{r.has(e)||n.push(e)}),this.documentOverlayCache.getOverlays(e,n).next(e=>{e.forEach((e,t)=>{r.set(e,t)})})}computeViews(e,t,n,i){let s=ki,a=Fi(),o=Fi();return t.forEach((e,t)=>{var r=n.get(t.key);i.has(t.key)&&(void 0===r||r.mutation instanceof as)?s=s.insert(t.key,t):void 0!==r?(a.set(t.key,r.mutation.getFieldMask()),ns(r.mutation,t,r.mutation.getFieldMask(),v.now())):a.set(t.key,en.empty())}),this.recalculateAndSaveOverlays(e,s).next(e=>(e.forEach((e,t)=>a.set(e,t)),t.forEach((e,t)=>{var r;return o.set(e,new Lo(t,null!=(r=a.get(e))?r:null))}),o))}recalculateAndSaveOverlays(a,o){let u=Fi(),l=new A((e,t)=>e-t),h=F();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(a,o).next(e=>{for(let n of e)n.keys().forEach(e=>{var t,r=o.get(e);null!==r&&(t=u.get(e)||en.empty(),t=n.applyToLocalView(r,t),u.set(e,t),r=(l.get(n.batchId)||F()).add(e),l=l.insert(n.batchId,r))})}).next(()=>{for(var i=[],s=l.getReverseIterator();s.hasNext();){let e=s.getNext(),t=e.key,r=e.value,n=Fi();r.forEach(e=>{var t;h.has(e)||(null!==(t=rs(o.get(e),u.get(e)))&&n.set(e,t),h=h.add(e))}),i.push(this.documentOverlayCache.saveOverlays(a,t,n))}return C.waitFor(i)}).next(()=>u)}recalculateAndSaveOverlaysForDocumentKeys(t,e){return this.remoteDocumentCache.getEntries(t,e).next(e=>this.recalculateAndSaveOverlays(t,e))}getDocumentsMatchingQuery(e,t,r,n){return i=t,x.isDocumentKey(i.path)&&null===i.collectionGroup&&0===i.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):wi(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,n):this.getDocumentsMatchingCollectionQuery(e,t,r,n);var i}getNextDocuments(s,t,a,o){return this.remoteDocumentCache.getAllFromCollectionGroup(s,t,a,o).next(r=>{var e=0<o-r.size?this.documentOverlayCache.getOverlaysForCollectionGroup(s,t,a.largestBatchId,o-r.size):C.resolve(Fi());let n=Xe,i=r;return e.next(e=>C.forEach(e,(t,e)=>(n<e.largestBatchId&&(n=e.largestBatchId),r.get(t)?C.resolve():this.remoteDocumentCache.getEntry(s,t).next(e=>{i=i.insert(t,e)}))).next(()=>this.populateOverlays(s,e,r)).next(()=>this.computeViews(s,i,e,F())).next(e=>({batchId:n,changes:Li(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new x(t)).next(e=>{let t=Oi();return t=e.isFoundDocument()?t.insert(e.key,e):t})}getDocumentsMatchingCollectionGroupQuery(n,i,s,a){let o=i.collectionGroup,u=Oi();return this.indexManager.getCollectionParents(n,o).next(e=>C.forEach(e,e=>{t=i,e=e.child(o);var t,r=new mi(e,null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(n,r,s,a).next(e=>{e.forEach((e,t)=>{u=u.insert(e,t)})})}).next(()=>u))}getDocumentsMatchingCollectionQuery(t,s,r,n){let a;return this.documentOverlayCache.getOverlaysForCollection(t,s.path,r.largestBatchId).next(e=>(a=e,this.remoteDocumentCache.getDocumentsMatchingQuery(t,s,r,a,n))).next(n=>{a.forEach((e,t)=>{var r=t.getKey();null===n.get(r)&&(n=n.insert(r,R.newInvalidDocument(r)))});let i=Oi();return n.forEach((e,t)=>{var r=a.get(e);void 0!==r&&ns(r.mutation,t,en.empty(),v.now()),Ci(s,t)&&(i=i.insert(e,t))}),i})}}class Vo{constructor(e){this.serializer=e,this.Br=new Map,this.Lr=new Map}getBundleMetadata(e,t){return C.resolve(this.Br.get(t))}saveBundleMetadata(e,t){return this.Br.set(t.id,{id:t.id,version:t.version,createTime:M(t.createTime)}),C.resolve()}getNamedQuery(e,t){return C.resolve(this.Lr.get(t))}saveNamedQuery(e,t){return this.Lr.set(t.name,{name:(t=t).name,query:ya(t.bundledQuery),readTime:M(t.readTime)}),C.resolve()}}class Mo{constructor(){this.overlays=new A(x.comparator),this.kr=new Map}getOverlay(e,t){return C.resolve(this.overlays.get(t))}getOverlays(e,t){let r=Fi();return C.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(r,n,e){return e.forEach((e,t)=>{this.wt(r,n,t)}),C.resolve()}removeOverlaysForBatchId(e,t,r){var n=this.kr.get(r);return void 0!==n&&(n.forEach(e=>this.overlays=this.overlays.remove(e)),this.kr.delete(r)),C.resolve()}getOverlaysForCollection(e,r,n){let i=Fi(),s=r.length+1,t=new x(r.child("")),a=this.overlays.getIteratorFrom(t);for(;a.hasNext();){let e=a.getNext().value,t=e.getKey();if(!r.isPrefixOf(t.path))break;t.path.length===s&&e.largestBatchId>n&&i.set(e.getKey(),e)}return C.resolve(i)}getOverlaysForCollectionGroup(e,r,n,t){let i=new A((e,t)=>e-t);for(var s=this.overlays.getIterator();s.hasNext();){let t=s.getNext().value;if(t.getKey().getCollectionGroup()===r&&t.largestBatchId>n){let e=i.get(t.largestBatchId);null===e&&(e=Fi(),i=i.insert(t.largestBatchId,e)),e.set(t.getKey(),t)}}let a=Fi(),o=i.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=t)););return C.resolve(a)}wt(e,t,r){var n=this.overlays.get(r.key);if(null!==n){let e=this.kr.get(n.largestBatchId).delete(r.key);this.kr.set(n.largestBatchId,e)}this.overlays=this.overlays.insert(r.key,new gs(t,r));let i=this.kr.get(t);void 0===i&&(i=F(),this.kr.set(t,i)),this.kr.set(t,i.add(r.key))}}class Po{constructor(){this.sessionToken=N.EMPTY_BYTE_STRING}getSessionToken(e){return C.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,C.resolve()}}class Uo{constructor(){this.qr=new D(o.Qr),this.$r=new D(o.Ur)}isEmpty(){return this.qr.isEmpty()}addReference(e,t){var r=new o(e,t);this.qr=this.qr.add(r),this.$r=this.$r.add(r)}Kr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Wr(new o(e,t))}Gr(e,t){e.forEach(e=>this.removeReference(e,t))}zr(e){let t=new x(new T([])),r=new o(t,e),n=new o(t,e+1),i=[];return this.$r.forEachInRange([r,n],e=>{this.Wr(e),i.push(e.key)}),i}jr(){this.qr.forEach(e=>this.Wr(e))}Wr(e){this.qr=this.qr.delete(e),this.$r=this.$r.delete(e)}Jr(e){var t=new x(new T([])),r=new o(t,e),t=new o(t,e+1);let n=F();return this.$r.forEachInRange([r,t],e=>{n=n.add(e.key)}),n}containsKey(e){var t=new o(e,0),t=this.qr.firstAfterOrEqual(t);return null!==t&&e.isEqual(t.key)}}class o{constructor(e,t){this.key=e,this.Hr=t}static Qr(e,t){return x.comparator(e.key,t.key)||S(e.Hr,t.Hr)}static Ur(e,t){return S(e.Hr,t.Hr)||x.comparator(e.key,t.key)}}class Bo{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.er=1,this.Yr=new D(o.Qr)}checkEmpty(e){return C.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,r,n){var i=this.er,s=(this.er++,0<this.mutationQueue.length&&this.mutationQueue[this.mutationQueue.length-1],new ds(i,t,r,n));this.mutationQueue.push(s);for(let t of n)this.Yr=this.Yr.add(new o(t.key,i)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return C.resolve(s)}lookupMutationBatch(e,t){return C.resolve(this.Zr(t))}getNextMutationBatchAfterBatchId(e,t){var r=this.Xr(t+1),r=r<0?0:r;return C.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return C.resolve(0===this.mutationQueue.length?Et:this.er-1)}getAllMutationBatches(e){return C.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new o(t,0),n=new o(t,Number.POSITIVE_INFINITY),i=[];return this.Yr.forEachInRange([r,n],e=>{var t=this.Zr(e.Hr);i.push(t)}),C.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new D(S);return t.forEach(e=>{var t=new o(e,0),r=new o(e,Number.POSITIVE_INFINITY);this.Yr.forEachInRange([t,r],e=>{n=n.add(e.Hr)})}),C.resolve(this.ei(n))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,n=r.length+1,i=r;x.isDocumentKey(i)||(i=i.child(""));var s=new o(new x(i),0);let a=new D(S);return this.Yr.forEachWhile(e=>{var t=e.key.path;return!!r.isPrefixOf(t)&&(t.length===n&&(a=a.add(e.Hr)),!0)},s),C.resolve(this.ei(a))}ei(e){let r=[];return e.forEach(e=>{var t=this.Zr(e);null!==t&&r.push(t)}),r}removeMutationBatch(r,n){y(0===this.ti(n.batchId,"removed"),55003),this.mutationQueue.shift();let i=this.Yr;return C.forEach(n.mutations,e=>{var t=new o(e.key,n.batchId);return i=i.delete(t),this.referenceDelegate.markPotentiallyOrphaned(r,e.key)}).next(()=>{this.Yr=i})}rr(e){}containsKey(e,t){var r=new o(t,0),r=this.Yr.firstAfterOrEqual(r);return C.resolve(t.isEqual(r&&r.key))}performConsistencyCheck(e){return this.mutationQueue.length,C.resolve()}ti(e,t){return this.Xr(e)}Xr(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}Zr(e){var t=this.Xr(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class qo{constructor(e){this.ni=e,this.docs=new A(x.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){var r=t.key,n=this.docs.get(r),n=n?n.size:0,i=this.ni(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:i}),this.size+=i-n,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){var t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){var r=this.docs.get(t);return C.resolve(r?r.document.mutableCopy():R.newInvalidDocument(t))}getEntries(e,t){let r=ki;return t.forEach(e=>{var t=this.docs.get(e);r=r.insert(e,t?t.document.mutableCopy():R.newInvalidDocument(e))}),C.resolve(r)}getDocumentsMatchingQuery(e,r,n,i){let s=ki,a=r.path,t=new x(a.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(t);for(;o.hasNext();){let{key:e,value:{document:t}}=o.getNext();if(!a.isPrefixOf(e.path))break;e.path.length>a.length+1||st(nt(t),n)<=0||(i.has(t.key)||Ci(r,t))&&(s=s.insert(t.key,t.mutableCopy()))}return C.resolve(s)}getAllFromCollectionGroup(e,t,r,n){E(9500)}ri(e,t){return C.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new jo(this)}getSize(e){return C.resolve(this.size)}}class jo extends So{constructor(e){super(),this.Or=e}applyChanges(r){let n=[];return this.changes.forEach((e,t)=>{t.isValidDocument()?n.push(this.Or.addEntry(r,t)):this.Or.removeEntry(e)}),C.waitFor(n)}getFromCache(e,t){return this.Or.getEntry(e,t)}getAllFromCache(e,t){return this.Or.getEntries(e,t)}}class zo{constructor(e){this.persistence=e,this.ii=new Ni(e=>li(e),hi),this.lastRemoteSnapshotVersion=w.min(),this.highestTargetId=0,this.si=0,this.oi=new Uo,this.targetCount=0,this._i=co.ar()}forEachTarget(e,r){return this.ii.forEach((e,t)=>r(t)),C.resolve()}getLastRemoteSnapshotVersion(e){return C.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return C.resolve(this.si)}allocateTargetId(e){return this.highestTargetId=this._i.next(),C.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this.si&&(this.si=t),C.resolve()}hr(e){this.ii.set(e.target,e);var t=e.targetId;t>this.highestTargetId&&(this._i=new co(t),this.highestTargetId=t),e.sequenceNumber>this.si&&(this.si=e.sequenceNumber)}addTargetData(e,t){return this.hr(t),this.targetCount+=1,C.resolve()}updateTargetData(e,t){return this.hr(t),C.resolve()}removeTargetData(e,t){return this.ii.delete(t.target),this.oi.zr(t.targetId),--this.targetCount,C.resolve()}removeTargets(r,n,i){let s=0,a=[];return this.ii.forEach((e,t)=>{t.sequenceNumber<=n&&null===i.get(t.targetId)&&(this.ii.delete(e),a.push(this.removeMatchingKeysForTargetId(r,t.targetId)),s++)}),C.waitFor(a).next(()=>s)}getTargetCount(e){return C.resolve(this.targetCount)}getTargetData(e,t){var r=this.ii.get(t)||null;return C.resolve(r)}addMatchingKeys(e,t,r){return this.oi.Kr(t,r),C.resolve()}removeMatchingKeys(t,e,r){this.oi.Gr(e,r);let n=this.persistence.referenceDelegate,i=[];return n&&e.forEach(e=>{i.push(n.markPotentiallyOrphaned(t,e))}),C.waitFor(i)}removeMatchingKeysForTargetId(e,t){return this.oi.zr(t),C.resolve()}getMatchingKeysForTargetId(e,t){var r=this.oi.Jr(t);return C.resolve(r)}containsKey(e,t){return C.resolve(this.oi.containsKey(t))}}class Ko{constructor(e,t){this.ai={},this.overlays={},this.ui=new Tt(0),this.ci=!1,this.ci=!0,this.li=new Po,this.referenceDelegate=e(this),this.hi=new zo(this),this.indexManager=new $a,this.remoteDocumentCache=(e=e=>this.referenceDelegate.Pi(e),new qo(e)),this.serializer=new la(t),this.Ti=new Vo(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.ci=!1,Promise.resolve()}get started(){return this.ci}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new Mo,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ai[e.toKey()];return r||(r=new Bo(t,this.referenceDelegate),this.ai[e.toKey()]=r),r}getGlobalsCache(){return this.li}getTargetCache(){return this.hi}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ti}runTransaction(e,t,r){p("MemoryPersistence","Starting transaction:",e);let n=new Go(this.ui.next());return this.referenceDelegate.Ii(),r(n).next(e=>this.referenceDelegate.di(n).next(()=>e)).toPromise().then(e=>(n.raiseOnCommittedEvent(),e))}Ei(t,r){return C.or(Object.values(this.ai).map(e=>()=>e.containsKey(t,r)))}}class Go extends ot{constructor(e){super(),this.currentSequenceNumber=e}}class $o{constructor(e){this.persistence=e,this.Ai=new Uo,this.Ri=null}static Vi(e){return new $o(e)}get mi(){if(this.Ri)return this.Ri;throw E(60996)}addReference(e,t,r){return this.Ai.addReference(r,t),this.mi.delete(r.toString()),C.resolve()}removeReference(e,t,r){return this.Ai.removeReference(r,t),this.mi.add(r.toString()),C.resolve()}markPotentiallyOrphaned(e,t){return this.mi.add(t.toString()),C.resolve()}removeTarget(e,t){this.Ai.zr(t.targetId).forEach(e=>this.mi.add(e.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.mi.add(e.toString()))}).next(()=>r.removeTargetData(e,t))}Ii(){this.Ri=new Set}di(r){let n=this.persistence.getRemoteDocumentCache().newChangeBuffer();return C.forEach(this.mi,e=>{let t=x.fromPath(e);return this.fi(r,t).next(e=>{e||n.removeEntry(t,w.min())})}).next(()=>(this.Ri=null,n.apply(r)))}updateLimboDocument(e,t){return this.fi(e,t).next(e=>{e?this.mi.delete(t.toString()):this.mi.add(t.toString())})}Pi(e){return 0}fi(e,t){return C.or([()=>C.resolve(this.Ai.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ei(e,t)])}}class Qo{constructor(e,t){this.persistence=e,this.gi=new Ni(e=>Dt(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=Io(this,t)}static Vi(e,t){return new Qo(e,t)}Ii(){}di(e){return C.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}mr(e){let r=this.yr(e);return this.persistence.getTargetCache().getTargetCount(e).next(t=>r.next(e=>t+e))}yr(e){let t=0;return this.gr(e,e=>{t++}).next(()=>t)}gr(r,n){return C.forEach(this.gi,(e,t)=>this.Sr(r,e,t).next(e=>e?C.resolve():n(t)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,r){let n=0,t=this.persistence.getRemoteDocumentCache(),i=t.newChangeBuffer();return t.ri(e,t=>this.Sr(e,t,r).next(e=>{e||(n++,i.removeEntry(t,w.min()))})).next(()=>i.apply(e)).next(()=>n)}markPotentiallyOrphaned(e,t){return this.gi.set(t,e.currentSequenceNumber),C.resolve()}removeTarget(e,t){var r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.gi.set(r,e.currentSequenceNumber),C.resolve()}removeReference(e,t,r){return this.gi.set(r,e.currentSequenceNumber),C.resolve()}updateLimboDocument(e,t){return this.gi.set(t,e.currentSequenceNumber),C.resolve()}Pi(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function n(e){switch(In(e)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:var t=cn(e);return t?16+n(t):16;case 5:return 2*e.stringValue.length;case 6:return sn(e.bytesValue).approximateByteSize();case 7:return e.referenceValue.length;case 9:return(e.arrayValue.values||[]).reduce((e,t)=>e+n(t),0);case 10:case 11:{var i=e.mapValue;let r=0;return Hr(i.fields,(e,t)=>{r+=e.length+n(t)}),r}default:throw E(13486,{value:e})}}(e.data.value)),t}Sr(e,t,r){return C.or([()=>this.persistence.Ei(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{var e=this.gi.get(t);return C.resolve(void 0!==e&&r<e)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class Ho{constructor(e){this.serializer=e}q(t,e,r,n){let s=new ht("createOrUpgrade",e);var i;r<1&&1<=n&&(t.createObjectStore(Ot),(i=t).createObjectStore(Ft,{keyPath:"userId"}),i.createObjectStore(Vt,{keyPath:Mt,autoIncrement:!0}).createIndex(Pt,Ut,{unique:!0}),i.createObjectStore(zt),Wo(t),t.createObjectStore(Rt));let a=C.resolve();return r<3&&3<=n&&(0!==r&&((i=t).deleteObjectStore(tr),i.deleteObjectStore(Jt),i.deleteObjectStore(ar),Wo(t)),a=a.next(()=>{return e=s,t=e.store(ar),r={highestTargetId:0,highestListenSequenceNumber:0,lastRemoteSnapshotVersion:w.min().toTimestamp(),targetCount:0},t.put(sr,r);var e,t,r})),r<4&&4<=n&&(a=(a=0!==r?a.next(()=>{return n=t,(i=s).store(Vt).j().next(e=>{n.deleteObjectStore(Vt),n.createObjectStore(Vt,{keyPath:Mt,autoIncrement:!0}).createIndex(Pt,Ut,{unique:!0});let t=i.store(Vt),r=e.map(e=>t.put(e));return C.waitFor(r)});var n,i}):a).next(()=>{t.createObjectStore(lr,{keyPath:"clientId"})})),r<5&&5<=n&&(a=a.next(()=>this.pi(s))),r<6&&6<=n&&(a=a.next(()=>(t.createObjectStore(Xt),this.yi(s)))),r<7&&7<=n&&(a=a.next(()=>this.wi(s))),r<8&&8<=n&&(a=a.next(()=>this.Si(t,s))),r<9&&9<=n&&(a=a.next(()=>{var e;(e=t).objectStoreNames.contains("remoteDocumentChanges")&&e.deleteObjectStore("remoteDocumentChanges")})),r<10&&10<=n&&(a=a.next(()=>this.bi(s))),r<11&&11<=n&&(a=a.next(()=>{t.createObjectStore(hr,{keyPath:"bundleId"}),t.createObjectStore(cr,{keyPath:"name"})})),r<12&&12<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(kr,{keyPath:Rr})).createIndex(Or,Lr,{unique:!1}),e.createIndex(Fr,Vr,{unique:!1})})),r<13&&13<=n&&(a=a.next(()=>{var e;(e=t.createObjectStore(Kt,{keyPath:Gt})).createIndex($t,Qt),e.createIndex(Ht,Wt)}).next(()=>this.Di(t,s)).next(()=>t.deleteObjectStore(Rt))),r<14&&14<=n&&(a=a.next(()=>this.Ci(t,s))),r<15&&15<=n&&(a=a.next(()=>{var e;(e=t).createObjectStore(dr,{keyPath:"indexId",autoIncrement:!0}).createIndex(fr,"collectionGroup",{unique:!1}),e.createObjectStore(gr,{keyPath:mr}).createIndex(Sr,xr,{unique:!1}),e.createObjectStore(Cr,{keyPath:Ar}).createIndex(Dr,Nr,{unique:!1})})),r<16&&16<=n&&(a=a.next(()=>{e.objectStore(gr).clear()}).next(()=>{e.objectStore(Cr).clear()})),r<17&&17<=n&&(a=a.next(()=>{t.createObjectStore(Mr,{keyPath:"name"})})),a=r<18&&18<=n&&ne()?a.next(()=>{e.objectStore(gr).clear()}).next(()=>{e.objectStore(Cr).clear()}):a}yi(t){let r=0;return t.store(Rt).X((e,t)=>{r+=so(t)}).next(()=>{var e={byteSize:r};return t.store(Xt).put(Yt,e)})}pi(n){let e=n.store(Ft),t=n.store(Vt);return e.j().next(e=>C.forEach(e,r=>{var e=IDBKeyRange.bound([r.userId,Et],[r.userId,r.lastAcknowledgedBatchId]);return t.j(Pt,e).next(e=>C.forEach(e,e=>{y(e.userId===r.userId,18650,"Cannot process batch from unexpected user",{batchId:e.batchId});var t=ga(this.serializer,e);return io(n,r.userId,t).next(()=>{})}))}))}wi(e){let a=e.store(tr),t=e.store(Rt);return e.store(ar).get(sr).next(i=>{let s=[];return t.X((e,t)=>{let r=new T(e),n=[0,Dt(r)];s.push(a.get(n).next(e=>e?C.resolve():(e=r,a.put({targetId:0,path:Dt(e),sequenceNumber:i.highestListenSequenceNumber}))))}).next(()=>C.waitFor(s))})}Si(e,t){e.createObjectStore(or,{keyPath:ur});let n=t.store(or),i=new Qa,s=r=>{if(i.add(r)){let e=r.lastSegment(),t=r.popLast();return n.put({collectionId:e,parent:Dt(t)})}};return t.store(Rt).X({Z:!0},(e,t)=>{var r=new T(e);return s(r.popLast())}).next(()=>t.store(zt).X({Z:!0},([,e],t)=>{var r=kt(e);return s(r.popLast())}))}bi(e){let n=e.store(Jt);return n.X((e,t)=>{var r=ma(t),r=pa(this.serializer,r);return n.put(r)})}Di(e,s){let t=s.store(Rt),a=[];return t.X((e,t)=>{var r,n=s.store(Kt),i=((r=t).document?new x(T.fromString(r.document.name).popFirst(5)):r.noDocument?x.fromSegments(r.noDocument.path):r.unknownDocument?x.fromSegments(r.unknownDocument.path):E(36783)).path.toArray(),i={prefixPath:i.slice(0,i.length-2),collectionGroup:i[i.length-2],documentId:i[i.length-1],readTime:t.readTime||[0,0],unknownDocument:t.unknownDocument,noDocument:t.noDocument,document:t.document,hasCommittedMutations:!!t.hasCommittedMutations};a.push(n.put(i))}).next(()=>C.waitFor(a))}Ci(e,s){let t=s.store(Vt),a=Co(this.serializer),o=new Ko($o.Vi,this.serializer.gt);return t.j().next(e=>{let n=new Map;return e.forEach(e=>{let t,r=null!=(t=n.get(e.userId))?t:F();ga(this.serializer,e).keys().forEach(e=>r=r.add(e)),n.set(e.userId,r)}),C.forEach(n,(e,t)=>{var r=new l(t),n=Ea.yt(this.serializer,r),i=o.getIndexManager(r),r=ao.yt(r,this.serializer,i,o.referenceDelegate);return new Fo(a,r,n,i).recalculateAndSaveOverlaysForDocumentKeys(new $r(s,Tt.ue),e).next()})})}}function Wo(e){e.createObjectStore(tr,{keyPath:rr}).createIndex(nr,ir,{unique:!0}),e.createObjectStore(Jt,{keyPath:"targetId"}).createIndex(Zt,er,{unique:!0}),e.createObjectStore(ar)}let Xo="IndexedDbPersistence",Yo="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";class Jo{constructor(e,t,r,n,i,s,a,o,u,l,h=18){if(this.allowTabSynchronization=e,this.persistenceKey=t,this.clientId=r,this.Fi=i,this.window=s,this.document=a,this.Mi=u,this.xi=l,this.Oi=h,this.ui=null,this.ci=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Ni=null,this.inForeground=!1,this.Bi=null,this.Li=null,this.ki=Number.NEGATIVE_INFINITY,this.qi=e=>Promise.resolve(),!Jo.C())throw new I(b.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new To(this,n),this.Qi=t+"main",this.serializer=new la(o),this.$i=new ct(this.Qi,this.Oi,new Ho(this.serializer)),this.li=new xa,this.hi=new fo(this.referenceDelegate,this.serializer),this.remoteDocumentCache=Co(this.serializer),this.Ti=new ba,this.window&&this.window.localStorage?this.Ui=this.window.localStorage:(this.Ui=null,!1===l&&d(Xo,"LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}start(){return this.Ki().then(()=>{if(this.isPrimary||this.allowTabSynchronization)return this.Wi(),this.Gi(),this.zi(),this.runTransaction("getHighestListenSequenceNumber","readonly",e=>this.hi.getHighestSequenceNumber(e));throw new I(b.FAILED_PRECONDITION,Yo)}).then(e=>{this.ui=new Tt(e,this.Mi)}).then(()=>{this.ci=!0}).catch(e=>(this.$i&&this.$i.close(),Promise.reject(e)))}ji(t){return this.qi=async e=>{if(this.started)return t(e)},t(this.isPrimary)}setDatabaseDeletedListener(e){this.$i.setDatabaseDeletedListener(e)}setNetworkEnabled(e){this.networkEnabled!==e&&(this.networkEnabled=e,this.Fi.enqueueAndForget(async()=>{this.started&&await this.Ki()}))}Ki(){return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",t=>eu(t).put({clientId:this.clientId,updateTimeMs:Date.now(),networkEnabled:this.networkEnabled,inForeground:this.inForeground}).next(()=>{if(this.isPrimary)return this.Ji(t).next(e=>{e||(this.isPrimary=!1,this.Fi.enqueueRetryable(()=>this.qi(!1)))})}).next(()=>this.Hi(t)).next(e=>this.isPrimary&&!e?this.Yi(t).next(()=>!1):!!e&&this.Zi(t).next(()=>!0))).catch(e=>{if(mt(e))return p(Xo,"Failed to extend owner lease: ",e),this.isPrimary;if(this.allowTabSynchronization)return p(Xo,"Releasing owner lease after error during lease refresh",e),!1;throw e}).then(e=>{this.isPrimary!==e&&this.Fi.enqueueRetryable(()=>this.qi(e)),this.isPrimary=e})}Ji(e){return Zo(e).get(Lt).next(e=>C.resolve(this.Xi(e)))}es(e){return eu(e).delete(this.clientId)}async ts(){if(this.isPrimary&&!this.ns(this.ki,18e5)){this.ki=Date.now();var e=await this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",e=>{let n=r(e,lr);return n.j().next(e=>{let t=this.rs(e,18e5),r=e.filter(e=>-1===t.indexOf(e));return C.forEach(r,e=>n.delete(e.clientId)).next(()=>r)})}).catch(()=>[]);if(this.Ui)for(var t of e)this.Ui.removeItem(this.ss(t.clientId))}}zi(){this.Li=this.Fi.enqueueAfterDelay("client_metadata_refresh",4e3,()=>this.Ki().then(()=>this.ts()).then(()=>this.zi()))}Xi(e){return!!e&&e.ownerId===this.clientId}Hi(t){return this.xi?C.resolve(!0):Zo(t).get(Lt).next(e=>{if(null!==e&&this.ns(e.leaseTimestampMs,5e3)&&!this._s(e.ownerId)){if(this.Xi(e)&&this.networkEnabled)return!0;if(!this.Xi(e)){if(e.allowTabSynchronization)return!1;throw new I(b.FAILED_PRECONDITION,Yo)}}return!(!this.networkEnabled||!this.inForeground)||eu(t).j().next(e=>void 0===this.rs(e,5e3).find(e=>{if(this.clientId!==e.clientId){var t=!this.networkEnabled&&e.networkEnabled,r=!this.inForeground&&e.inForeground,n=this.networkEnabled===e.networkEnabled;if(t||r&&n)return!0}return!1}))}).next(e=>(this.isPrimary!==e&&p(Xo,`Client ${e?"is":"is not"} eligible for a primary lease.`),e))}async shutdown(){this.ci=!1,this.us(),this.Li&&(this.Li.cancel(),this.Li=null),this.cs(),this.ls(),await this.$i.runTransaction("shutdown","readwrite",[Ot,lr],e=>{let t=new $r(e,Tt.ue);return this.Yi(t).next(()=>this.es(t))}),this.$i.close(),this.hs()}rs(e,t){return e.filter(e=>this.ns(e.updateTimeMs,t)&&!this._s(e.clientId))}Ps(){return this.runTransaction("getActiveClients","readonly",e=>eu(e).j().next(e=>this.rs(e,18e5).map(e=>e.clientId)))}get started(){return this.ci}getGlobalsCache(){return this.li}getMutationQueue(e,t){return ao.yt(e,this.serializer,t,this.referenceDelegate)}getTargetCache(){return this.hi}getRemoteDocumentCache(){return this.remoteDocumentCache}getIndexManager(e){return new Xa(e,this.serializer.gt.databaseId)}getDocumentOverlayCache(e){return Ea.yt(this.serializer,e)}getBundleCache(){return this.Ti}runTransaction(t,r,n){p(Xo,"Starting transaction:",t);var e,i="readonly"===r?"readonly":"readwrite",s=18===(e=this.Oi)?Gr:17===e?Kr:16===e?zr:15===e?jr:14===e?qr:13===e?Br:12===e?Ur:11===e?Pr:void E(60245);let a;return this.$i.runTransaction(t,i,s,e=>(a=new $r(e,this.ui?this.ui.next():Tt.ue),"readwrite-primary"===r?this.Ji(a).next(e=>!!e||this.Hi(a)).next(e=>{if(e)return n(a);throw d(`Failed to obtain primary lease for action '${t}'.`),this.isPrimary=!1,this.Fi.enqueueRetryable(()=>this.qi(!1)),new I(b.FAILED_PRECONDITION,at)}).next(e=>this.Zi(a).next(()=>e)):this.Ts(a).next(()=>n(a)))).then(e=>(a.raiseOnCommittedEvent(),e))}Ts(e){return Zo(e).get(Lt).next(e=>{if(null!==e&&this.ns(e.leaseTimestampMs,5e3)&&!this._s(e.ownerId)&&!this.Xi(e)&&!(this.xi||this.allowTabSynchronization&&e.allowTabSynchronization))throw new I(b.FAILED_PRECONDITION,Yo)})}Zi(e){var t={ownerId:this.clientId,allowTabSynchronization:this.allowTabSynchronization,leaseTimestampMs:Date.now()};return Zo(e).put(Lt,t)}static C(){return ct.C()}Yi(e){let t=Zo(e);return t.get(Lt).next(e=>this.Xi(e)?(p(Xo,"Releasing primary lease."),t.delete(Lt)):C.resolve())}ns(e,t){var r=Date.now();return!(e<r-t||r<e&&(d(`Detected an update time that is in the future: ${e} > `+r),1))}Wi(){null!==this.document&&"function"==typeof this.document.addEventListener&&(this.Bi=()=>{this.Fi.enqueueAndForget(()=>(this.inForeground="visible"===this.document.visibilityState,this.Ki()))},this.document.addEventListener("visibilitychange",this.Bi),this.inForeground="visible"===this.document.visibilityState)}cs(){this.Bi&&(this.document.removeEventListener("visibilitychange",this.Bi),this.Bi=null)}Gi(){var e;"function"==typeof(null==(e=this.window)?void 0:e.addEventListener)&&(this.Ni=()=>{this.us();var e=/(?:Version|Mobile)\/1[456]/;re()&&(navigator.appVersion.match(e)||navigator.userAgent.match(e))&&this.Fi.enterRestrictedMode(!0),this.Fi.enqueueAndForget(()=>this.shutdown())},this.window.addEventListener("pagehide",this.Ni))}ls(){this.Ni&&(this.window.removeEventListener("pagehide",this.Ni),this.Ni=null)}_s(e){var t;try{var r=null!==(null==(t=this.Ui)?void 0:t.getItem(this.ss(e)));return p(Xo,`Client '${e}' ${r?"is":"is not"} zombied in LocalStorage`),r}catch(e){return d(Xo,"Failed to get zombied client id.",e),!1}}us(){if(this.Ui)try{this.Ui.setItem(this.ss(this.clientId),String(Date.now()))}catch(e){d("Failed to set zombie client id.",e)}}hs(){if(this.Ui)try{this.Ui.removeItem(this.ss(this.clientId))}catch(e){}}ss(e){return`firestore_zombie_${this.persistenceKey}_`+e}}function Zo(e){return r(e,Ot)}function eu(e){return r(e,lr)}function tu(e,t){let r=e.projectId;return e.isDefaultDatabase||(r+="."+e.database),"firestore/"+t+"/"+r+"/"}class ru{constructor(e,t,r,n){this.targetId=e,this.fromCache=t,this.Is=r,this.ds=n}static Es(e,t){let r=F(),n=F();for(let e of t.docChanges)switch(e.type){case 0:r=r.add(e.doc.key);break;case 1:n=n.add(e.doc.key)}return new ru(e,t.fromCache,r,n)}}class nu{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class iu{constructor(){this.As=!1,this.Rs=!1,this.Vs=100,this.fs=re()?8:0<dt(ee())?6:4}initialize(e,t){this.gs=e,this.indexManager=t,this.As=!0}getDocumentsMatchingQuery(r,n,e,t){let i={result:null};return this.ps(r,n).next(e=>{i.result=e}).next(()=>{if(!i.result)return this.ys(r,n,t,e).next(e=>{i.result=e})}).next(()=>{if(!i.result){let t=new nu;return this.ws(r,n,t).next(e=>{if(i.result=e,this.Rs)return this.Ss(r,n,t,e.size)})}}).next(()=>i.result)}Ss(e,t,r,n){return r.documentReadCount<this.Vs?(_e()<=c.DEBUG&&p("QueryEngine","SDK will not create cache indexes for query:",xi(t),"since it only creates cache indexes for collection contains","more than or equal to",this.Vs,"documents"),C.resolve()):(_e()<=c.DEBUG&&p("QueryEngine","Query:",xi(t),"scans",r.documentReadCount,"local documents and returns",n,"documents as results."),r.documentReadCount>this.fs*n?(_e()<=c.DEBUG&&p("QueryEngine","The SDK decides to create cache indexes for query:",xi(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,bi(t))):C.resolve())}ps(i,s){if(vi(s))return C.resolve(null);let t=bi(s);return this.indexManager.getIndexType(i,t).next(e=>0===e?null:(null!==s.limit&&1===e&&(s=Ti(s,null,"F"),t=bi(s)),this.indexManager.getDocumentsMatchingTarget(i,t).next(e=>{let n=F(...e);return this.gs.getDocuments(i,n).next(r=>this.indexManager.getMinOffset(i,t).next(e=>{var t=this.bs(s,r);return this.Ds(s,t,n,e.readTime)?this.ps(i,Ti(s,null,"F")):this.vs(i,t,s,e)}))})))}ys(r,n,i,s){return vi(n)||s.isEqual(w.min())?C.resolve(null):this.gs.getDocuments(r,i).next(e=>{var t=this.bs(n,e);return this.Ds(n,t,i,s)?C.resolve(null):(_e()<=c.DEBUG&&p("QueryEngine","Re-using previous result from %s to execute query: %s",s.toString(),xi(n)),this.vs(r,t,n,rt(s,Xe)).next(e=>e))})}bs(r,e){let n=new D(Di(r));return e.forEach((e,t)=>{Ci(r,t)&&(n=n.add(t))}),n}Ds(e,t,r,n){var i;return null!==e.limit&&(r.size!==t.size||!!(i="F"===e.limitType?t.last():t.first())&&(i.hasPendingWrites||0<i.version.compareTo(n)))}ws(e,t,r){return _e()<=c.DEBUG&&p("QueryEngine","Using full collection scan to execute query:",xi(t)),this.gs.getDocumentsMatchingQuery(e,t,it.min(),r)}vs(e,r,t,n){return this.gs.getDocumentsMatchingQuery(e,t,n).next(t=>(r.forEach(e=>{t=t.insert(e.key,e)}),t))}}let su="LocalStore",au=3e8;class ou{constructor(e,t,r,n){this.persistence=e,this.Cs=t,this.serializer=n,this.Fs=new A(S),this.Ms=new Ni(e=>li(e),hi),this.xs=new Map,this.Os=e.getRemoteDocumentCache(),this.hi=e.getTargetCache(),this.Ti=e.getBundleCache(),this.Ns(r)}Ns(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new Fo(this.Os,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Os.setIndexManager(this.indexManager),this.Cs.initialize(this.localDocuments,this.indexManager)}collectGarbage(t){return this.persistence.runTransaction("Collect garbage","readwrite-primary",e=>t.collect(e,this.Fs))}}function uu(e,t,r,n){return new ou(e,t,r,n)}async function lu(e,t){let o=e;return o.persistence.runTransaction("Handle user change","readonly",s=>{let a;return o.mutationQueue.getAllMutationBatches(s).next(e=>(a=e,o.Ns(t),o.mutationQueue.getAllMutationBatches(s))).next(e=>{let t=[],r=[],n=F();for(let i of a){t.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}for(let i of e){r.push(i.batchId);for(let e of i.mutations)n=n.add(e.key)}return o.localDocuments.getDocuments(s,n).next(e=>({Bs:e,removedBatchIds:t,addedBatchIds:r}))})})}function hu(e,n){let i=e;return i.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let t=n.batch.keys(),r=i.Os.newChangeBuffer({trackRemovals:!0});return((e,t,n,i)=>{let s=n.batch,r=s.keys(),a=C.resolve();return r.forEach(r=>{a=a.next(()=>i.getEntry(t,r)).next(e=>{var t=n.docVersions.get(r);y(null!==t,48541),e.version.compareTo(t)<0&&(s.applyToRemoteDocument(e,n),e.isValidDocument())&&(e.setReadTime(n.commitVersion),i.addEntry(e))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,s))})(i,e,n,r).next(()=>r.apply(e)).next(()=>i.mutationQueue.performConsistencyCheck(e)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(e,t,n.batch.batchId)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,(e=>{let t=F();for(let r=0;r<e.mutationResults.length;++r)0<e.mutationResults[r].transformResults.length&&(t=t.add(e.batch.mutations[r].key));return t})(n))).next(()=>i.localDocuments.getDocuments(e,t))})}function cu(e){let t=e;return t.persistence.runTransaction("Get last remote snapshot version","readonly",e=>t.hi.getLastRemoteSnapshotVersion(e))}function du(e,l){let h=e,c=l.snapshotVersion,d=h.Fs;return h.persistence.runTransaction("Apply remote event","readwrite-primary",o=>{let e=h.Os.newChangeBuffer({trackRemovals:!0}),u=(d=h.Fs,[]),t=(l.targetChanges.forEach((t,r)=>{var n,i,s,a=d.get(r);if(a){u.push(h.hi.removeMatchingKeys(o,t.removedDocuments,r).next(()=>h.hi.addMatchingKeys(o,t.addedDocuments,r)));let e=a.withSequenceNumber(o.currentSequenceNumber);null!==l.targetMismatches.get(r)?e=e.withResumeToken(N.EMPTY_BYTE_STRING,w.min()).withLastLimboFreeSnapshotVersion(w.min()):0<t.resumeToken.approximateByteSize()&&(e=e.withResumeToken(t.resumeToken,c)),d=d.insert(r,e),n=a,i=e,s=t,(0===n.resumeToken.approximateByteSize()||i.snapshotVersion.toMicroseconds()-n.snapshotVersion.toMicroseconds()>=au||0<s.addedDocuments.size+s.modifiedDocuments.size+s.removedDocuments.size)&&u.push(h.hi.updateTargetData(o,e))}}),ki),r=F();if(l.documentUpdates.forEach(e=>{l.resolvedLimboDocuments.has(e)&&u.push(h.persistence.referenceDelegate.updateLimboDocument(o,e))}),u.push(fu(o,e,l.documentUpdates).next(e=>{t=e.Ls,r=e.ks})),!c.isEqual(w.min())){let e=h.hi.getLastRemoteSnapshotVersion(o).next(e=>h.hi.setTargetsMetadata(o,o.currentSequenceNumber,c));u.push(e)}return C.waitFor(u).next(()=>e.apply(o)).next(()=>h.localDocuments.getLocalViewOfDocuments(o,t,r)).next(()=>t)}).then(e=>(h.Fs=d,e))}function fu(e,s,t){let r=F(),a=F();return t.forEach(e=>r=r.add(e)),s.getEntries(e,r).next(n=>{let i=ki;return t.forEach((e,t)=>{var r=n.get(e);t.isFoundDocument()!==r.isFoundDocument()&&(a=a.add(e)),t.isNoDocument()&&t.version.isEqual(w.min())?(s.removeEntry(e,t.readTime),i=i.insert(e,t)):!r.isValidDocument()||0<t.version.compareTo(r.version)||0===t.version.compareTo(r.version)&&r.hasPendingWrites?(s.addEntry(t),i=i.insert(e,t)):p(su,"Ignoring outdated watch update for ",e,". Current version:",r.version," Watch version:",t.version)}),{Ls:i,ks:a}})}function gu(e,n){let i=e;return i.persistence.runTransaction("Allocate target","readwrite",t=>{let r;return i.hi.getTargetData(t,n).next(e=>e?(r=e,C.resolve(r)):i.hi.allocateTargetId(t).next(e=>(r=new ua(n,e,"TargetPurposeListen",t.currentSequenceNumber),i.hi.addTargetData(t,r).next(()=>r))))}).then(e=>{var t=i.Fs.get(e.targetId);return(null===t||0<e.snapshotVersion.compareTo(t.snapshotVersion))&&(i.Fs=i.Fs.insert(e.targetId,e),i.Ms.set(n,e.targetId)),e})}async function mu(e,t,r){let n=e,i=n.Fs.get(t),s=r?"readwrite":"readwrite-primary";try{r||await n.persistence.runTransaction("Release target",s,e=>n.persistence.referenceDelegate.removeTarget(e,i))}catch(e){if(!mt(e))throw e;p(su,`Failed to update sequence numbers for target ${t}: `+e)}n.Fs=n.Fs.remove(t),n.Ms.delete(i.target)}function pu(e,a,o){let u=e,l=w.min(),h=F();return u.persistence.runTransaction("Execute query","readwrite",t=>{return e=u,r=t,n=bi(a),(void 0!==(s=(i=e).Ms.get(n))?C.resolve(i.Fs.get(s)):i.hi.getTargetData(r,n)).next(e=>{if(e)return l=e.lastLimboFreeSnapshotVersion,u.hi.getMatchingKeysForTargetId(t,e.targetId).next(e=>{h=e})}).next(()=>u.Cs.getDocumentsMatchingQuery(t,a,o?l:w.min(),o?h:F())).next(e=>(wu(u,Ai(a),e),{documents:e,qs:h}));var e,r,n,i,s})}function yu(e,t){let r=e,n=r.hi,i=r.Fs.get(t);return i?Promise.resolve(i.target):r.persistence.runTransaction("Get target data","readonly",e=>n.Et(e,t).next(e=>e?e.target:null))}function vu(e,t){let r=e,n=r.xs.get(t)||w.min();return r.persistence.runTransaction("Get new document changes","readonly",e=>r.Os.getAllFromCollectionGroup(e,t,rt(n,Xe),Number.MAX_SAFE_INTEGER)).then(e=>(wu(r,t,e),e))}function wu(e,t,r){let n=e.xs.get(t)||w.min();r.forEach((e,t)=>{0<t.readTime.compareTo(n)&&(n=t.readTime)}),e.xs.set(t,n)}let _u="firestore_clients";function bu(e,t){return _u+`_${e}_`+t}let Iu="firestore_mutations";function Tu(e,t,r){let n=Iu+`_${e}_`+r;return t.isAuthenticated()&&(n+="_"+t.uid),n}let Eu="firestore_targets";function Su(e,t){return Eu+`_${e}_`+t}let xu="SharedClientState";class Cu{constructor(e,t,r,n){this.user=e,this.batchId=t,this.state=r,this.error=n}static Ks(e,t,r){var n=JSON.parse(r);let i,s="object"==typeof n&&-1!==["pending","acknowledged","rejected"].indexOf(n.state)&&(void 0===n.error||"object"==typeof n.error);return s&&n.error&&(s="string"==typeof n.error.message&&"string"==typeof n.error.code)&&(i=new I(n.error.code,n.error.message)),s?new Cu(e,t,n.state,i):(d(xu,`Failed to parse mutation state for ID '${t}': `+r),null)}Ws(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class Au{constructor(e,t,r){this.targetId=e,this.state=t,this.error=r}static Ks(e,t){var r=JSON.parse(t);let n,i="object"==typeof r&&-1!==["not-current","current","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return i&&r.error&&(i="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(n=new I(r.error.code,r.error.message)),i?new Au(e,r.state,n):(d(xu,`Failed to parse target state for ID '${e}': `+t),null)}Ws(){var e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class Du{constructor(e,t){this.clientId=e,this.activeTargetIds=t}static Ks(e,t){var r=JSON.parse(t);let n="object"==typeof r&&r.activeTargetIds instanceof Array,i=Pi;for(let s=0;n&&s<r.activeTargetIds.length;++s)n=Ct(r.activeTargetIds[s]),i=i.add(r.activeTargetIds[s]);return n?new Du(e,i):(d(xu,`Failed to parse client data for instance '${e}': `+t),null)}}class Nu{constructor(e,t){this.clientId=e,this.onlineState=t}static Ks(e){var t=JSON.parse(e);return"object"==typeof t&&-1!==["Unknown","Online","Offline"].indexOf(t.onlineState)&&"string"==typeof t.clientId?new Nu(t.clientId,t.onlineState):(d(xu,"Failed to parse online state: "+e),null)}}class ku{constructor(){this.activeTargetIds=Pi}Gs(e){this.activeTargetIds=this.activeTargetIds.add(e)}zs(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Ws(){var e={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(e)}}class Ru{constructor(e,t,r,n,i){this.window=e,this.Fi=t,this.persistenceKey=r,this.js=n,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.Js=this.Hs.bind(this),this.Ys=new A(S),this.started=!1,this.Zs=[];var s=r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.Xs=bu(this.persistenceKey,this.js),this.eo="firestore_sequence_number_"+this.persistenceKey,this.Ys=this.Ys.insert(this.js,new ku),this.no=new RegExp(`^${_u}_${s}_([^_]*)$`),this.ro=new RegExp(`^${Iu}_${s}_(\\d+)(?:_(.*))?$`),this.io=new RegExp(`^${Eu}_${s}_(\\d+)$`),this.so="firestore_online_state_"+this.persistenceKey,this.oo="firestore_bundle_loaded_v2_"+this.persistenceKey,this.window.addEventListener("storage",this.Js)}static C(e){return!(!e||!e.localStorage)}async start(){let e=await this.syncEngine.Ps();for(let r of e)if(r!==this.js){let e=this.getItem(bu(this.persistenceKey,r));var t;e&&(t=Du.Ks(r,e))&&(this.Ys=this.Ys.insert(t.clientId,t))}this._o();let r=this.storage.getItem(this.so);if(r){let e=this.ao(r);e&&this.uo(e)}for(let e of this.Zs)this.Hs(e);this.Zs=[],this.window.addEventListener("pagehide",()=>this.shutdown()),this.started=!0}writeSequenceNumber(e){this.setItem(this.eo,JSON.stringify(e))}getAllActiveQueryTargets(){return this.co(this.Ys)}isActiveQueryTarget(r){let n=!1;return this.Ys.forEach((e,t)=>{t.activeTargetIds.has(r)&&(n=!0)}),n}addPendingMutation(e){this.lo(e,"pending")}updateMutationState(e,t,r){this.lo(e,t,r),this.ho(e)}addLocalQueryTarget(t,e=!0){let r="not-current";if(this.isActiveQueryTarget(t)){let e=this.storage.getItem(Su(this.persistenceKey,t));var n;e&&(n=Au.Ks(t,e))&&(r=n.state)}return e&&this.Po.Gs(t),this._o(),r}removeLocalQueryTarget(e){this.Po.zs(e),this._o()}isLocalQueryTarget(e){return this.Po.activeTargetIds.has(e)}clearQueryState(e){this.removeItem(Su(this.persistenceKey,e))}updateQueryState(e,t,r){this.To(e,t,r)}handleUserChange(e,t,r){t.forEach(e=>{this.ho(e)}),this.currentUser=e,r.forEach(e=>{this.addPendingMutation(e)})}setOnlineState(e){this.Io(e)}notifyBundleLoaded(e){this.Eo(e)}shutdown(){this.started&&(this.window.removeEventListener("storage",this.Js),this.removeItem(this.Xs),this.started=!1)}getItem(e){var t=this.storage.getItem(e);return p(xu,"READ",e,t),t}setItem(e,t){p(xu,"SET",e,t),this.storage.setItem(e,t)}removeItem(e){p(xu,"REMOVE",e),this.storage.removeItem(e)}Hs(e){let t=e;t.storageArea===this.storage&&(p(xu,"EVENT",t.key,t.newValue),t.key===this.Xs?d("Received WebStorage notification for local change. Another client might have garbage-collected our state"):this.Fi.enqueueRetryable(async()=>{if(this.started){if(null!==t.key){if(this.no.test(t.key))return null==t.newValue?(e=this.Ao(t.key),this.Ro(e,null)):(e=this.Vo(t.key,t.newValue))?this.Ro(e.clientId,e):void 0;if(this.ro.test(t.key)){if(null!==t.newValue){var e=this.mo(t.key,t.newValue);if(e)return this.fo(e)}}else if(this.io.test(t.key)){if(null!==t.newValue){var e=this.po(t.key,t.newValue);if(e)return this.yo(e)}}else if(t.key===this.so){if(null!==t.newValue){var e=this.ao(t.newValue);if(e)return this.uo(e)}}else t.key===this.eo?(e=(e=>{let t=Tt.ue;if(null!=e)try{var r=JSON.parse(e);y("number"==typeof r,30636,{wo:e}),t=r}catch(e){d(xu,"Failed to read sequence number from WebStorage",e)}return t})(t.newValue))!==Tt.ue&&this.sequenceNumberHandler(e):t.key===this.oo&&(e=this.So(t.newValue),await Promise.all(e.map(e=>this.syncEngine.bo(e))))}}else this.Zs.push(t)}))}get Po(){return this.Ys.get(this.js)}_o(){this.setItem(this.Xs,this.Po.Ws())}lo(e,t,r){var n=new Cu(this.currentUser,e,t,r),i=Tu(this.persistenceKey,this.currentUser,e);this.setItem(i,n.Ws())}ho(e){var t=Tu(this.persistenceKey,this.currentUser,e);this.removeItem(t)}Io(e){var t={clientId:this.js,onlineState:e};this.storage.setItem(this.so,JSON.stringify(t))}To(e,t,r){var n=Su(this.persistenceKey,e),i=new Au(e,t,r);this.setItem(n,i.Ws())}Eo(e){var t=JSON.stringify(Array.from(e));this.setItem(this.oo,t)}Ao(e){var t=this.no.exec(e);return t?t[1]:null}Vo(e,t){var r=this.Ao(e);return Du.Ks(r,t)}mo(e,t){var r=this.ro.exec(e),n=Number(r[1]),r=void 0!==r[2]?r[2]:null;return Cu.Ks(new l(r),n,t)}po(e,t){var r=this.io.exec(e),r=Number(r[1]);return Au.Ks(r,t)}ao(e){return Nu.Ks(e)}So(e){return JSON.parse(e)}async fo(e){if(e.user.uid===this.currentUser.uid)return this.syncEngine.Do(e.batchId,e.state,e.error);p(xu,"Ignoring mutation for non-active user "+e.user.uid)}yo(e){return this.syncEngine.vo(e.targetId,e.state,e.error)}Ro(e,t){let r=t?this.Ys.insert(e,t):this.Ys.remove(e),n=this.co(this.Ys),i=this.co(r),s=[],a=[];return i.forEach(e=>{n.has(e)||s.push(e)}),n.forEach(e=>{i.has(e)||a.push(e)}),this.syncEngine.Co(s,a).then(()=>{this.Ys=r})}uo(e){this.Ys.get(e.clientId)&&this.onlineStateHandler(e.onlineState)}co(e){let r=Pi;return e.forEach((e,t)=>{r=r.unionWith(t.activeTargetIds)}),r}}class Ou{constructor(){this.Fo=new ku,this.Mo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.Fo.Gs(e),this.Mo[e]||"not-current"}updateQueryState(e,t,r){this.Mo[e]=t}removeLocalQueryTarget(e){this.Fo.zs(e)}isLocalQueryTarget(e){return this.Fo.activeTargetIds.has(e)}clearQueryState(e){delete this.Mo[e]}getAllActiveQueryTargets(){return this.Fo.activeTargetIds}isActiveQueryTarget(e){return this.Fo.activeTargetIds.has(e)}start(){return this.Fo=new ku,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class Lu{xo(e){}shutdown(){}}let Fu="ConnectivityMonitor";class Vu{constructor(){this.Oo=()=>this.No(),this.Bo=()=>this.Lo(),this.ko=[],this.qo()}xo(e){this.ko.push(e)}shutdown(){window.removeEventListener("online",this.Oo),window.removeEventListener("offline",this.Bo)}qo(){window.addEventListener("online",this.Oo),window.addEventListener("offline",this.Bo)}No(){p(Fu,"Network connectivity changed: AVAILABLE");for(var e of this.ko)e(0)}Lo(){p(Fu,"Network connectivity changed: UNAVAILABLE");for(var e of this.ko)e(1)}static C(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let Mu=null;function Pu(){return null===Mu?Mu=268435456+Math.round(2147483648*Math.random()):Mu++,"0x"+Mu.toString(16)}let Uu="RestConnection",Bu={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class qu{constructor(e){this.Ho=e.Ho,this.Yo=e.Yo}Zo(e){this.Xo=e}e_(e){this.t_=e}n_(e){this.r_=e}onMessage(e){this.i_=e}close(){this.Yo()}send(e){this.Ho(e)}s_(){this.Xo()}o_(){this.t_()}__(e){this.r_(e)}a_(e){this.i_(e)}}let ju="WebChannelConnection";class zu extends class{get Qo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;var t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),n=encodeURIComponent(this.databaseId.database);this.$o=t+"://"+e.host,this.Uo=`projects/${r}/databases/`+n,this.Ko=this.databaseId.database===gn?"project_id="+r:`project_id=${r}&database_id=`+n}Wo(t,e,r,n,i){let s=Pu(),a=this.Go(t,e.toUriEncodedString());p(Uu,`Sending RPC '${t}' ${s}:`,a,r);var o={"google-cloud-resource-prefix":this.Uo,"x-goog-request-params":this.Ko},u=(this.zo(o,n,i),new URL(a)).host,u=X(u);return this.jo(t,a,o,r,u).then(e=>(p(Uu,`Received RPC '${t}' ${s}: `,e),e),e=>{throw be(Uu,`RPC '${t}' ${s} failed with error: `,e,"url: ",a,"request:",r),e})}Jo(e,t,r,n,i,s){return this.Wo(e,t,r,n,i)}zo(r,e,t){r["X-Goog-Api-Client"]="gl-js/ fire/"+ve,r["Content-Type"]="text/plain",this.databaseInfo.appId&&(r["X-Firebase-GMPID"]=this.databaseInfo.appId),e&&e.headers.forEach((e,t)=>r[t]=e),t&&t.headers.forEach((e,t)=>r[t]=e)}Go(e,t){var r=Bu[e];return this.$o+`/v1/${t}:`+r}terminate(){}}{constructor(e){super(e),this.u_=[],this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}jo(u,t,r,n,e){let l=Pu();return new Promise((s,a)=>{let o=new pr;o.setWithCredentials(!0),o.listenOnce(vr.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case wr.NO_ERROR:var e=o.getResponseJson();p(ju,`XHR for RPC '${u}' ${l} received:`,JSON.stringify(e)),s(e);break;case wr.TIMEOUT:p(ju,`RPC '${u}' ${l} timed out`),a(new I(b.DEADLINE_EXCEEDED,"Request time out"));break;case wr.HTTP_ERROR:var t=o.getStatus();if(p(ju,`RPC '${u}' ${l} failed with status:`,t,"response text:",o.getResponseText()),0<t){let e=o.getResponseJson();var r=null==(e=Array.isArray(e)?e[0]:e)?void 0:e.error;if(r&&r.status&&r.message){n=r.status,i=n.toLowerCase().replace(/_/g,"-");let e=0<=Object.values(b).indexOf(i)?i:b.UNKNOWN;a(new I(e,r.message))}else a(new I(b.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new I(b.UNAVAILABLE,"Connection failed."));break;default:E(9055,{c_:u,streamId:l,l_:o.getLastErrorCode(),h_:o.getLastError()})}}finally{p(ju,`RPC '${u}' ${l} completed.`)}var n,i});var e=JSON.stringify(n);p(ju,`RPC '${u}' ${l} sending request:`,n),o.send(t,"POST",e,r,15)})}P_(i,e,t){let s=Pu(),r=[this.$o,"/","google.firestore.v1.Firestore","/",i,"/channel"],n=Tr(),a=Ir(),o={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/`+this.databaseId.database},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(o.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(o.useFetchStreams=!0),this.zo(o.initMessageHeaders,e,t),o.encodeInitMessageHeaders=!0;var l=r.join("");p(ju,`Creating RPC '${i}' stream ${s}: `+l,o);let h=n.createWebChannel(l,o),c=(this.T_(h),!1),d=!1,f=new qu({Ho:e=>{d?p(ju,`Not sending because RPC '${i}' stream ${s} is closed:`,e):(c||(p(ju,`Opening RPC '${i}' stream ${s} transport.`),h.open(),c=!0),p(ju,`RPC '${i}' stream ${s} sending:`,e),h.send(e))},Yo:()=>h.close()}),g=(e,t,r)=>{e.listen(t,e=>{try{r(e)}catch(e){setTimeout(()=>{throw e},0)}})};return g(h,yr.EventType.OPEN,()=>{d||(p(ju,`RPC '${i}' stream ${s} transport opened.`),f.s_())}),g(h,yr.EventType.CLOSE,()=>{d||(d=!0,p(ju,`RPC '${i}' stream ${s} transport closed`),f.__(),this.I_(h))}),g(h,yr.EventType.ERROR,e=>{d||(d=!0,be(ju,`RPC '${i}' stream ${s} transport errored. Name:`,e.name,"Message:",e.message),f.__(new I(b.UNAVAILABLE,"The operation could not be completed")))}),g(h,yr.EventType.MESSAGE,e=>{if(!d){var t=e.data[0],n=(y(!!t,16349),t),n=(null==n?void 0:n.error)||(null==(n=n[0])?void 0:n.error);if(n){p(ju,`RPC '${i}' stream ${s} received error:`,n);let e=n.status,t=(e=>{var t=m[e];if(void 0!==t)return ys(t)})(e),r=n.message;void 0===t&&(t=b.INTERNAL,r="Unknown error status: "+e+" with message "+n.message),d=!0,f.__(new I(t,r)),h.close()}else p(ju,`RPC '${i}' stream ${s} received:`,t),f.a_(t)}}),g(a,br.STAT_EVENT,e=>{e.stat===_r.PROXY?p(ju,`RPC '${i}' stream ${s} detected buffering proxy`):e.stat===_r.NOPROXY&&p(ju,`RPC '${i}' stream ${s} detected no buffering proxy`)}),setTimeout(()=>{f.o_()},0),f}terminate(){this.u_.forEach(e=>e.close()),this.u_=[]}T_(e){this.u_.push(e)}I_(t){this.u_=this.u_.filter(e=>e===t)}}function Ku(){return"undefined"!=typeof window?window:null}function Gu(){return"undefined"!=typeof document?document:null}function $u(e){return new Fs(e,!0)}class Qu{constructor(e,t,r=1e3,n=1.5,i=6e4){this.Fi=e,this.timerId=t,this.d_=r,this.E_=n,this.A_=i,this.R_=0,this.V_=null,this.m_=Date.now(),this.reset()}reset(){this.R_=0}f_(){this.R_=this.A_}g_(e){this.cancel();var t=Math.floor(this.R_+this.p_()),r=Math.max(0,Date.now()-this.m_),n=Math.max(0,t-r);0<n&&p("ExponentialBackoff",`Backing off for ${n} ms (base delay: ${this.R_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.V_=this.Fi.enqueueAfterDelay(this.timerId,n,()=>(this.m_=Date.now(),e())),this.R_*=this.E_,this.R_<this.d_&&(this.R_=this.d_),this.R_>this.A_&&(this.R_=this.A_)}y_(){null!==this.V_&&(this.V_.skipDelay(),this.V_=null)}cancel(){null!==this.V_&&(this.V_.cancel(),this.V_=null)}p_(){return(Math.random()-.5)*this.R_}}let Hu="PersistentStream";class Wu{constructor(e,t,r,n,i,s,a,o){this.Fi=e,this.w_=r,this.S_=n,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.b_=0,this.D_=null,this.v_=null,this.stream=null,this.C_=0,this.F_=new Qu(e,t)}M_(){return 1===this.state||5===this.state||this.x_()}x_(){return 2===this.state||3===this.state}start(){this.C_=0,4!==this.state?this.auth():this.O_()}async stop(){this.M_()&&await this.close(0)}N_(){this.state=0,this.F_.reset()}B_(){this.x_()&&null===this.D_&&(this.D_=this.Fi.enqueueAfterDelay(this.w_,6e4,()=>this.L_()))}k_(e){this.q_(),this.stream.send(e)}async L_(){if(this.x_())return this.close(0)}q_(){this.D_&&(this.D_.cancel(),this.D_=null)}Q_(){this.v_&&(this.v_.cancel(),this.v_=null)}async close(e,t){this.q_(),this.Q_(),this.F_.cancel(),this.b_++,4!==e?this.F_.reset():t&&t.code===b.RESOURCE_EXHAUSTED?(d(t.toString()),d("Using maximum backoff delay to prevent overloading the backend."),this.F_.f_()):t&&t.code===b.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.U_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.n_(t)}U_(){}auth(){this.state=1;let e=this.K_(this.b_),r=this.b_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,t])=>{this.b_===r&&this.W_(e,t)},t=>{e(()=>{var e=new I(b.UNKNOWN,"Fetching auth token failed: "+t.message);return this.G_(e)})})}W_(e,t){let r=this.K_(this.b_);this.stream=this.z_(e,t),this.stream.Zo(()=>{r(()=>this.listener.Zo())}),this.stream.e_(()=>{r(()=>(this.state=2,this.v_=this.Fi.enqueueAfterDelay(this.S_,1e4,()=>(this.x_()&&(this.state=3),Promise.resolve())),this.listener.e_()))}),this.stream.n_(e=>{r(()=>this.G_(e))}),this.stream.onMessage(e=>{r(()=>1==++this.C_?this.j_(e):this.onNext(e))})}O_(){this.state=5,this.F_.g_(async()=>{this.state=0,this.start()})}G_(e){return p(Hu,"close with error: "+e),this.stream=null,this.close(4,e)}K_(t){return e=>{this.Fi.enqueueAndForget(()=>this.b_===t?e():(p(Hu,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class Xu extends Wu{constructor(e,t,r,n,i,s){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}z_(e,t){return this.connection.P_("Listen",e,t)}j_(e){return this.onNext(e)}onNext(e){this.F_.reset();var t=((e,t)=>{let r;if("targetChange"in t){t.targetChange;var n="NO_CHANGE"===(u=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===u?1:"REMOVE"===u?2:"CURRENT"===u?3:"RESET"===u?4:E(39313,{state:u}),i=t.targetChange.targetIds||[],s=(u=t.targetChange.resumeToken,e.useProto3Json?(y(void 0===u||"string"==typeof u,58123),N.fromBase64String(u||"")):(y(void 0===u||u instanceof Buffer||u instanceof Uint8Array,16193),N.fromUint8Array(u||new Uint8Array))),a=t.targetChange.cause,a=a&&(a=void 0===(u=a).code?b.UNKNOWN:ys(u.code),new I(a,u.message||""));r=new Cs(n,i,s,a||null)}else if("documentChange"in t){t.documentChange;var n=t.documentChange,i=(n.document,n.document.name,n.document.updateTime,zs(e,n.document.name)),s=M(n.document.updateTime),a=n.document.createTime?M(n.document.createTime):w.min(),o=new qn({mapValue:{fields:n.document.fields}}),i=R.newFoundDocument(i,s,a,o),s=n.targetIds||[],o=n.removedTargetIds||[];r=new Ss(s,o,i.key,i)}else if("documentDelete"in t){t.documentDelete;n=t.documentDelete,s=(n.document,zs(e,n.document)),o=n.readTime?M(n.readTime):w.min(),i=R.newNoDocument(s,o),s=n.removedTargetIds||[];r=new Ss([],s,i.key,i)}else if("documentRemove"in t){t.documentRemove;o=t.documentRemove,n=(o.document,zs(e,o.document)),s=o.removedTargetIds||[];r=new Ss([],s,n,null)}else{if(!("filter"in t))return E(11601,{At:t});{t.filter;let e=t.filter;e.targetId;var{count:i=0,unchangedNames:o}=e,s=new ms(i,o),n=e.targetId;r=new xs(n,s)}}var u;return r})(this.serializer,e),r="targetChange"in(e=e)&&(!(r=e.targetChange).targetIds||!r.targetIds.length)&&r.readTime?M(r.readTime):w.min();return this.listener.J_(t,r)}H_(e){var t={},r=(t.database=$s(this.serializer),t.addTarget=((t,r)=>{var n;let e=r.target;if((n=ci(e)?{documents:Zs(t,e)}:{query:ea(t,e).Vt}).targetId=r.targetId,0<r.resumeToken.approximateByteSize()){n.resumeToken=Ps(t,r.resumeToken);let e=Vs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}else if(0<r.snapshotVersion.compareTo(w.min())){n.readTime=Ms(t,r.snapshotVersion.toTimestamp());let e=Vs(t,r.expectedCount);null!==e&&(n.expectedCount=e)}return n})(this.serializer,e),ra(this.serializer,e));r&&(t.labels=r),this.k_(t)}Y_(e){var t={};t.database=$s(this.serializer),t.removeTarget=e,this.k_(t)}}class Yu extends Wu{constructor(e,t,r,n,i,s){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,r,n,s),this.serializer=i}get Z_(){return 0<this.C_}start(){this.lastStreamToken=void 0,super.start()}U_(){this.Z_&&this.X_([])}z_(e,t){return this.connection.P_("Write",e,t)}j_(e){return y(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,y(!e.writeResults||0===e.writeResults.length,55816),this.listener.ea()}onNext(e){y(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.F_.reset();var t=Js(e.writeResults,e.commitTime),r=M(e.commitTime);return this.listener.ta(r,t)}na(){var e={};e.database=$s(this.serializer),this.k_(e)}X_(e){var t={streamToken:this.lastStreamToken,writes:e.map(e=>Xs(this.serializer,e))};this.k_(t)}}class Ju extends class{}{constructor(e,t,r,n){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=n,this.ra=!1}ia(){if(this.ra)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.")}Wo(r,n,i,s){return this.ia(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.Wo(r,Bs(n,i),s,e,t)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}Jo(r,n,i,s,a){return this.ia(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([e,t])=>this.connection.Jo(r,Bs(n,i),s,e,t,a)).catch(e=>{throw"FirebaseError"===e.name?(e.code===b.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new I(b.UNKNOWN,e.toString())})}terminate(){this.ra=!0,this.connection.terminate()}}class Zu{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.sa=0,this.oa=null,this._a=!0}aa(){0===this.sa&&(this.ua("Unknown"),this.oa=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.oa=null,this.ca("Backend didn't respond within 10 seconds."),this.ua("Offline"),Promise.resolve())))}la(e){"Online"===this.state?this.ua("Unknown"):(this.sa++,1<=this.sa&&(this.ha(),this.ca("Connection failed 1 times. Most recent error: "+e.toString()),this.ua("Offline")))}set(e){this.ha(),this.sa=0,"Online"===e&&(this._a=!1),this.ua(e)}ua(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ca(e){var t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this._a?(d(t),this._a=!1):p("OnlineStateTracker",t)}ha(){null!==this.oa&&(this.oa.cancel(),this.oa=null)}}let el="RemoteStore";class tl{constructor(e,t,r,n,i){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.Pa=[],this.Ta=new Map,this.Ia=new Set,this.da=[],this.Ea=i,this.Ea.xo(e=>{r.enqueueAndForget(async()=>{var e;hl(this)&&(p(el,"Restarting streams for network reachability change."),(e=this).Ia.add(4),await nl(e),e.Aa.set("Unknown"),e.Ia.delete(4),await rl(e))})}),this.Aa=new Zu(r,n)}}async function rl(e){if(hl(e))for(var t of e.da)await t(!0)}async function nl(e){for(var t of e.da)await t(!1)}function il(e,t){var r=e;r.Ta.has(t.targetId)||(r.Ta.set(t.targetId,t),ll(r)?ul(r):wl(r).x_()&&al(r,t))}function sl(e,t){var r=e,n=wl(r);r.Ta.delete(t),n.x_()&&ol(r,t),0===r.Ta.size&&(n.x_()?n.B_():hl(r)&&r.Aa.set("Unknown"))}function al(e,t){var r;e.Ra.$e(t.targetId),(0<t.resumeToken.approximateByteSize()||0<t.snapshotVersion.compareTo(w.min()))&&(r=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size,t=t.withExpectedCount(r)),wl(e).H_(t)}function ol(e,t){e.Ra.$e(t),wl(e).Y_(t)}function ul(t){t.Ra=new Ds({getRemoteKeysForTarget:e=>t.remoteSyncer.getRemoteKeysForTarget(e),Et:e=>t.Ta.get(e)||null,lt:()=>t.datastore.serializer.databaseId}),wl(t).start(),t.Aa.aa()}function ll(e){return hl(e)&&!wl(e).M_()&&0<e.Ta.size}function hl(e){return 0===e.Ia.size}function cl(e){e.Ra=void 0}async function dl(e,t,r){if(!mt(t))throw t;e.Ia.add(1),await nl(e),e.Aa.set("Offline"),r=r||(()=>cu(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{p(el,"Retrying IndexedDB access"),await r(),e.Ia.delete(1),await rl(e)})}function fl(t,r){return r().catch(e=>dl(t,e,r))}async function gl(e){var t,r,n,i,s=e,a=_l(s);let o=0<s.Pa.length?s.Pa[s.Pa.length-1].batchId:Et;for(;hl(i=s)&&i.Pa.length<10;)try{let e=await((e,t)=>{let r=e;return r.persistence.runTransaction("Get next mutation batch","readonly",e=>(void 0===t&&(t=Et),r.mutationQueue.getNextMutationBatchAfterBatchId(e,t)))})(s.localStore,o);if(null===e){0===s.Pa.length&&a.B_();break}o=e.batchId,t=s,r=e,n=void 0,t.Pa.push(r),(n=_l(t)).x_()&&n.Z_&&n.X_(r.mutations)}catch(e){await dl(s,e)}ml(s)&&pl(s)}function ml(e){return hl(e)&&!_l(e).M_()&&0<e.Pa.length}function pl(e){_l(e).start()}async function yl(e,t){var r=e,n=(r.asyncQueue.verifyOperationInProgress(),p(el,"RemoteStore received new credentials"),hl(r));r.Ia.add(3),await nl(r),n&&r.Aa.set("Unknown"),await r.remoteSyncer.handleCredentialChange(t),r.Ia.delete(3),await rl(r)}async function vl(e,t){var r=e;t?(r.Ia.delete(2),await rl(r)):(r.Ia.add(2),await nl(r),r.Aa.set("Unknown"))}function wl(t){return t.Va||(t.Va=(e=t.datastore,r=t.asyncQueue,n={Zo:(async function(e){e.Aa.set("Online")}).bind(null,t),e_:(async function(r){r.Ta.forEach((e,t)=>{al(r,e)})}).bind(null,t),n_:(async function(e,t){cl(e),ll(e)?(e.Aa.la(t),ul(e)):e.Aa.set("Unknown")}).bind(null,t),J_:(async function(t,e,r){if(t.Aa.set("Online"),e instanceof Cs&&2===e.state&&e.cause)try{var n,i=t,s=e,a=s.cause;for(n of s.targetIds)i.Ta.has(n)&&(await i.remoteSyncer.rejectListen(n,a),i.Ta.delete(n),i.Ra.removeTarget(n))}catch(r){p(el,"Failed to remove targets %s: %s ",e.targetIds.join(","),r),await dl(t,r)}else if(e instanceof Ss?t.Ra.Ye(e):e instanceof xs?t.Ra.it(e):t.Ra.et(e),!r.isEqual(w.min()))try{let e=await cu(t.localStore);0<=r.compareTo(e)&&(u=r,(l=(o=t).Ra.Pt(u)).targetChanges.forEach((e,t)=>{var r;0<e.resumeToken.approximateByteSize()&&(r=o.Ta.get(t))&&o.Ta.set(t,r.withResumeToken(e.resumeToken,u))}),l.targetMismatches.forEach((e,t)=>{var r=o.Ta.get(e);r&&(o.Ta.set(e,r.withResumeToken(N.EMPTY_BYTE_STRING,r.snapshotVersion)),ol(o,e),r=new ua(r.target,e,t,r.sequenceNumber),al(o,r))}),await o.remoteSyncer.applyRemoteEvent(l))}catch(e){p(el,"Failed to raise snapshot:",e),await dl(t,e)}var o,u,l}).bind(null,t)},(i=e).ia(),new Xu(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.da.push(async e=>{e?(t.Va.N_(),ll(t)?ul(t):t.Aa.set("Unknown")):(await t.Va.stop(),cl(t))})),t.Va;var e,r,n,i}function _l(t){return t.ma||(t.ma=(e=t.datastore,r=t.asyncQueue,n={Zo:()=>Promise.resolve(),e_:(async function(e){_l(e).na()}).bind(null,t),n_:(async function(e,t){if(t&&_l(e).Z_){var r=e,n=t;if(ps(t=n.code)&&t!==b.ABORTED){let e=r.Pa.shift();_l(r).N_(),await fl(r,()=>r.remoteSyncer.rejectFailedWrite(e.batchId,n)),await gl(r)}await 0}ml(e)&&pl(e)}).bind(null,t),ea:(async function(e){var t,r=_l(e);for(t of e.Pa)r.X_(t.mutations)}).bind(null,t),ta:(async function(e,t,r){let n=e.Pa.shift(),i=fs.from(n,t,r);await fl(e,()=>e.remoteSyncer.applySuccessfulWrite(i)),await gl(e)}).bind(null,t)},(i=e).ia(),new Yu(r,i.connection,i.authCredentials,i.appCheckCredentials,i.serializer,n)),t.da.push(async e=>{e?(t.ma.N_(),await gl(t)):(await t.ma.stop(),0<t.Pa.length&&(p(el,`Stopping write stream with ${t.Pa.length} pending writes`),t.Pa=[]))})),t.ma;var e,r,n,i}class bl{constructor(e,t,r,n,i){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=n,this.removalCallback=i,this.deferred=new f,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,n,i){var s=Date.now()+r,s=new bl(e,t,s,n,i);return s.start(r),s}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new I(b.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function Il(e,t){if(d("AsyncQueue",t+": "+e),mt(e))return new I(b.UNAVAILABLE,t+": "+e);throw e}class Tl{static emptySet(e){return new Tl(e.comparator)}constructor(r){this.comparator=r?(e,t)=>r(e,t)||x.comparator(e.key,t.key):(e,t)=>x.comparator(e.key,t.key),this.keyedMap=Oi(),this.sortedSet=new A(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){var t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(r){this.sortedSet.inorderTraversal((e,t)=>(r(e),!1))}add(e){var t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){var t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof Tl))return!1;if(this.size!==e.size)return!1;for(var r=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();r.hasNext();){let e=r.getNext().key,t=n.getNext().key;if(!e.isEqual(t))return!1}return!0}toString(){let t=[];return this.forEach(e=>{t.push(e.toString())}),0===t.length?"DocumentSet ()":"DocumentSet (\n  "+t.join("  \n")+"\n)"}copy(e,t){var r=new Tl;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}}class El{constructor(){this.fa=new A(x.comparator)}track(e){var t=e.doc.key,r=this.fa.get(t);!r||0!==e.type&&3===r.type?this.fa=this.fa.insert(t,e):3===e.type&&1!==r.type?this.fa=this.fa.insert(t,{type:r.type,doc:e.doc}):2===e.type&&2===r.type?this.fa=this.fa.insert(t,{type:2,doc:e.doc}):2===e.type&&0===r.type?this.fa=this.fa.insert(t,{type:0,doc:e.doc}):1===e.type&&0===r.type?this.fa=this.fa.remove(t):1===e.type&&2===r.type?this.fa=this.fa.insert(t,{type:1,doc:r.doc}):0===e.type&&1===r.type?this.fa=this.fa.insert(t,{type:2,doc:e.doc}):E(63341,{At:e,ga:r})}pa(){let r=[];return this.fa.inorderTraversal((e,t)=>{r.push(t)}),r}}class Sl{constructor(e,t,r,n,i,s,a,o,u){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=n,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=u}static fromInitialDocuments(e,t,r,n,i){let s=[];return t.forEach(e=>{s.push({type:0,doc:e})}),new Sl(e,t,Tl.emptySet(t),s,r,n,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&Ei(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;var t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let n=0;n<t.length;n++)if(t[n].type!==r[n].type||!t[n].doc.isEqual(r[n].doc))return!1;return!0}}class xl{constructor(){this.ya=void 0,this.wa=[]}Sa(){return this.wa.some(e=>e.ba())}}class Cl{constructor(){this.queries=Al(),this.onlineState="Unknown",this.Da=new Set}terminate(){var e,r,t,n;e=this,r=new I(b.ABORTED,"Firestore shutting down"),n=(t=e).queries,t.queries=Al(),n.forEach((e,t)=>{for(let e of t.wa)e.onError(r)})}}function Al(){return new Ni(e=>Si(e),Ei)}async function Dl(t,r){let e=t,n=3;var i=r.query;let s=e.queries.get(i);s?!s.Sa()&&r.ba()&&(n=2):(s=new xl,n=r.ba()?0:1);try{switch(n){case 0:s.ya=await e.onListen(i,!0);break;case 1:s.ya=await e.onListen(i,!1);break;case 2:await e.onFirstRemoteStoreListen(i)}}catch(t){let e=Il(t,`Initialization of query '${xi(r.query)}' failed`);return void r.onError(e)}e.queries.set(i,s),s.wa.push(r),r.va(e.onlineState),s.ya&&r.Ca(s.ya)&&kl(e)}async function Nl(e,t){var r=e,n=t.query;let i=3;var s=r.queries.get(n);if(s){let e=s.wa.indexOf(t);0<=e&&(s.wa.splice(e,1),0===s.wa.length?i=t.ba()?0:1:!s.Sa()&&t.ba()&&(i=2))}switch(i){case 0:return r.queries.delete(n),r.onUnlisten(n,!0);case 1:return r.queries.delete(n),r.onUnlisten(n,!1);case 2:return r.onLastRemoteStoreUnlisten(n);default:return}}function kl(e){e.Da.forEach(e=>{e.next()})}(pe=pe||{}).Fa="default",pe.Cache="cache";class Rl{constructor(e,t,r){this.query=e,this.Ma=t,this.xa=!1,this.Oa=null,this.onlineState="Unknown",this.options=r||{}}Ca(t){if(!this.options.includeMetadataChanges){let e=[];for(var r of t.docChanges)3!==r.type&&e.push(r);t=new Sl(t.query,t.docs,t.oldDocs,e,t.mutatedKeys,t.fromCache,t.syncStateChanged,!0,t.hasCachedResults)}let e=!1;return this.xa?this.Na(t)&&(this.Ma.next(t),e=!0):this.Ba(t,this.onlineState)&&(this.La(t),e=!0),this.Oa=t,e}onError(e){this.Ma.error(e)}va(e){this.onlineState=e;let t=!1;return this.Oa&&!this.xa&&this.Ba(this.Oa,e)&&(this.La(this.Oa),t=!0),t}Ba(e,t){return!e.fromCache||!this.ba()||(!this.options.ka||!("Offline"!==t))&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Na(e){var t;return 0<e.docChanges.length||(t=this.Oa&&this.Oa.hasPendingWrites!==e.hasPendingWrites,!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges)}La(e){e=Sl.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.xa=!0,this.Ma.next(e)}ba(){return this.options.source!==pe.Cache}}class Ol{constructor(e,t){this.qa=e,this.byteLength=t}Qa(){return"metadata"in this.qa}}class Ll{constructor(e){this.serializer=e}Qs(e){return zs(this.serializer,e)}$s(e){return e.metadata.exists?Ws(this.serializer,e.document,!1):R.newNoDocument(this.Qs(e.metadata.name),this.Us(e.metadata.readTime))}Us(e){return M(e)}}class Fl{constructor(e,t){this.$a=e,this.serializer=t,this.Ua=[],this.Ka=[],this.collectionGroups=new Set,this.progress=Vl(e)}get queries(){return this.Ua}get documents(){return this.Ka}Wa(e){this.progress.bytesLoaded+=e.byteLength;let t=this.progress.documentsLoaded;var r;return e.qa.namedQuery?this.Ua.push(e.qa.namedQuery):e.qa.documentMetadata?(this.Ka.push({metadata:e.qa.documentMetadata}),e.qa.documentMetadata.exists||++t,r=T.fromString(e.qa.documentMetadata.name),this.collectionGroups.add(r.get(r.length-2))):e.qa.document&&(this.Ka[this.Ka.length-1].document=e.qa.document,++t),t!==this.progress.documentsLoaded?(this.progress.documentsLoaded=t,Object.assign({},this.progress)):null}Ga(e){let r=new Map,n=new Ll(this.serializer);for(var i of e)if(i.metadata.queries){let e=n.Qs(i.metadata.name);for(let t of i.metadata.queries){var s=(r.get(t)||F()).add(e);r.set(t,s)}}return r}async za(e){let t=await(async(r,n,e,t)=>{let i=r,s=F(),a=ki;for(let r of e){let e=n.Qs(r.metadata.name),t=(r.document&&(s=s.add(e)),n.$s(r));t.setReadTime(n.Us(r.metadata.readTime)),a=a.insert(e,t)}let o=i.Os.newChangeBuffer({trackRemovals:!0}),u=await gu(i,(r=t,bi(yi(T.fromString("__bundle__/docs/"+r)))));return i.persistence.runTransaction("Apply bundle documents","readwrite",t=>fu(t,o,a).next(e=>(o.apply(t),e)).next(e=>i.hi.removeMatchingKeysForTargetId(t,u.targetId).next(()=>i.hi.addMatchingKeys(t,s,u.targetId)).next(()=>i.localDocuments.getLocalViewOfDocuments(t,e.Ls,e.ks)).next(()=>e.Ls)))})(e,new Ll(this.serializer),this.Ka,this.$a.id),r=this.Ga(this.documents);for(let t of this.Ua)await(async(e,r,n=F())=>{let i=await gu(e,bi(ya(r.bundledQuery))),s=e;return s.persistence.runTransaction("Save named query","readwrite",e=>{var t=M(r.readTime);return 0<=i.snapshotVersion.compareTo(t)?s.Ti.saveNamedQuery(e,r):(t=i.withResumeToken(N.EMPTY_BYTE_STRING,t),s.Fs=s.Fs.insert(t.targetId,t),s.hi.updateTargetData(e,t).next(()=>s.hi.removeMatchingKeysForTargetId(e,i.targetId)).next(()=>s.hi.addMatchingKeys(e,n,i.targetId)).next(()=>s.Ti.saveNamedQuery(e,r)))})})(e,t,r.get(t.name));return this.progress.taskState="Success",{progress:this.progress,ja:this.collectionGroups,Ja:t}}}function Vl(e){return{taskState:"Running",documentsLoaded:0,bytesLoaded:0,totalDocuments:e.totalDocuments,totalBytes:e.totalBytes}}class Ml{constructor(e){this.key=e}}class Pl{constructor(e){this.key=e}}class Ul{constructor(e,t){this.query=e,this.Ha=t,this.Ya=null,this.hasCachedResults=!1,this.current=!1,this.Za=F(),this.mutatedKeys=F(),this.Xa=Di(e),this.eu=new Tl(this.Xa)}get tu(){return this.Ha}nu(e,t){let o=t?t.ru:new El,u=(t||this).eu,l=(t||this).mutatedKeys,h=u,c=!1,d="F"===this.query.limitType&&u.size===this.query.limit?u.last():null,f="L"===this.query.limitType&&u.size===this.query.limit?u.first():null;if(e.inorderTraversal((e,t)=>{var r=u.get(e),n=Ci(this.query,t)?t:null,i=!!r&&this.mutatedKeys.has(r.key),s=!!n&&(n.hasLocalMutations||this.mutatedKeys.has(n.key)&&n.hasCommittedMutations);let a=!1;r&&n?r.data.isEqual(n.data)?i!==s&&(o.track({type:3,doc:n}),a=!0):!this.iu(r,n)&&(o.track({type:2,doc:n}),a=!0,d&&0<this.Xa(n,d)||f&&this.Xa(n,f)<0)&&(c=!0):!r&&n?(o.track({type:0,doc:n}),a=!0):r&&!n&&(o.track({type:1,doc:r}),a=!0,d||f)&&(c=!0),a&&(l=n?(h=h.add(n),s?l.add(e):l.delete(e)):(h=h.delete(e),l.delete(e)))}),null!==this.query.limit)for(;h.size>this.query.limit;){let e="F"===this.query.limitType?h.last():h.first();h=h.delete(e.key),l=l.delete(e.key),o.track({type:1,doc:e})}return{eu:h,ru:o,Ds:c,mutatedKeys:l}}iu(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,n){var i=this.eu,s=(this.eu=e.eu,this.mutatedKeys=e.mutatedKeys,e.ru.pa()),a=(s.sort((e,t)=>{return r=e.type,n=t.type,(i=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return E(20277,{At:e})}})(r)-i(n)||this.Xa(e.doc,t.doc);var r,n,i}),this.su(r),n=null!=n&&n,t&&!n?this.ou():[]),o=0===this.Za.size&&this.current&&!n?1:0,u=o!==this.Ya;return this.Ya=o,0!==s.length||u?{snapshot:new Sl(this.query,e.eu,i,s,e.mutatedKeys,0==o,u,!1,!!r&&0<r.resumeToken.approximateByteSize()),_u:a}:{_u:a}}va(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({eu:this.eu,ru:new El,mutatedKeys:this.mutatedKeys,Ds:!1},!1)):{_u:[]}}au(e){return!this.Ha.has(e)&&!!this.eu.has(e)&&!this.eu.get(e).hasLocalMutations}su(e){e&&(e.addedDocuments.forEach(e=>this.Ha=this.Ha.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.Ha=this.Ha.delete(e)),this.current=e.current)}ou(){if(!this.current)return[];let t=this.Za,r=(this.Za=F(),this.eu.forEach(e=>{this.au(e.key)&&(this.Za=this.Za.add(e.key))}),[]);return t.forEach(e=>{this.Za.has(e)||r.push(new Pl(e))}),this.Za.forEach(e=>{t.has(e)||r.push(new Ml(e))}),r}uu(e){this.Ha=e.qs,this.Za=F();var t=this.nu(e.documents);return this.applyChanges(t,!0)}cu(){return Sl.fromInitialDocuments(this.query,this.eu,this.mutatedKeys,0===this.Ya,this.hasCachedResults)}}let Bl="SyncEngine";class ql{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}}class jl{constructor(e){this.key=e,this.lu=!1}}class zl{constructor(e,t,r,n,i,s){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=n,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.hu={},this.Pu=new Ni(e=>Si(e),Ei),this.Tu=new Map,this.Iu=new Set,this.du=new A(x.comparator),this.Eu=new Map,this.Au=new Uo,this.Ru={},this.Vu=new Map,this.mu=co.ur(),this.onlineState="Unknown",this.fu=void 0}get isPrimaryClient(){return!0===this.fu}}async function Kl(e,t,r,n){var i=await gu(e.localStore,bi(t)),s=i.targetId,a=e.sharedClientState.addLocalQueryTarget(s,r);let o;return n&&(o=await Gl(e,t,s,"current"===a,i.resumeToken)),e.isPrimaryClient&&r&&il(e.remoteStore,i),o}async function Gl(n,e,t,r,i){n.gu=(e,t,r)=>(async(e,t,r,n)=>{let i=t.view.nu(r);i.Ds&&(i=await pu(e.localStore,t.query,!1).then(({documents:e})=>t.view.nu(e,i)));var s=n&&n.targetChanges.get(t.targetId),a=n&&null!=n.targetMismatches.get(t.targetId),s=t.view.applyChanges(i,e.isPrimaryClient,s,a);return th(e,t.targetId,s._u),s.snapshot})(n,e,t,r);var s=await pu(n.localStore,e,!0),a=new Ul(e,s.qs),s=a.nu(s.documents),o=Es.createSynthesizedTargetChangeForCurrentChange(t,r&&"Offline"!==n.onlineState,i),s=a.applyChanges(s,n.isPrimaryClient,o),o=(th(n,t,s._u),new ql(e,t,a));return n.Pu.set(e,o),n.Tu.has(t)?n.Tu.get(t).push(e):n.Tu.set(t,[e]),s.snapshot}async function $l(t,e,r){var n=uh(t);try{let t=await((e,i)=>{let s=e,a=v.now(),o=i.reduce((e,t)=>e.add(t.key),F()),u,l;return s.persistence.runTransaction("Locally write mutations","readwrite",n=>{let t=ki,r=F();return s.Os.getEntries(n,o).next(e=>{(t=e).forEach((e,t)=>{t.isValidDocument()||(r=r.add(e))})}).next(()=>s.localDocuments.getOverlayedDocuments(n,t)).next(e=>{u=e;var t=[];for(let r of i){let e=((e,r)=>{let n=null;for(var i of e.fieldTransforms){let e=r.data.field(i.field),t=zi(i.transform,e||null);null!=t&&(n=null===n?qn.empty():n).set(i.field,t)}return n||null})(r,u.get(r.key).overlayedDocument);null!=e&&t.push(new as(r.key,e,function i(e){let s=[];return Hr(e.fields,(e,r)=>{var n=new h([e]);if(Ln(r)){let t=i(r.mapValue).fields;if(0===t.length)s.push(n);else for(let e of t)s.push(n.child(e))}else s.push(n)}),new en(s)}(e.value.mapValue),V.exists(!0)))}return s.mutationQueue.addMutationBatch(n,a,t,i)}).next(e=>{var t=(l=e).applyToLocalDocumentSet(u,r);return s.documentOverlayCache.saveOverlays(n,e.batchId,t)})}).then(()=>({batchId:l.batchId,changes:Li(u)}))})(n.localStore,e);n.sharedClientState.addPendingMutation(t.batchId);{var i=n;var s=t.batchId;var a=r;let e=i.Ru[i.currentUser.toKey()];e=(e=e||new A(S)).insert(s,a),i.Ru[i.currentUser.toKey()]=e}await nh(n,t.changes),await gl(n.remoteStore)}catch(t){let e=Il(t,"Failed to persist write");r.reject(e)}}async function Ql(e,t){let n=e;try{let e=await du(n.localStore,t);t.targetChanges.forEach((e,t)=>{var r=n.Eu.get(t);r&&(y(e.addedDocuments.size+e.modifiedDocuments.size+e.removedDocuments.size<=1,22616),0<e.addedDocuments.size?r.lu=!0:0<e.modifiedDocuments.size?y(r.lu,14607):0<e.removedDocuments.size&&(y(r.lu,42227),r.lu=!1))}),await nh(n,e,t)}catch(e){await ut(e)}}function Hl(e,i,t){var s=e;if(s.isPrimaryClient&&0===t||!s.isPrimaryClient&&1===t){let n=[];s.Pu.forEach((e,t)=>{var r=t.view.va(i);r.snapshot&&n.push(r.snapshot)});{t=s.eventManager;var a=i;var o=t;o.onlineState=a;let r=!1;o.queries.forEach((e,t)=>{for(let e of t.wa)e.va(a)&&(r=!0)}),r&&kl(o)}n.length&&s.hu.J_(n),s.onlineState=i,s.isPrimaryClient&&s.sharedClientState.setOnlineState(i)}}async function Wl(e,t,r){var n=e;try{let e=await((e,n)=>{let i=e;return i.persistence.runTransaction("Reject batch","readwrite-primary",t=>{let r;return i.mutationQueue.lookupMutationBatch(t,n).next(e=>(y(null!==e,37113),r=e.keys(),i.mutationQueue.removeMutationBatch(t,e))).next(()=>i.mutationQueue.performConsistencyCheck(t)).next(()=>i.documentOverlayCache.removeOverlaysForBatchId(t,r,n)).next(()=>i.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(t,r)).next(()=>i.localDocuments.getDocuments(t,r))})})(n.localStore,t);Jl(n,t,r),Yl(n,t),n.sharedClientState.updateMutationState(t,"rejected",r),await nh(n,e)}catch(r){await ut(r)}}async function Xl(t,r){let n=t;hl(n.remoteStore)||p(Bl,"The network is disabled. The task returned by 'awaitPendingWrites()' will not complete until the network is enabled.");try{let e=await(e=>{let t=e;return t.persistence.runTransaction("Get highest unacknowledged batch id","readonly",e=>t.mutationQueue.getHighestUnacknowledgedBatchId(e))})(n.localStore);var i;e===Et?r.resolve():((i=n.Vu.get(e)||[]).push(r),n.Vu.set(e,i))}catch(t){let e=Il(t,"Initialization of waitForPendingWrites() operation failed");r.reject(e)}}function Yl(e,t){(e.Vu.get(t)||[]).forEach(e=>{e.resolve()}),e.Vu.delete(t)}function Jl(e,t,r){var n=e;let i=n.Ru[n.currentUser.toKey()];if(i){let e=i.get(t);e&&(r?e.reject(r):e.resolve(),i=i.remove(t)),n.Ru[n.currentUser.toKey()]=i}}function Zl(t,e,r=null){t.sharedClientState.removeLocalQueryTarget(e);for(var n of t.Tu.get(e))t.Pu.delete(n),r&&t.hu.pu(n,r);t.Tu.delete(e),t.isPrimaryClient&&t.Au.zr(e).forEach(e=>{t.Au.containsKey(e)||eh(t,e)})}function eh(e,t){e.Iu.delete(t.path.canonicalString());var r=e.du.get(t);null!==r&&(sl(e.remoteStore,r),e.du=e.du.remove(t),e.Eu.delete(r),rh(e))}function th(e,t,r){for(var n of r)n instanceof Ml?(e.Au.addReference(n.key,t),i=e,s=n,o=a=void 0,a=s.key,o=a.path.canonicalString(),i.du.get(a)||i.Iu.has(o)||(p(Bl,"New document in limbo: "+a),i.Iu.add(o),rh(i))):n instanceof Pl?(p(Bl,"Document no longer in limbo: "+n.key),e.Au.removeReference(n.key,t),e.Au.containsKey(n.key)||eh(e,n.key)):E(19791,{yu:n});var i,s,a,o}function rh(e){for(;0<e.Iu.size&&e.du.size<e.maxConcurrentLimboResolutions;){var t=e.Iu.values().next().value,t=(e.Iu.delete(t),new x(T.fromString(t))),r=e.mu.next();e.Eu.set(r,new jl(t)),e.du=e.du.insert(t,r),il(e.remoteStore,new ua(bi(yi(t.path)),r,"TargetPurposeLimboResolution",Tt.ue))}}async function nh(e,t,i){let s=e,a=[],o=[],r=[];if(!s.Pu.isEmpty()){s.Pu.forEach((e,n)=>{r.push(s.gu(n,t,i).then(t=>{var r;if((t||i)&&s.isPrimaryClient){let e=t?!t.fromCache:null==(r=null==i?void 0:i.targetChanges.get(n.targetId))?void 0:r.current;s.sharedClientState.updateQueryState(n.targetId,e?"current":"not-current")}if(t){a.push(t);let e=ru.Es(n.targetId,t);o.push(e)}}))}),await Promise.all(r),s.hu.J_(a);{var n=s.localStore,u=o;let i=n;try{await i.persistence.runTransaction("notifyLocalViewChanges","readwrite",r=>C.forEach(u,t=>C.forEach(t.Is,e=>i.persistence.referenceDelegate.addReference(r,t.targetId,e)).next(()=>C.forEach(t.ds,e=>i.persistence.referenceDelegate.removeReference(r,t.targetId,e)))))}catch(n){if(!mt(n))throw n;p(su,"Failed to update sequence numbers: "+n)}for(let e of u){let n=e.targetId;if(!e.fromCache){let e=i.Fs.get(n),t=e.snapshotVersion,r=e.withLastLimboFreeSnapshotVersion(t);i.Fs=i.Fs.insert(n,r)}}}}}async function ih(e,t,r,n){var i=e,s=await((e,r)=>{let n=e,i=n.mutationQueue;return n.persistence.runTransaction("Lookup mutation documents","readonly",t=>i.Xn(t,r).next(e=>e?n.localDocuments.getDocuments(t,e):C.resolve(null)))})(i.localStore,t);null!==s?("pending"===r?await gl(i.remoteStore):"acknowledged"===r||"rejected"===r?(Jl(i,t,n||null),Yl(i,t),i.localStore.mutationQueue.rr(t)):E(6720,"Unknown batchState",{wu:r}),await nh(i,s)):p(Bl,"Cannot apply mutation batch with id: "+t)}async function sh(r,e){var n,i,s,a=r,o=[],u=[];for(let r of e){let t,e=a.Tu.get(r);if(e&&0!==e.length){t=await gu(a.localStore,bi(e[0]));for(let r of e){let e=a.Pu.get(r),t=(n=e,s=i=void 0,s=await pu((i=a).localStore,n.query,!0),s=n.view.uu(s),i.isPrimaryClient&&th(i,n.targetId,s._u),await s);t.snapshot&&u.push(t.snapshot)}}else{let e=await yu(a.localStore,r);t=await gu(a.localStore,e),await Gl(a,ah(e),r,!1,t.resumeToken)}o.push(t)}return a.hu.J_(u),o}function ah(e){return pi(e.path,e.collectionGroup,e.orderBy,e.filters,e.limit,"F",e.startAt,e.endAt)}function oh(e){var t=e;return t.remoteStore.remoteSyncer.applyRemoteEvent=Ql.bind(null,t),t.remoteStore.remoteSyncer.getRemoteKeysForTarget=(function(e,t){let n=e,r=n.Eu.get(t);if(r&&r.lu)return F().add(r.key);{let r=F(),e=n.Tu.get(t);if(e)for(let t of e){let e=n.Pu.get(t);r=r.unionWith(e.view.tu)}return r}}).bind(null,t),t.remoteStore.remoteSyncer.rejectListen=(async function(e,n,t){let i=e,r=(i.sharedClientState.updateQueryState(n,"rejected",t),i.Eu.get(n)),s=r&&r.key;if(s){let e=new A(x.comparator),t=(e=e.insert(s,R.newNoDocument(s,w.min())),F().add(s)),r=new Ts(w.min(),new Map,new A(S),e,t);await Ql(i,r),i.du=i.du.remove(s),i.Eu.delete(n),rh(i)}else await mu(i.localStore,n,!1).then(()=>Zl(i,n,t)).catch(ut)}).bind(null,t),t.hu.J_=(function(r,e){var n=r;let i=!1;for(let r of e){let e=r.query,t=n.queries.get(e);if(t){for(let e of t.wa)e.Ca(r)&&(i=!0);t.ya=r}}i&&kl(n)}).bind(null,t.eventManager),t.hu.pu=(function(e,t,r){var n=e,i=n.queries.get(t);if(i)for(let e of i.wa)e.onError(r);n.queries.delete(t)}).bind(null,t.eventManager),t}function uh(e){var t=e;return t.remoteStore.remoteSyncer.applySuccessfulWrite=(async function(e,t){var r=e,n=t.batch.batchId;try{let e=await hu(r.localStore,t);Jl(r,n,null),Yl(r,n),r.sharedClientState.updateMutationState(n,"acknowledged"),await nh(r,e)}catch(e){await ut(e)}}).bind(null,t),t.remoteStore.remoteSyncer.rejectFailedWrite=Wl.bind(null,t),t}function lh(e,t,r){let n=e;(async(e,r,n)=>{try{var i=await r.getMetadata();if(await((e,t)=>{let r=e,n=M(t.createTime);return r.persistence.runTransaction("hasNewerBundle","readonly",e=>r.Ti.getBundleMetadata(e,t.id)).then(e=>!!e&&0<=e.createTime.compareTo(n))})(e.localStore,i))return await r.close(),n._completeWith({taskState:"Success",documentsLoaded:i.totalDocuments,bytesLoaded:i.totalBytes,totalDocuments:i.totalDocuments,totalBytes:i.totalBytes}),Promise.resolve(new Set);n._updateProgress(Vl(i));var s=new Fl(i,r.serializer);let t=await r.Su();for(;t;){let e=await s.Wa(t);e&&n._updateProgress(e),t=await r.Su()}var a=await s.za(e.localStore);return await nh(e,a.Ja,void 0),await((e,t)=>{let r=e;return r.persistence.runTransaction("Save bundle","readwrite",e=>r.Ti.saveBundleMetadata(e,t))})(e.localStore,i),n._completeWith(a.progress),Promise.resolve(a.ja)}catch(e){return be(Bl,"Loading bundle failed with "+e),n._failWith(e),Promise.resolve(new Set)}})(n,t,r).then(e=>{n.sharedClientState.notifyBundleLoaded(e)})}class hh{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=$u(e.databaseInfo.databaseId),this.sharedClientState=this.bu(e),this.persistence=this.Du(e),await this.persistence.start(),this.localStore=this.vu(e),this.gcScheduler=this.Cu(e,this.localStore),this.indexBackfillerScheduler=this.Fu(e,this.localStore)}Cu(e,t){return null}Fu(e,t){return null}vu(e){return uu(this.persistence,new iu,e.initialUser,this.serializer)}Du(e){return new Ko($o.Vi,this.serializer)}bu(e){return new Ou}async terminate(){var e;null!=(e=this.gcScheduler)&&e.stop(),null!=(e=this.indexBackfillerScheduler)&&e.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}hh.provider={build:()=>new hh};class ch extends hh{constructor(e){super(),this.cacheSizeBytes=e}Cu(e,t){y(this.persistence.referenceDelegate instanceof Qo,46915);var r=this.persistence.referenceDelegate.garbageCollector;return new _o(r,e.asyncQueue,t)}Du(e){let t=void 0!==this.cacheSizeBytes?no.withCacheSize(this.cacheSizeBytes):no.DEFAULT;return new Ko(e=>Qo.Vi(e,t),this.serializer)}}class dh extends hh{constructor(e,t,r){super(),this.Mu=e,this.cacheSizeBytes=t,this.forceOwnership=r,this.kind="persistent",this.synchronizeTabs=!1}async initialize(e){await super.initialize(e),await this.Mu.initialize(this,e),await uh(this.Mu.syncEngine),await gl(this.Mu.remoteStore),await this.persistence.ji(()=>(this.gcScheduler&&!this.gcScheduler.started&&this.gcScheduler.start(),this.indexBackfillerScheduler&&!this.indexBackfillerScheduler.started&&this.indexBackfillerScheduler.start(),Promise.resolve()))}vu(e){return uu(this.persistence,new iu,e.initialUser,this.serializer)}Cu(e,t){var r=this.persistence.referenceDelegate.garbageCollector;return new _o(r,e.asyncQueue,t)}Fu(e,t){var r=new It(t,this.persistence);return new bt(e.asyncQueue,r)}Du(e){var t=tu(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),r=void 0!==this.cacheSizeBytes?no.withCacheSize(this.cacheSizeBytes):no.DEFAULT;return new Jo(this.synchronizeTabs,t,e.clientId,r,e.asyncQueue,Ku(),Gu(),this.serializer,this.sharedClientState,!!this.forceOwnership)}bu(e){return new Ou}}class fh extends dh{constructor(e,t){super(e,t,!1),this.Mu=e,this.cacheSizeBytes=t,this.synchronizeTabs=!0}async initialize(e){await super.initialize(e);var t=this.Mu.syncEngine;this.sharedClientState instanceof Ru&&(this.sharedClientState.syncEngine={Do:ih.bind(null,t),vo:(async function(e,r,n,t){var i=e;if(i.fu)p(Bl,"Ignoring unexpected query state notification.");else{var s=i.Tu.get(r);if(s&&0<s.length)switch(n){case"current":case"not-current":{let e=await vu(i.localStore,Ai(s[0])),t=Ts.createSynthesizedRemoteEventForCurrentChange(r,"current"===n,N.EMPTY_BYTE_STRING);await nh(i,e,t);break}case"rejected":await mu(i.localStore,r,!0),Zl(i,r,t);break;default:E(64155,n)}}}).bind(null,t),Co:(async function(e,t,n){let i=oh(e);if(i.fu){for(let r of t)if(i.Tu.has(r)&&i.sharedClientState.isActiveQueryTarget(r))p(Bl,"Adding an already active target "+r);else{let e=await yu(i.localStore,r),t=await gu(i.localStore,e);await Gl(i,ah(e),t.targetId,!1,t.resumeToken),il(i.remoteStore,t)}for(let r of n)i.Tu.has(r)&&await mu(i.localStore,r,!1).then(()=>{sl(i.remoteStore,r),Zl(i,r)}).catch(ut)}}).bind(null,t),Ps:(function(e){return e.localStore.persistence.Ps()}).bind(null,t),bo:(async function(e,t){let r=e;return vu(r.localStore,t).then(e=>nh(r,e))}).bind(null,t)},await this.sharedClientState.start()),await this.persistence.ji(async e=>{{var t=this.Mu.syncEngine,s=e;let i=t;if(oh(i),uh(i),!0===s&&!0!==i.fu){let e=i.sharedClientState.getAllActiveQueryTargets(),t=await sh(i,e.toArray());i.fu=!0,await vl(i.remoteStore,!0);for(let e of t)il(i.remoteStore,e)}else if(!1===s&&!1!==i.fu){let r=[],n=Promise.resolve();i.Tu.forEach((e,t)=>{i.sharedClientState.isLocalQueryTarget(t)?r.push(t):n=n.then(()=>(Zl(i,t),mu(i.localStore,t,!0))),sl(i.remoteStore,t)}),await n,await sh(i,r);{s=i;let r=s;r.Eu.forEach((e,t)=>{sl(r.remoteStore,t)}),r.Au.jr(),r.Eu=new Map,r.du=new A(x.comparator)}i.fu=!1,await vl(i.remoteStore,!1)}}await 0,this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start():e||this.gcScheduler.stop()),this.indexBackfillerScheduler&&(e&&!this.indexBackfillerScheduler.started?this.indexBackfillerScheduler.start():e||this.indexBackfillerScheduler.stop())})}bu(e){var t,r=Ku();if(Ru.C(r))return t=tu(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),new Ru(r,e.asyncQueue,t,e.clientId,e.initialUser);throw new I(b.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.")}}class gh{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>Hl(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=(async function(e,t){var r,n,i=e;if(!i.currentUser.isEqual(t)){p(Bl,"User change. New user:",t.toKey());let e=await lu(i.localStore,t);i.currentUser=t,n="'waitForPendingWrites' promise is rejected due to a user change.",(r=i).Vu.forEach(e=>{e.forEach(e=>{e.reject(new I(b.CANCELLED,n))})}),r.Vu.clear(),i.sharedClientState.handleUserChange(t,e.removedBatchIds,e.addedBatchIds),await nh(i,e.Bs)}}).bind(null,this.syncEngine),await vl(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new Cl}createDatastore(e){var t,r,n,i=$u(e.databaseInfo.databaseId),s=(t=e.databaseInfo,new zu(t));return t=e.authCredentials,e=e.appCheckCredentials,r=s,n=i,new Ju(t,e,r,n)}createRemoteStore(e){return t=this.localStore,r=this.datastore,e=e.asyncQueue,n=e=>Hl(this.syncEngine,e,0),i=new(Vu.C()?Vu:Lu),new tl(t,r,e,n,i);var t,r,n,i}createSyncEngine(e,t){return r=this.localStore,n=this.remoteStore,i=this.eventManager,s=this.sharedClientState,a=e.initialUser,e=e.maxConcurrentLimboResolutions,t=t,o=new zl(r,n,i,s,a,e),t&&(o.fu=!0),o;var r,n,i,s,a,o}async terminate(){var e,t;e=this.remoteStore,t=e,p(el,"RemoteStore shutting down."),t.Ia.add(5),await nl(t),t.Ea.shutdown(),await!t.Aa.set("Unknown"),null!=(t=this.datastore)&&t.terminate(),null!=(t=this.eventManager)&&t.terminate()}}function mh(t,r=10240){let n=0;return{async read(){var e;return n<t.byteLength?(e={value:t.slice(n,n+r),done:!1},n+=r,e):{done:!0}},async cancel(){},releaseLock(){},closed:Promise.resolve()}}gh.provider={build:()=>new gh};class ph{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.xu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.xu(this.observer.error,e):d("Uncaught Error in snapshot listener:",e.toString()))}Ou(){this.muted=!0}xu(e,t){setTimeout(()=>{this.muted||e(t)},0)}}class yh{constructor(e,t){this.Nu=e,this.serializer=t,this.metadata=new f,this.buffer=new Uint8Array,this.Bu=new TextDecoder("utf-8"),this.Lu().then(e=>{e&&e.Qa()?this.metadata.resolve(e.qa.metadata):this.metadata.reject(new Error(`The first element of the bundle is not a metadata, it is
             `+JSON.stringify(null==e?void 0:e.qa)))},e=>this.metadata.reject(e))}close(){return this.Nu.cancel()}async getMetadata(){return this.metadata.promise}async Su(){return await this.getMetadata(),this.Lu()}async Lu(){var e,t,r=await this.ku();return null===r?null:(t=this.Bu.decode(r),e=Number(t),isNaN(e)&&this.qu(`length string (${t}) is not valid number`),t=await this.Qu(e),new Ol(JSON.parse(t),r.length+e))}$u(){return this.buffer.findIndex(e=>e==="{".charCodeAt(0))}async ku(){for(;this.$u()<0&&!await this.Uu(););var e,t;return 0===this.buffer.length?null:((e=this.$u())<0&&this.qu("Reached the end of bundle when a length string is expected."),t=this.buffer.slice(0,e),this.buffer=this.buffer.slice(e),t)}async Qu(e){for(;this.buffer.length<e;)await this.Uu()&&this.qu("Reached the end of bundle when more is expected.");var t=this.Bu.decode(this.buffer.slice(0,e));return this.buffer=this.buffer.slice(e),t}qu(e){throw this.Nu.cancel(),new Error("Invalid bundle format: "+e)}async Uu(){var e,t=await this.Nu.read();return t.done||((e=new Uint8Array(this.buffer.length+t.value.length)).set(this.buffer),e.set(t.value,this.buffer.length),this.buffer=e),t.done}}class vh{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),0<this.mutations.length)throw this.lastTransactionError=new I(b.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;var t=await(async(e,t)=>{let u=e,r={documents:t.map(e=>js(u.serializer,e))},n=await u.Jo("BatchGetDocuments",u.serializer.databaseId,T.emptyPath(),r,t.length),l=new Map,i=(n.forEach(e=>{t=u.serializer;var t,r,n,i,s,a,o="found"in(e=e)?(r=t,y(!!(n=e).found,43571),n.found.name,n.found.updateTime,i=zs(r,n.found.name),s=M(n.found.updateTime),o=n.found.createTime?M(n.found.createTime):w.min(),a=new qn({mapValue:{fields:n.found.fields}}),R.newFoundDocument(i,s,o,a)):"missing"in e?(r=t,y(!!(n=e).missing,3894),y(!!n.readTime,22933),i=zs(r,n.missing),s=M(n.readTime),R.newNoDocument(i,s)):E(7234,{result:e});l.set(o.key.toString(),o)}),[]);return t.forEach(e=>{var t=l.get(e.toString());y(!!t,55234,{key:e}),i.push(t)}),i})(this.datastore,e);return t.forEach(e=>this.recordVersion(e)),t}set(e,t){this.write(t.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,t){try{this.write(t.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new hs(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;let t=this.readVersions;this.mutations.forEach(e=>{t.delete(e.key.toString())}),t.forEach((e,t)=>{var r=x.fromPath(t);this.mutations.push(new cs(r,this.precondition(r)))});{var r=this.datastore,n=this.mutations;let t=r,e={writes:n.map(e=>Xs(t.serializer,e))};await t.Wo("Commit",t.serializer.databaseId,T.emptyPath(),e)}await 0,this.committed=!0}recordVersion(e){let t;if(e.isFoundDocument())t=e.version;else{if(!e.isNoDocument())throw E(50498,{Wu:e.constructor.name});t=w.min()}var r=this.readVersions.get(e.key.toString());if(r){if(!t.isEqual(r))throw new I(b.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),t)}precondition(e){var t=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&t?t.isEqual(w.min())?V.exists(!1):V.updateTime(t):V.none()}preconditionForUpdate(e){var t=this.readVersions.get(e.toString());if(this.writtenDocs.has(e.toString())||!t)return V.exists(!0);if(t.isEqual(w.min()))throw new I(b.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return V.updateTime(t)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}class wh{constructor(e,t,r,n,i){this.asyncQueue=e,this.datastore=t,this.options=r,this.updateFunction=n,this.deferred=i,this.Gu=r.maxAttempts,this.F_=new Qu(this.asyncQueue,"transaction_retry")}zu(){--this.Gu,this.ju()}ju(){this.F_.g_(async()=>{let t=new vh(this.datastore),e=this.Ju(t);e&&e.then(e=>{this.asyncQueue.enqueueAndForget(()=>t.commit().then(()=>{this.deferred.resolve(e)}).catch(e=>{this.Hu(e)}))}).catch(e=>{this.Hu(e)})})}Ju(e){try{var t=this.updateFunction(e);return!St(t)&&t.catch&&t.then?t:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(e){return this.deferred.reject(e),null}}Hu(e){0<this.Gu&&this.Yu(e)?(--this.Gu,this.asyncQueue.enqueueAndForget(()=>(this.ju(),Promise.resolve()))):this.deferred.reject(e)}Yu(e){var t;return"FirebaseError"===e.name&&("aborted"===(t=e.code)||"failed-precondition"===t||"already-exists"===t||!ps(t))}}let _h="FirestoreClient";class bh{constructor(e,t,r,n,i){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=n,this.user=l.UNAUTHENTICATED,this.clientId=Oe.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=i,this.authCredentials.start(r,async e=>{p(_h,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(r,e=>(p(_h,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let r=new f;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),r.resolve()}catch(e){var t=Il(e,"Failed to shutdown persistence");r.reject(t)}}),r.promise}}async function Ih(e,t){e.asyncQueue.verifyOperationInProgress(),p(_h,"Initializing OfflineComponentProvider");var r=e.configuration;await t.initialize(r);let n=r.initialUser;e.setCredentialChangeListener(async e=>{n.isEqual(e)||(await lu(t.localStore,e),n=e)}),t.persistence.setDatabaseDeletedListener(()=>{be("Terminating Firestore due to IndexedDb database deletion"),e.terminate().then(()=>{p("Terminating Firestore due to IndexedDb database deletion completed successfully")}).catch(e=>{be("Terminating Firestore due to IndexedDb database deletion failed",e)})}),e._offlineComponents=t}async function Th(e,r){e.asyncQueue.verifyOperationInProgress();var t=await Eh(e);p(_h,"Initializing OnlineComponentProvider"),await r.initialize(t,e.configuration),e.setCredentialChangeListener(e=>yl(r.remoteStore,e)),e.setAppCheckTokenChangeListener((e,t)=>yl(r.remoteStore,t)),e._onlineComponents=r}async function Eh(t){if(!t._offlineComponents)if(t._uninitializedComponentsProvider){p(_h,"Using user provided OfflineComponentProvider");try{await Ih(t,t._uninitializedComponentsProvider._offline)}catch(e){var r=e;if(!("FirebaseError"===(n=r).name?n.code===b.FAILED_PRECONDITION||n.code===b.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&n instanceof DOMException)||22===n.code||20===n.code||11===n.code))throw r;be("Error using user provided cache. Falling back to memory cache: "+r),await Ih(t,new hh)}}else p(_h,"Using default OfflineComponentProvider"),await Ih(t,new ch(void 0));var n;return t._offlineComponents}async function Sh(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(p(_h,"Using user provided OnlineComponentProvider"),await Th(e,e._uninitializedComponentsProvider._online)):(p(_h,"Using default OnlineComponentProvider"),await Th(e,new gh))),e._onlineComponents}function xh(e){return Eh(e).then(e=>e.persistence)}function Ch(e){return Eh(e).then(e=>e.localStore)}function Ah(e){return Sh(e).then(e=>e.remoteStore)}function Dh(e){return Sh(e).then(e=>e.syncEngine)}async function Nh(e){var t=await Sh(e),r=t.eventManager;return r.onListen=(async function(e,t,r=!0){var n=oh(e);let i;var s=n.Pu.get(t);return i=s?(n.sharedClientState.addLocalQueryTarget(s.targetId),s.view.cu()):await Kl(n,t,r,!0)}).bind(null,t.syncEngine),r.onUnlisten=(async function(e,t,r){let n=e,i=n.Pu.get(t),s=n.Tu.get(i.targetId);1<s.length?(n.Tu.set(i.targetId,s.filter(e=>!Ei(e,t))),n.Pu.delete(t)):n.isPrimaryClient?(n.sharedClientState.removeLocalQueryTarget(i.targetId),n.sharedClientState.isActiveQueryTarget(i.targetId)||await mu(n.localStore,i.targetId,!1).then(()=>{n.sharedClientState.clearQueryState(i.targetId),r&&sl(n.remoteStore,i.targetId),Zl(n,i.targetId)}).catch(ut)):(Zl(n,i.targetId),await mu(n.localStore,i.targetId,!0))}).bind(null,t.syncEngine),r.onFirstRemoteStoreListen=(async function(e,t){await Kl(oh(e),t,!0,!1)}).bind(null,t.syncEngine),r.onLastRemoteStoreUnlisten=(async function(e,t){var r=e,n=r.Pu.get(t),i=r.Tu.get(n.targetId);r.isPrimaryClient&&1===i.length&&(r.sharedClientState.removeLocalQueryTarget(n.targetId),sl(r.remoteStore,n.targetId))}).bind(null,t.syncEngine),r}function kh(r){return r.asyncQueue.enqueue(async()=>{var e=await xh(r),t=await Ah(r);return e.setNetworkEnabled(!1),(async e=>{var t=e;t.Ia.add(0),await nl(t),t.Aa.set("Offline")})(t)})}function Rh(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await((e,t)=>{let r=e;return r.persistence.runTransaction("read document","readonly",e=>r.localDocuments.getDocument(e,t))})(e,t);n.isFoundDocument()?r.resolve(n):n.isNoDocument()?r.resolve(null):r.reject(new I(b.UNAVAILABLE,"Failed to get document from cache. (However, this document may exist on the server. Run again without setting 'source' in the GetOptions to attempt to retrieve the document from the server.)"))}catch(e){n=Il(e,`Failed to get document '${t} from cache`);r.reject(n)}})(await Ch(e),t,r)),r.promise}function Oh(e,t,l={}){let h=new f;return e.asyncQueue.enqueueAndForget(async()=>{{var i=await Nh(e),s=e.asyncQueue,a=t,o=l,u=h;let r=new ph({next:e=>{r.Ou(),s.enqueueAndForget(()=>Nl(i,n));var t=e.docs.has(a);!t&&e.fromCache?u.reject(new I(b.UNAVAILABLE,"Failed to get document because the client is offline.")):t&&e.fromCache&&o&&"server"===o.source?u.reject(new I(b.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):u.resolve(e)},error:e=>u.reject(e)}),n=new Rl(yi(a.path),r,{includeMetadataChanges:!0,ka:!0});return Dl(i,n)}}),h.promise}function Lh(e,t){let r=new f;return e.asyncQueue.enqueueAndForget(async()=>(async(e,t,r)=>{try{var n=await pu(e,t,!0),i=new Ul(t,n.qs),s=i.nu(n.documents),a=i.applyChanges(s,!1);r.resolve(a.snapshot)}catch(e){n=Il(e,`Failed to execute query '${t} against cache`);r.reject(n)}})(await Ch(e),t,r)),r.promise}function Fh(o,u,l={}){let h=new f;return o.asyncQueue.enqueueAndForget(async()=>{{var n=await Nh(o),i=o.asyncQueue,e=u,s=l,a=h;let t=new ph({next:e=>{t.Ou(),i.enqueueAndForget(()=>Nl(n,r)),e.fromCache&&"server"===s.source?a.reject(new I(b.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):a.resolve(e)},error:e=>a.reject(e)}),r=new Rl(e,t,{includeMetadataChanges:!0,ka:!0});return Dl(n,r)}}),h.promise}function Vh(r,e){let n=new ph(e);return r.asyncQueue.enqueueAndForget(async()=>{return e=await Nh(r),t=n,e.Da.add(t),void t.next();var e,t}),()=>{n.Ou(),r.asyncQueue.enqueueAndForget(async()=>{var e,t;e=await Nh(r),t=n,e.Da.delete(t)})}}function Mh(e,t,r,n){r=r,t=$u(t),s="string"==typeof r?Re().encode(r):r,r=((e,t)=>{if(e instanceof Uint8Array)return mh(e,t);if(e instanceof ArrayBuffer)return mh(new Uint8Array(e),t);if(e instanceof ReadableStream)return e.getReader();throw new Error("Source of `toByteStreamReader` has to be a ArrayBuffer or ReadableStream")})(s),t=t;let i=new yh(r,t);var s;e.asyncQueue.enqueueAndForget(async()=>{lh(await Dh(e),i,n)})}function Ph(n,i){return n.asyncQueue.enqueue(async()=>{{var e=await Ch(n),r=i;let t=e;return t.persistence.runTransaction("Get named query","readonly",e=>t.Ti.getNamedQuery(e,r))}})}function Uh(e){var t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let Bh=new Map,qh="firestore.googleapis.com";class jh{constructor(e){var t;if(void 0===e.host){if(void 0!==e.ssl)throw new I(b.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=qh,this.ssl=!0}else this.host=e.host,this.ssl=null==(t=e.ssl)||t;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new I(b.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}je("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=Uh(null!=(t=e.experimentalLongPollingOptions)?t:{});var r=this.experimentalLongPollingOptions;if(void 0!==r.timeoutSeconds){if(isNaN(r.timeoutSeconds))throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (must not be NaN)`);if(r.timeoutSeconds<5)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (minimum allowed value is 5)`);if(30<r.timeoutSeconds)throw new I(b.INVALID_ARGUMENT,`invalid long polling timeout: ${r.timeoutSeconds} (maximum allowed value is 30)`)}this.useFetchStreams=!!e.useFetchStreams}isEqual(e){return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,r=e.experimentalLongPollingOptions,t.timeoutSeconds===r.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams;var t,r}}class zh{constructor(e,t,r,n){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=n,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new jh({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(this._app)return this._app;throw new I(b.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available")}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new jh(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=(e=>{if(!e)return new Se;switch(e.type){case"firstParty":return new De(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new I(b.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}})(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return e=this,(t=Bh.get(e))&&(p("ComponentProvider","Removing Datastore"),Bh.delete(e),t.terminate()),Promise.resolve();var e,t}}function Kh(n,e,t,i={}){n=g(n,zh);let r=X(e),s=n._getSettings(),a=Object.assign(Object.assign({},s),{emulatorOptions:n._getEmulatorOptions()}),o=e+":"+t;r&&((async e=>(await fetch(e,{credentials:"include"})).ok)("https://"+o),Z("Firestore",!0)),s.host!==qh&&s.host!==o&&be("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");var u=Object.assign(Object.assign({},s),{host:o,ssl:r,emulatorOptions:i});if(!oe(u,a)&&(n._setSettings(u),i.mockUserToken)){let t,r;if("string"==typeof i.mockUserToken)t=i.mockUserToken,r=l.MOCK_USER;else{t=((e,t)=>{if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var r=t||"demo-project",n=e.iat||0,i=e.sub||e.user_id;if(i)return r=Object.assign({iss:"https://securetoken.google.com/"+r,aud:r,iat:n,exp:n+3600,auth_time:n,sub:i,user_id:i,firebase:{sign_in_provider:"custom",identities:{}}},e),[z(JSON.stringify({alg:"none",type:"JWT"})),z(JSON.stringify(r)),""].join(".");throw new Error("mockUserToken must contain 'sub' or 'user_id' field!")})(i.mockUserToken,null==(u=n._app)?void 0:u.options.projectId);let e=i.mockUserToken.sub||i.mockUserToken.user_id;if(!e)throw new I(b.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");r=new l(e)}n._authCredentials=new xe(new Ee(t,r))}}class Gh{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new Gh(this.firestore,e,this._query)}}class P{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new $h(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new P(this.firestore,e,this._key)}toJSON(){return{type:P._jsonSchemaVersion,referencePath:this._key.toString()}}static fromJSON(e,t,r){if(He(t,P._jsonSchema))return new P(e,r||null,new x(T.fromString(t.referencePath)))}}P._jsonSchemaVersion="firestore/documentReference/1.0",P._jsonSchema={type:t("string",P._jsonSchemaVersion),referencePath:t("string")};class $h extends Gh{constructor(e,t,r){super(e,t,yi(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){var e=this._path.popLast();return e.isEmpty()?null:new P(this.firestore,null,new x(e))}withConverter(e){return new $h(this.firestore,e,this._path)}}function Qh(e,t,...r){var n;if(e=_(e),qe("collection","path",t),e instanceof zh)return Ke(n=T.fromString(t,...r)),new $h(e,null,n);if(e instanceof P||e instanceof $h)return Ke(n=e._path.child(T.fromString(t,...r))),new $h(e.firestore,null,n);throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function Hh(e,t,...r){var n;if(e=_(e),qe("doc","path",t=1===arguments.length?Oe.newId():t),e instanceof zh)return ze(n=T.fromString(t,...r)),new P(e,null,new x(n));if(e instanceof P||e instanceof $h)return ze(n=e._path.child(T.fromString(t,...r))),new P(e.firestore,e instanceof $h?e.converter:null,new x(n));throw new I(b.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore")}function Wh(e,t){return e=_(e),t=_(t),(e instanceof P||e instanceof $h)&&(t instanceof P||t instanceof $h)&&e.firestore===t.firestore&&e.path===t.path&&e.converter===t.converter}function Xh(e,t){return e=_(e),t=_(t),e instanceof Gh&&t instanceof Gh&&e.firestore===t.firestore&&Ei(e._query,t._query)&&e.converter===t.converter}let Yh="AsyncQueue";class Jh{constructor(e=Promise.resolve()){this.Zu=[],this.Xu=!1,this.ec=[],this.tc=null,this.nc=!1,this.rc=!1,this.sc=[],this.F_=new Qu(this,"async_queue_retry"),this.oc=()=>{var e=Gu();e&&p(Yh,"Visibility state changed to "+e.visibilityState),this.F_.y_()},this._c=e;var t=Gu();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.oc)}get isShuttingDown(){return this.Xu}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.ac(),this.uc(e)}enterRestrictedMode(e){var t;this.Xu||(this.Xu=!0,this.rc=e||!1,(t=Gu())&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.oc))}enqueue(e){if(this.ac(),this.Xu)return new Promise(()=>{});let t=new f;return this.uc(()=>this.Xu&&this.rc?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Zu.push(e),this.cc()))}async cc(){if(0!==this.Zu.length){try{await this.Zu[0](),this.Zu.shift(),this.F_.reset()}catch(e){if(!mt(e))throw e;p(Yh,"Operation failed with retryable error: "+e)}0<this.Zu.length&&this.F_.g_(()=>this.cc())}}uc(e){var t=this._c.then(()=>(this.nc=!0,e().catch(e=>{throw this.tc=e,this.nc=!1,d("INTERNAL UNHANDLED ERROR: ",Zh(e)),e}).then(e=>(this.nc=!1,e))));return this._c=t}enqueueAfterDelay(e,t,r){this.ac(),-1<this.sc.indexOf(e)&&(t=0);var n=bl.createAndSchedule(this,e,t,r,e=>this.lc(e));return this.ec.push(n),n}ac(){this.tc&&E(47125,{hc:Zh(this.tc)})}verifyOperationInProgress(){}async Pc(){for(var e;await(e=this._c),e!==this._c;);}Tc(e){for(var t of this.ec)if(t.timerId===e)return!0;return!1}Ic(t){return this.Pc().then(()=>{this.ec.sort((e,t)=>e.targetTimeMs-t.targetTimeMs);for(var e of this.ec)if(e.skipDelay(),"all"!==t&&e.timerId===t)break;return this.Pc()})}dc(e){this.sc.push(e)}lc(e){var t=this.ec.indexOf(e);this.ec.splice(t,1)}}function Zh(e){let t=e.message||"";return t=e.stack?e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack:t}function ec(t){var r=t,t=["next","error","complete"];if("object"==typeof r&&null!==r){var n=r;for(let e of t)if(e in n&&"function"==typeof n[e])return 1}}class tc{constructor(){this._progressObserver={},this._taskCompletionResolver=new f,this._lastProgress={taskState:"Running",totalBytes:0,totalDocuments:0,bytesLoaded:0,documentsLoaded:0}}onProgress(e,t,r){this._progressObserver={next:e,error:t,complete:r}}catch(e){return this._taskCompletionResolver.promise.catch(e)}then(e,t){return this._taskCompletionResolver.promise.then(e,t)}_completeWith(e){this._updateProgress(e),this._progressObserver.complete&&this._progressObserver.complete(),this._taskCompletionResolver.resolve(e)}_failWith(e){this._lastProgress.taskState="Error",this._progressObserver.next&&this._progressObserver.next(this._lastProgress),this._progressObserver.error&&this._progressObserver.error(e),this._taskCompletionResolver.reject(e)}_updateProgress(e){this._lastProgress=e,this._progressObserver.next&&this._progressObserver.next(e)}}var rc,nc,n,ic;class U extends zh{constructor(e,t,r,n){super(e,t,r,n),this.type="firestore",this._queue=new Jh,this._persistenceKey=(null==n?void 0:n.name)||"[DEFAULT]"}async _terminate(){var e;this._firestoreClient&&(e=this._firestoreClient.terminate(),this._queue=new Jh(e),this._firestoreClient=void 0,await e)}}function sc(e){if(e._terminated)throw new I(b.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||ac(e),e._firestoreClient}function ac(e){var t,r,n,i,s,a=e._freezeSettings(),o=(i=e._databaseId,t=(null==(o=e._app)?void 0:o.options.appId)||"",r=e._persistenceKey,n=a,new fn(i,t,r,n.host,n.ssl,n.experimentalForceLongPolling,n.experimentalAutoDetectLongPolling,Uh(n.experimentalLongPollingOptions),n.useFetchStreams,n.isUsingEmulator));e._componentsProvider||null!=(s=a.localCache)&&s._offlineComponentProvider&&null!=(s=a.localCache)&&s._onlineComponentProvider&&(e._componentsProvider={_offline:a.localCache._offlineComponentProvider,_online:a.localCache._onlineComponentProvider}),e._firestoreClient=new bh(e._authCredentials,e._appCheckCredentials,e._queue,o,e._componentsProvider&&(i=e._componentsProvider,s=null==i?void 0:i._online.build(),{_offline:null==i?void 0:i._offline.build(s),_online:s}))}function oc(e,t,r){if((e=g(e,U))._firestoreClient||e._terminated)throw new I(b.FAILED_PRECONDITION,"Firestore has already been started and persistence can no longer be enabled. You can only enable persistence before calling any other methods on a Firestore object.");if(e._componentsProvider||e._getSettings().localCache)throw new I(b.FAILED_PRECONDITION,"SDK cache is already specified.");e._componentsProvider={_online:t,_offline:r},ac(e)}function uc(r){if(r._initialized&&!r._terminated)throw new I(b.FAILED_PRECONDITION,"Persistence can only be cleared before a Firestore instance is initialized or after it is terminated.");let n=new f;return r._queue.enqueueAndForgetEvenWhileRestricted(async()=>{try{e=tu(r._databaseId,r._persistenceKey),await(ct.C()?(t=e+"main",void await ct.delete(t)):Promise.resolve()),n.resolve()}catch(e){n.reject(e)}var e,t}),n.promise}function lc(e){return(r=sc(e=g(e,U))).asyncQueue.enqueue(async()=>{var e=await xh(r),t=await Ah(r);return e.setNetworkEnabled(!0),(e=t).Ia.delete(0),rl(e)});var r}class hc{constructor(e){this._byteString=e}static fromBase64String(e){try{return new hc(N.fromBase64String(e))}catch(e){throw new I(b.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new hc(N.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}toJSON(){return{type:hc._jsonSchemaVersion,bytes:this.toBase64()}}static fromJSON(e){if(He(e,hc._jsonSchema))return hc.fromBase64String(e.bytes)}}hc._jsonSchemaVersion="firestore/bytes/1.0",hc._jsonSchema={type:t("string",hc._jsonSchemaVersion),bytes:t("string")};class cc{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new I(b.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new h(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class dc{constructor(e){this._methodName=e}}class fc{constructor(e,t){if(!isFinite(e)||e<-90||90<e)throw new I(b.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||180<t)throw new I(b.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}_compareTo(e){return S(this._lat,e._lat)||S(this._long,e._long)}toJSON(){return{latitude:this._lat,longitude:this._long,type:fc._jsonSchemaVersion}}static fromJSON(e){if(He(e,fc._jsonSchema))return new fc(e.latitude,e.longitude)}}fc._jsonSchemaVersion="firestore/geoPoint/1.0",fc._jsonSchema={type:t("string",fc._jsonSchemaVersion),latitude:t("number"),longitude:t("number")};class gc{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){var t=this._values,r=e._values;if(t.length!==r.length)return!1;for(let n=0;n<t.length;++n)if(t[n]!==r[n])return!1;return!0}toJSON(){return{type:gc._jsonSchemaVersion,vectorValues:this._values}}static fromJSON(e){if(He(e,gc._jsonSchema)){if(Array.isArray(e.vectorValues)&&e.vectorValues.every(e=>"number"==typeof e))return new gc(e.vectorValues);throw new I(b.INVALID_ARGUMENT,"Expected 'vectorValues' field to be a number array")}}}gc._jsonSchemaVersion="firestore/vectorValue/1.0",gc._jsonSchema={type:t("string",gc._jsonSchemaVersion),vectorValues:t("object")};let mc=/^__.*__$/;class pc{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return null!==this.fieldMask?new as(e,this.data,this.fieldMask,t,this.fieldTransforms):new ss(e,this.data,t,this.fieldTransforms)}}class yc{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return new as(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function vc(e){switch(e){case 0:case 2:case 1:return 1;case 3:case 4:return;default:throw E(40011,{Ec:e})}}class wc{constructor(e,t,r,n,i,s){this.settings=e,this.databaseId=t,this.serializer=r,this.ignoreUndefinedProperties=n,void 0===i&&this.Ac(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get Ec(){return this.settings.Ec}Rc(e){return new wc(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}Vc(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.Rc({path:t,mc:!1});return t.fc(e),t}gc(e){var t=null==(t=this.path)?void 0:t.child(e),t=this.Rc({path:t,mc:!1});return t.Ac(),t}yc(e){return this.Rc({path:void 0,mc:!0})}wc(e){return Uc(e,this.settings.methodName,this.settings.Sc||!1,this.path,this.settings.bc)}contains(t){return void 0!==this.fieldMask.find(e=>t.isPrefixOf(e))||void 0!==this.fieldTransforms.find(e=>t.isPrefixOf(e.field))}Ac(){if(this.path)for(let e=0;e<this.path.length;e++)this.fc(this.path.get(e))}fc(e){if(0===e.length)throw this.wc("Document fields must not be empty");if(vc(this.Ec)&&mc.test(e))throw this.wc('Document fields cannot begin and end with "__"')}}class _c{constructor(e,t,r){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=r||$u(e)}Dc(e,t,r,n=!1){return new wc({Ec:e,methodName:t,bc:r,path:h.emptyPath(),mc:!1,Sc:n},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function bc(e){var t=e._freezeSettings(),r=$u(e._databaseId);return new _c(e._databaseId,!!t.ignoreUndefinedProperties,r)}function Ic(e,n,i,t,r,s={}){var a=e.Dc(s.merge||s.mergeFields?2:0,n,i,r),o=(Fc("Data must be an object, but it was:",a,t),Oc(t,a));let u,l;if(s.merge)u=new en(a.fieldMask),l=a.fieldTransforms;else if(s.mergeFields){let t=[];for(let r of s.mergeFields){let e=Vc(n,r,i);if(!a.contains(e))throw new I(b.INVALID_ARGUMENT,`Field '${e}' is specified in your field mask but missing from your input data.`);Bc(t,e)||t.push(e)}u=new en(t),l=a.fieldTransforms.filter(e=>u.covers(e.field))}else u=null,l=a.fieldTransforms;return new pc(new qn(o),u,l)}class Tc extends dc{_toFieldTransform(e){if(2!==e.Ec)throw 1===e.Ec?e.wc(this._methodName+"() can only appear at the top level of your update data"):e.wc(this._methodName+"() cannot be used with set() unless you pass {merge:true}");return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof Tc}}function Ec(e,t,r){return new wc({Ec:3,bc:t.settings.bc,methodName:e._methodName,mc:r},t.databaseId,t.serializer,t.ignoreUndefinedProperties)}class Sc extends dc{_toFieldTransform(e){return new Ji(e.path,new Ki)}isEqual(e){return e instanceof Sc}}class xc extends dc{constructor(e,t){super(e),this.vc=t}_toFieldTransform(e){let t=Ec(this,e,!0),r=this.vc.map(e=>Rc(e,t)),n=new Gi(r);return new Ji(e.path,n)}isEqual(e){return e instanceof xc&&oe(this.vc,e.vc)}}class Cc extends dc{constructor(e,t){super(e),this.vc=t}_toFieldTransform(e){let t=Ec(this,e,!0),r=this.vc.map(e=>Rc(e,t)),n=new Qi(r);return new Ji(e.path,n)}isEqual(e){return e instanceof Cc&&oe(this.vc,e.vc)}}class Ac extends dc{constructor(e,t){super(e),this.Cc=t}_toFieldTransform(e){var t=new Wi(e.serializer,qi(e.serializer,this.Cc));return new Ji(e.path,t)}isEqual(e){return e instanceof Ac&&this.Cc===e.Cc}}function Dc(e,i,s,t){let a=e.Dc(1,i,s),o=(Fc("Data must be an object, but it was:",a,t),[]),u=qn.empty();Hr(t,(e,t)=>{var r=Pc(i,e,s),n=(t=_(t),a.gc(r));if(t instanceof Tc)o.push(r);else{let e=Rc(t,n);null!=e&&(o.push(r),u.set(r,e))}});var r=new en(o);return new yc(u,r,a.fieldTransforms)}function Nc(e,t,r,n,i,s){var a=e.Dc(1,t,r),o=[Vc(t,n,r)],u=[i];if(s.length%2!=0)throw new I(b.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let f=0;f<s.length;f+=2)o.push(Vc(t,s[f])),u.push(s[f+1]);var l=[],h=qn.empty();for(let g=o.length-1;0<=g;--g)if(!Bc(l,o[g])){let t=o[g];var c=_(u[g]);let r=a.gc(t);if(c instanceof Tc)l.push(t);else{let e=Rc(c,r);null!=e&&(l.push(t),h.set(t,e))}}var d=new en(l);return new yc(h,d,a.fieldTransforms)}function kc(e,t,r,n=!1){return Rc(r,e.Dc(n?4:3,t))}function Rc(e,r){if(Lc(e=_(e)))return Fc("Unsupported field value:",r,e),Oc(e,r);if(e instanceof dc){{var t=e;var n=r;if(!vc(n.Ec))throw n.wc(t._methodName+"() can only be used with update() and set()");if(!n.path)throw n.wc(t._methodName+"() is not currently supported inside arrays");var i=t._toFieldTransform(n);i&&n.fieldTransforms.push(i)}return null}if(void 0===e&&r.ignoreUndefinedProperties)return null;if(r.path&&r.fieldMask.push(r.path),e instanceof Array){if(r.settings.mc&&4!==r.Ec)throw r.wc("Nested arrays are not supported");{var s,a=r,o=[];let t=0;for(s of e){let e=Rc(s,a.yc(t));null==e&&(e={nullValue:"NULL_VALUE"}),o.push(e),t++}return{arrayValue:{values:o}}}}var u,t=e,n=r;if(null===(t=_(t)))return{nullValue:"NULL_VALUE"};if("number"==typeof t)return qi(n.serializer,t);if("boolean"==typeof t)return{booleanValue:t};if("string"==typeof t)return{stringValue:t};if(t instanceof Date)return l=v.fromDate(t),{timestampValue:Ms(n.serializer,l)};if(t instanceof v)return l=new v(t.seconds,1e3*Math.floor(t.nanoseconds/1e3)),{timestampValue:Ms(n.serializer,l)};if(t instanceof fc)return{geoPointValue:{latitude:t.latitude,longitude:t.longitude}};if(t instanceof hc)return{bytesValue:Ps(n.serializer,t._byteString)};if(t instanceof P){var l=n.databaseId,i=t.firestore._databaseId;if(i.isEqual(l))return{referenceValue:Us(t.firestore._databaseId||n.databaseId,t._key.path)};throw n.wc(`Document reference is for database ${i.projectId}/${i.database} but should be for database ${l.projectId}/`+l.database)}if(t instanceof gc)return e=t,u=n,{mapValue:{fields:{[pn]:{stringValue:wn},[_n]:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw u.wc("VectorValues must only contain numeric values.");return Ui(u.serializer,e)})}}}}};throw n.wc("Unsupported field value: "+$e(t))}function Oc(e,n){let i={};return Wr(e)?n.path&&0<n.path.length&&n.fieldMask.push(n.path):Hr(e,(e,t)=>{var r=Rc(t,n.Vc(e));null!=r&&(i[e]=r)}),{mapValue:{fields:i}}}function Lc(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof v||e instanceof fc||e instanceof hc||e instanceof P||e instanceof dc||e instanceof gc)}function Fc(e,t,r){var n;if(!Lc(r)||!Ge(r))throw"an object"===(n=$e(r))?t.wc(e+" a custom object"):t.wc(e+" "+n)}function Vc(e,t,r){if((t=_(t))instanceof cc)return t._internalPath;if("string"==typeof t)return Pc(e,t);throw Uc("Field path arguments must be of type string or ",e,!1,void 0,r)}let Mc=new RegExp("[~\\*/\\[\\]]");function Pc(t,r,n){if(0<=r.search(Mc))throw Uc(`Invalid field path (${r}). Paths must not contain '~', '*', '/', '[', or ']'`,t,!1,void 0,n);try{return new cc(...r.split("."))._internalPath}catch(e){throw Uc(`Invalid field path (${r}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,t,!1,void 0,n)}}function Uc(e,t,r,n,i){var s=n&&!n.isEmpty(),a=void 0!==i;let o=`Function ${t}() called with invalid data`,u=(r&&(o+=" (via `toFirestore()`)"),o+=". ","");return(s||a)&&(u+=" (found",s&&(u+=" in field "+n),a&&(u+=" in document "+i),u+=")"),new I(b.INVALID_ARGUMENT,o+e+u)}function Bc(e,t){return e.some(e=>e.isEqual(t))}class qc{constructor(e,t,r,n,i){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=n,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new P(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){var e;if(this._document)return this._converter?(e=new jc(this._firestore,this._userDataWriter,this._key,this._document,null),this._converter.fromFirestore(e)):this._userDataWriter.convertValue(this._document.data.value)}get(e){if(this._document){var t=this._document.data.field(zc("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class jc extends qc{data(){return super.data()}}function zc(e,t){return"string"==typeof t?Pc(e,t):(t instanceof cc?t:t._delegate)._internalPath}function Kc(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new I(b.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}class Gc{}class $c extends Gc{}function Qc(e,t,...r){let n=[];t instanceof Gc&&n.push(t);var t=n=n.concat(r),i=t.filter(e=>!1).length,s=t.filter(e=>e instanceof Hc).length;if(1<i||0<i&&0<s)throw new I(b.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.");for(let t of n)e=t._apply(e);return e}class Hc extends $c{constructor(e,t,r){super(),this._field=e,this._op=t,this._value=r,this.type="where"}static _create(e,t,r){return new Hc(e,t,r)}_apply(e){var t=this._parse(e);return rd(e._query,t),new Gh(e.firestore,e.converter,Ii(e._query,t))}_parse(e){var t=bc(e.firestore);{var n=e._query,i="where",s=t,a=e.firestore._databaseId,o=(e=this._field,this._op),u=this._value;let r;if(e.isKeyField()){if("array-contains"===o||"array-contains-any"===o)throw new I(b.INVALID_ARGUMENT,`Invalid Query. You can't perform '${o}' queries on documentId().`);if("in"===o||"not-in"===o){td(u,o);let e=[];for(let t of u)e.push(ed(a,n,t));r={arrayValue:{values:e}}}else r=ed(a,n,u)}else"in"!==o&&"not-in"!==o&&"array-contains-any"!==o||td(u,o),r=kc(s,i,u,"in"===o||"not-in"===o);return O.create(e,o,r)}}}(class extends Gc{});class Wc extends $c{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new Wc(e,t)}_apply(e){var t=((e,t,r)=>{if(null!==e.startAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new I(b.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new Gn(t,r)})(e._query,this._field,this._direction);return new Gh(e.firestore,e.converter,(t=(e=e._query).explicitOrderBy.concat([t]),new mi(e.path,e.collectionGroup,t,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)))}}class Xc extends $c{constructor(e,t,r){super(),this.type=e,this._limit=t,this._limitType=r}static _create(e,t,r){return new Xc(e,t,r)}_apply(e){return new Gh(e.firestore,e.converter,Ti(e._query,this._limit,this._limitType))}}class Yc extends $c{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new Yc(e,t,r)}_apply(e){var t,r=Zc(e,this.type,this._docOrFields,this._inclusive);return new Gh(e.firestore,e.converter,(e=e._query,t=r,new mi(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,t,e.endAt)))}}class Jc extends $c{constructor(e,t,r){super(),this.type=e,this._docOrFields=t,this._inclusive=r}static _create(e,t,r){return new Jc(e,t,r)}_apply(e){var t,r=Zc(e,this.type,this._docOrFields,this._inclusive);return new Gh(e.firestore,e.converter,(e=e._query,t=r,new mi(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,e.startAt,t)))}}function Zc(e,r,n,i){if(n[0]=_(n[0]),n[0]instanceof qc){var s=e._query,a=e.firestore._databaseId,o=r,u=n[0]._document,l=i;if(!u)throw new I(b.NOT_FOUND,`Can't use a DocumentSnapshot that doesn't exist for ${o}().`);var h=[];for(let t of _i(s))if(t.field.isKeyField())h.push(Dn(a,u.key));else{let e=u.data.field(t.field);if(hn(e))throw new I(b.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+t.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){let e=t.field.canonicalString();throw new I(b.INVALID_ARGUMENT,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}h.push(e)}return new jn(h,l)}var t=bc(e.firestore),c=e._query,d=e.firestore._databaseId,f=t,g=r,m=n,o=i,p=c.explicitOrderBy;if(m.length>p.length)throw new I(b.INVALID_ARGUMENT,`Too many arguments provided to ${g}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);var y=[];for(let w=0;w<m.length;w++){var v=m[w];if(p[w].field.isKeyField()){if("string"!=typeof v)throw new I(b.INVALID_ARGUMENT,`Invalid query. Expected a string for document ID in ${g}(), but got a `+typeof v);if(!wi(c)&&-1!==v.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${g}() must be a plain document ID, but '${v}' contains a slash.`);let e=c.path.child(T.fromString(v));if(!x.isDocumentKey(e))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${g}() must result in a valid document path, but '${e}' is not because it contains an odd number of segments.`);let t=new x(e);y.push(Dn(d,t))}else{let e=kc(f,g,v);y.push(e)}}return new jn(y,o)}function ed(e,t,r){if("string"==typeof(r=_(r))){if(""===r)throw new I(b.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!wi(t)&&-1!==r.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${r}' contains a '/' character.`);var n=t.path.child(T.fromString(r));if(x.isDocumentKey(n))return Dn(e,new x(n));throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${n}' is not because it has an odd number of segments (${n.length}).`)}if(r instanceof P)return Dn(e,r._key);throw new I(b.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${$e(r)}.`)}function td(e,t){if(!Array.isArray(e)||0===e.length)throw new I(b.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function rd(e,t){var r=((t,r)=>{for(var n of t)for(let e of n.getFlattenedFilters())if(0<=r.indexOf(e.op))return e.op;return null})(e.filters,(e=>{switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}})(t.op));if(null!==r)throw r===t.op?new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new I(b.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${r.toString()}' filters.`)}class nd{convertValue(e,t="none"){switch(In(e)){case 0:return null;case 1:return e.booleanValue;case 2:return k(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(sn(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw E(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,r="none"){let n={};return Hr(e,(e,t)=>{n[e]=this.convertValue(t,r)}),n}convertVectorValue(e){var t=null==(t=null==(t=null==(t=e.fields)?void 0:t[_n].arrayValue)?void 0:t.values)?void 0:t.map(e=>k(e.doubleValue));return new gc(t)}convertGeoPoint(e){return new fc(k(e.latitude),k(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":var r=cn(e);return null==r?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(dn(e));default:return null}}convertTimestamp(e){var t=nn(e);return new v(t.seconds,t.nanos)}convertDocumentKey(e,t){var r=T.fromString(e),n=(y(oa(r),9688,{name:e}),new mn(r.get(1),r.get(3))),r=new x(r.popFirst(5));return n.isEqual(t)||d(`Document ${r} contains a document reference within a different database (${n.projectId}/${n.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),r}}function id(e,t,r){return e?r&&(r.merge||r.mergeFields)?e.toFirestore(t,r):e.toFirestore(t):t}class sd extends nd{constructor(e){super(),this.firestore=e}convertBytes(e){return new hc(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new P(this.firestore,null,t)}}class ad{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class od extends qc{constructor(e,t,r,n,i,s){super(e,t,r,n,s),this._firestore=e,this._firestoreImpl=e,this.metadata=i}exists(){return super.exists()}data(e={}){var t;if(this._document)return this._converter?(t=new ud(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null),this._converter.fromFirestore(t,e)):this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}get(e,t={}){if(this._document){var r=this._document.data.field(zc("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}toJSON(){if(this.metadata.hasPendingWrites)throw new I(b.FAILED_PRECONDITION,"DocumentSnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");var e=this._document,t={};return t.type=od._jsonSchemaVersion,t.bundle="",t.bundleSource="DocumentSnapshot",t.bundleName=this._key.toString(),e&&e.isValidDocument()&&e.isFoundDocument()&&(this._userDataWriter.convertObjectMap(e.data.value.mapValue.fields,"previous"),t.bundle=(this._firestore,this.ref.path,"NOT SUPPORTED")),t}}od._jsonSchemaVersion="firestore/documentSnapshot/1.0",od._jsonSchema={type:t("string",od._jsonSchemaVersion),bundleSource:t("string","DocumentSnapshot"),bundleName:t("string"),bundle:t("string")};class ud extends od{data(e={}){return super.data(e)}}class ld{constructor(e,t,r,n){this._firestore=e,this._userDataWriter=t,this._snapshot=n,this.metadata=new ad(n.hasPendingWrites,n.fromCache),this.query=r}get docs(){let t=[];return this.forEach(e=>t.push(e)),t}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(t,r){this._snapshot.docs.forEach(e=>{t.call(r,new ud(this._firestore,this._userDataWriter,e.key,e,new ad(this._snapshot.mutatedKeys.has(e.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){var t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new I(b.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=((s,t)=>{if(s._snapshot.oldDocs.isEmpty()){let r=0;return s._snapshot.docChanges.map(e=>{var t=new ud(s._firestore,s._userDataWriter,e.doc.key,e.doc,new ad(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);return e.doc,{type:"added",doc:t,oldIndex:-1,newIndex:r++}})}{let i=s._snapshot.oldDocs;return s._snapshot.docChanges.filter(e=>t||3!==e.type).map(e=>{var t=new ud(s._firestore,s._userDataWriter,e.doc.key,e.doc,new ad(s._snapshot.mutatedKeys.has(e.doc.key),s._snapshot.fromCache),s.query.converter);let r=-1,n=-1;return 0!==e.type&&(r=i.indexOf(e.doc.key),i=i.delete(e.doc.key)),1!==e.type&&(i=i.add(e.doc),n=i.indexOf(e.doc.key)),{type:(e=>{switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return E(61501,{type:e})}})(e.type),doc:t,oldIndex:r,newIndex:n}})}})(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}toJSON(){if(this.metadata.hasPendingWrites)throw new I(b.FAILED_PRECONDITION,"QuerySnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");var e={};e.type=ld._jsonSchemaVersion,e.bundleSource="QuerySnapshot",e.bundleName=Oe.newId(),this._firestore._databaseId.database,this._firestore._databaseId.projectId;let t=[],r=[],n=[];return this.docs.forEach(e=>{null!==e._document&&(t.push(e._document),r.push(this._userDataWriter.convertObjectMap(e._document.data.value.mapValue.fields,"previous")),n.push(e.ref.path))}),e.bundle=(this._firestore,this.query._query,"NOT SUPPORTED"),e}}function hd(e,t){return e instanceof od&&t instanceof od?e._firestore===t._firestore&&e._key.isEqual(t._key)&&(null===e._document?null===t._document:e._document.isEqual(t._document))&&e._converter===t._converter:e instanceof ld&&t instanceof ld&&e._firestore===t._firestore&&Xh(e.query,t.query)&&e.metadata.isEqual(t.metadata)&&e._snapshot.isEqual(t._snapshot)}ld._jsonSchemaVersion="firestore/querySnapshot/1.0",ld._jsonSchema={type:t("string",ld._jsonSchemaVersion),bundleSource:t("string","QuerySnapshot"),bundleName:t("string"),bundle:t("string")};class cd extends nd{constructor(e){super(),this.firestore=e}convertBytes(e){return new hc(e)}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return new P(this.firestore,null,t)}}function dd(e,t,r){e=g(e,P);var n=g(e.firestore,U),i=id(e.converter,t,r);return md(n,[Ic(bc(n),"setDoc",e._key,i,null!==e.converter,r).toMutation(e._key,V.none())])}function fd(e,t,r,...n){e=g(e,P);var i=g(e.firestore,U),s=bc(i);return md(i,[("string"==typeof(t=_(t))||t instanceof cc?Nc(s,"updateDoc",e._key,t,r,n):Dc(s,"updateDoc",e._key,t)).toMutation(e._key,V.exists(!0))])}function gd(n,...i){var t,r;n=_(n);let e={includeMetadataChanges:!1,source:"default"},s=0;var a={includeMetadataChanges:(e="object"!=typeof i[s]||ec(i[s])?e:i[s++]).includeMetadataChanges,source:e.source};if(ec(i[s])){let e=i[s];i[s]=null==(r=e.next)?void 0:r.bind(e),i[s+1]=null==(t=e.error)?void 0:t.bind(e),i[s+2]=null==(r=e.complete)?void 0:r.bind(e)}let o,u,l;if(n instanceof P)u=g(n.firestore,U),l=yi(n._key.path),o={next:e=>{i[s]&&i[s](pd(u,n,e))},error:i[s+1],complete:i[s+2]};else{let t=g(n,Gh),r=(u=g(t.firestore,U),l=t._query,new cd(u));o={next:e=>{i[s]&&i[s](new ld(u,r,t,e))},error:i[s+1],complete:i[s+2]},Kc(n._query)}{var h=sc(u),c=l,d=a,f=o;let e=new ph(f),t=new Rl(c,e,d);return h.asyncQueue.enqueueAndForget(async()=>Dl(await Nh(h),t)),()=>{e.Ou(),h.asyncQueue.enqueueAndForget(async()=>Nl(await Nh(h),t))}}}function md(t,r){{var n=sc(t),i=r;let e=new f;return n.asyncQueue.enqueueAndForget(async()=>$l(await Dh(n),i,e)),e.promise}}function pd(e,t,r){var n=r.docs.get(t._key),i=new cd(e);return new od(e,i,t._key,n,new ad(r.hasPendingWrites,r.fromCache),t.converter)}let yd={maxAttempts:5};class vd{constructor(e,t){this._firestore=e,this._commitHandler=t,this._mutations=[],this._committed=!1,this._dataReader=bc(e)}set(e,t,r){this._verifyNotCommitted();var n=wd(e,this._firestore),i=id(n.converter,t,r),i=Ic(this._dataReader,"WriteBatch.set",n._key,i,null!==n.converter,r);return this._mutations.push(i.toMutation(n._key,V.none())),this}update(e,t,r,...n){this._verifyNotCommitted();var i=wd(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof cc?Nc(this._dataReader,"WriteBatch.update",i._key,t,r,n):Dc(this._dataReader,"WriteBatch.update",i._key,t);return this._mutations.push(s.toMutation(i._key,V.exists(!0))),this}delete(e){this._verifyNotCommitted();var t=wd(e,this._firestore);return this._mutations=this._mutations.concat(new hs(t._key,V.none())),this}commit(){return this._verifyNotCommitted(),this._committed=!0,0<this._mutations.length?this._commitHandler(this._mutations):Promise.resolve()}_verifyNotCommitted(){if(this._committed)throw new I(b.FAILED_PRECONDITION,"A write batch can no longer be used after commit() has been called.")}}function wd(e,t){if((e=_(e)).firestore!==t)throw new I(b.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return e}class _d extends class{constructor(e,t){this._firestore=e,this._transaction=t,this._dataReader=bc(e)}get(e){let r=wd(e,this._firestore),n=new sd(this._firestore);return this._transaction.lookup([r._key]).then(e=>{if(!e||1!==e.length)return E(24041);var t=e[0];if(t.isFoundDocument())return new qc(this._firestore,n,t.key,t,r.converter);if(t.isNoDocument())return new qc(this._firestore,n,r._key,null,r.converter);throw E(18433,{doc:t})})}set(e,t,r){var n=wd(e,this._firestore),i=id(n.converter,t,r),i=Ic(this._dataReader,"Transaction.set",n._key,i,null!==n.converter,r);return this._transaction.set(n._key,i),this}update(e,t,r,...n){var i=wd(e,this._firestore),s="string"==typeof(t=_(t))||t instanceof cc?Nc(this._dataReader,"Transaction.update",i._key,t,r,n):Dc(this._dataReader,"Transaction.update",i._key,t);return this._transaction.update(i._key,s),this}delete(e){var t=wd(e,this._firestore);return this._transaction.delete(t._key),this}}{constructor(e,t){super(e,t),this._firestore=e}get(e){let t=wd(e,this._firestore),r=new cd(this._firestore);return super.get(e).then(e=>new od(this._firestore,r,t._key,e._document,new ad(!1,!1),t.converter))}}function bd(r,n,e){r=g(r,U);var i=Object.assign(Object.assign({},yd),e);if(i.maxAttempts<1)throw new I(b.INVALID_ARGUMENT,"Max attempts must be at least 1");{var s=sc(r),a=e=>n(new _d(r,e)),o=i;let t=new f;return s.asyncQueue.enqueueAndForget(async()=>{var e=await Sh(s).then(e=>e.datastore);new wh(s.asyncQueue,e,o,a,t).zu()}),t.promise}}nc=!0,n=Hd.SDK_VERSION,ve=n,Hd._registerComponent(new le("firestore",(e,{instanceIdentifier:t,options:r})=>{var n=e.getProvider("app").getImmediate(),n=new U(new Ce(e.getProvider("auth-internal")),new ke(n,e.getProvider("app-check-internal")),((e,t)=>{if(Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))return new mn(e.options.projectId,t);throw new I(b.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.')})(n,t),n);return r=Object.assign({useFetchStreams:nc},r),n._setSettings(r),n},"PUBLIC").setMultipleInstances(!0)),Hd.registerVersion(ye,"4.8.0",rc),Hd.registerVersion(ye,"4.8.0","esm2017");function Id(e,t){if(void 0===t)return{merge:!1};if(void 0!==t.mergeFields&&void 0!==t.merge)throw new I("invalid-argument",`Invalid options passed to function ${e}(): You cannot `+'specify both "merge" and "mergeFields".');return t}function Td(){if("undefined"==typeof Uint8Array)throw new I("unimplemented","Uint8Arrays are not available in this environment.")}function Ed(){if("undefined"==typeof atob)throw new I("unimplemented","Blobs are unavailable in Firestore in this environment.")}class Sd{constructor(e){this._delegate=e}static fromBase64String(e){return Ed(),new Sd(hc.fromBase64String(e))}static fromUint8Array(e){return Td(),new Sd(hc.fromUint8Array(e))}toBase64(){return Ed(),this._delegate.toBase64()}toUint8Array(){return Td(),this._delegate.toUint8Array()}isEqual(e){return this._delegate.isEqual(e._delegate)}toString(){return"Blob(base64: "+this.toBase64()+")"}}function xd(e){var t=["next","error","complete"];if("object"==typeof e&&null!==e){var r,n=e;for(r of t)if(r in n&&"function"==typeof n[r])return 1}}class Cd{enableIndexedDbPersistence(e,r){{e=e._delegate;var n={forceOwnership:r};be("enableIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();return oc(e,gh.provider,{build:e=>new dh(e,t.cacheSizeBytes,null==n?void 0:n.forceOwnership)}),Promise.resolve()}}enableMultiTabIndexedDbPersistence(e){return(async e=>{be("enableMultiTabIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead.");let t=e._freezeSettings();oc(e,gh.provider,{build:e=>new fh(e,t.cacheSizeBytes)})})(e._delegate)}clearIndexedDbPersistence(e){return uc(e._delegate)}}class Ad{constructor(e,t,r){this._delegate=t,this._persistenceProvider=r,this.INTERNAL={delete:()=>this.terminate()},e instanceof mn||(this._appCompat=e)}get _databaseId(){return this._delegate._databaseId}settings(e){var t=this._delegate._getSettings();e.merge||t.host===e.host||be("You are overriding the original host. If you did not intend to override your settings, use {merge: true}."),e.merge&&delete(e=Object.assign(Object.assign({},t),e)).merge,this._delegate._setSettings(e)}useEmulator(e,t,r={}){Kh(this._delegate,e,t,r)}enableNetwork(){return lc(this._delegate)}disableNetwork(){return kh(sc(g(this._delegate,U)))}enablePersistence(e){let t=!1,r=!1;return e&&(t=!!e.synchronizeTabs,r=!!e.experimentalForceOwningTab,je("synchronizeTabs",t,"experimentalForceOwningTab",r)),t?this._persistenceProvider.enableMultiTabIndexedDbPersistence(this):this._persistenceProvider.enableIndexedDbPersistence(this,r)}clearPersistence(){return this._persistenceProvider.clearIndexedDbPersistence(this)}terminate(){return this._appCompat&&(this._appCompat._removeServiceInstance("firestore-compat"),this._appCompat._removeServiceInstance("firestore")),this._delegate._delete()}waitForPendingWrites(){var t=this._delegate;{var r=sc(t=g(t,U));let e=new f;return r.asyncQueue.enqueueAndForget(async()=>Xl(await Dh(r),e)),e.promise}}onSnapshotsInSync(e){return t=this._delegate,e=e,Vh(sc(t=g(t,U)),ec(e)?e:{next:e});var t}get app(){if(this._appCompat)return this._appCompat;throw new I("failed-precondition","Firestore was not initialized using the Firebase SDK. 'app' is not available")}collection(e){try{return new jd(this,Qh(this._delegate,e))}catch(e){throw Ld(e,"collection()","Firestore.collection()")}}doc(e){try{return new Od(this,Hh(this._delegate,e))}catch(e){throw Ld(e,"doc()","Firestore.doc()")}}collectionGroup(e){try{return new Ud(this,((e,t)=>{if(e=g(e,zh),qe("collectionGroup","collection id",t),0<=t.indexOf("/"))throw new I(b.INVALID_ARGUMENT,`Invalid collection ID '${t}' passed to function collectionGroup(). Collection IDs must not contain '/'.`);return new Gh(e,null,(e=t,new mi(T.emptyPath(),e)))})(this._delegate,e))}catch(e){throw Ld(e,"collectionGroup()","Firestore.collectionGroup()")}}runTransaction(t){return bd(this._delegate,e=>t(new Nd(this,e)))}batch(){return sc(this._delegate),new kd(new vd(this._delegate,e=>md(this._delegate,e)))}loadBundle(e){return t=this._delegate,e=e,r=sc(t=g(t,U)),n=new tc,Mh(r,t._databaseId,e,n),n;var t,r,n}namedQuery(e){return t=this._delegate,e=e,Ph(sc(t=g(t,U)),e).then(e=>e?new Gh(t,null,e.query):null).then(e=>e?new Ud(this,e):null);var t}}class Dd extends nd{constructor(e){super(),this.firestore=e}convertBytes(e){return new Sd(new hc(e))}convertReference(e){var t=this.convertDocumentKey(e,this.firestore._databaseId);return Od.forKey(t,this.firestore,null)}}class Nd{constructor(e,t){this._firestore=e,this._delegate=t,this._userDataWriter=new Dd(e)}get(e){let t=zd(e);return this._delegate.get(t).then(e=>new Md(this._firestore,new od(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,t.converter)))}set(e,t,r){var n=zd(e);return r?(Id("Transaction.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=zd(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=zd(e);return this._delegate.delete(t),this}}class kd{constructor(e){this._delegate=e}set(e,t,r){var n=zd(e);return r?(Id("WriteBatch.set",r),this._delegate.set(n,t,r)):this._delegate.set(n,t),this}update(e,t,r,...n){var i=zd(e);return 2===arguments.length?this._delegate.update(i,t):this._delegate.update(i,t,r,...n),this}delete(e){var t=zd(e);return this._delegate.delete(t),this}commit(){return this._delegate.commit()}}class Rd{constructor(e,t,r){this._firestore=e,this._userDataWriter=t,this._delegate=r}fromFirestore(e,t){var r=new ud(this._firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,null);return this._delegate.fromFirestore(new Pd(this._firestore,r),null!=t?t:{})}toFirestore(e,t){return t?this._delegate.toFirestore(e,t):this._delegate.toFirestore(e)}static getInstance(e,t){var r=Rd.INSTANCES;let n=r.get(e),i=(n||(n=new WeakMap,r.set(e,n)),n.get(t));return i||(i=new Rd(e,new Dd(e),t),n.set(t,i)),i}}Rd.INSTANCES=new WeakMap;class Od{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new Dd(e)}static forPath(e,t,r){if(e.length%2!=0)throw new I("invalid-argument","Invalid document reference. Document references must have an even number of segments, but "+e.canonicalString()+" has "+e.length);return new Od(t,new P(t._delegate,r,new x(e)))}static forKey(e,t,r){return new Od(t,new P(t._delegate,r,e))}get id(){return this._delegate.id}get parent(){return new jd(this.firestore,this._delegate.parent)}get path(){return this._delegate.path}collection(e){try{return new jd(this.firestore,Qh(this._delegate,e))}catch(e){throw Ld(e,"collection()","DocumentReference.collection()")}}isEqual(e){return(e=_(e))instanceof P&&Wh(this._delegate,e)}set(e,t){t=Id("DocumentReference.set",t);try{return t?dd(this._delegate,e,t):dd(this._delegate,e)}catch(e){throw Ld(e,"setDoc()","DocumentReference.set()")}}update(e,t,...r){try{return 1===arguments.length?fd(this._delegate,e):fd(this._delegate,e,t,...r)}catch(e){throw Ld(e,"updateDoc()","DocumentReference.update()")}}delete(){return md(g((e=this._delegate).firestore,U),[new hs(e._key,V.none())]);var e}onSnapshot(...e){var t=Fd(e),r=Vd(e,e=>new Md(this.firestore,new od(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)));return gd(this._delegate,t,r)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=g(t,P);let r=g(t.firestore,U),e=sc(r),n=new cd(r);return Rh(e,t._key).then(e=>new od(r,n,t._key,e,new ad(null!==e&&e.hasLocalMutations,!0),t.converter))}:"server"===(null==e?void 0:e.source)?t=>{t=g(t,P);let r=g(t.firestore,U);return Oh(sc(r),t._key,{source:"server"}).then(e=>pd(r,t,e))}:t=>{t=g(t,P);let r=g(t.firestore,U);return Oh(sc(r),t._key).then(e=>pd(r,t,e))})(this._delegate)).then(e=>new Md(this.firestore,new od(this.firestore._delegate,this._userDataWriter,e._key,e._document,e.metadata,this._delegate.converter)))}withConverter(e){return new Od(this.firestore,e?this._delegate.withConverter(Rd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function Ld(e,t,r){return e.message=e.message.replace(t,r),e}function Fd(e){for(var t of e)if("object"==typeof t&&!xd(t))return t;return{}}function Vd(e,t){let r,n,i;return i=xd(e[0])?e[0]:xd(e[1])?e[1]:"function"==typeof e[0]?{next:e[0],error:e[1],complete:e[2]}:{next:e[1],error:e[2],complete:e[3]},{next:e=>{i.next&&i.next(t(e))},error:null==(r=i.error)?void 0:r.bind(i),complete:null==(n=i.complete)?void 0:n.bind(i)}}class Md{constructor(e,t){this._firestore=e,this._delegate=t}get ref(){return new Od(this._firestore,this._delegate.ref)}get id(){return this._delegate.id}get metadata(){return this._delegate.metadata}get exists(){return this._delegate.exists()}data(e){return this._delegate.data(e)}get(e,t){return this._delegate.get(e,t)}isEqual(e){return hd(this._delegate,e._delegate)}}class Pd extends Md{data(e){var t=this._delegate.data(e);return this._delegate._converter||(e="Document in a QueryDocumentSnapshot should exist",void 0!==t)||E(57014,e),t}}class Ud{constructor(e,t){this.firestore=e,this._delegate=t,this._userDataWriter=new Dd(e)}where(e,t,r){try{return new Ud(this.firestore,Qc(this._delegate,(n=r,i=t,s=zc("where",e),Hc._create(s,i,n))))}catch(e){throw Ld(e,/(orderBy|where)\(\)/,"Query.$1()")}var n,i,s}orderBy(e,t){try{return new Ud(this.firestore,Qc(this._delegate,([r,n="asc"]=[e,t],i=n,s=zc("orderBy",r),Wc._create(s,i))))}catch(e){throw Ld(e,/(orderBy|where)\(\)/,"Query.$1()")}var r,n,i,s}limit(e){try{return new Ud(this.firestore,Qc(this._delegate,(Qe("limit",t=e),Xc._create("limit",t,"F"))))}catch(e){throw Ld(e,"limit()","Query.limit()")}var t}limitToLast(e){try{return new Ud(this.firestore,Qc(this._delegate,(Qe("limitToLast",t=e),Xc._create("limitToLast",t,"L"))))}catch(e){throw Ld(e,"limitToLast()","Query.limitToLast()")}var t}startAt(...e){try{return new Ud(this.firestore,Qc(this._delegate,([...t]=[...e],Yc._create("startAt",t,!0))))}catch(e){throw Ld(e,"startAt()","Query.startAt()")}var t}startAfter(...e){try{return new Ud(this.firestore,Qc(this._delegate,([...t]=[...e],Yc._create("startAfter",t,!1))))}catch(e){throw Ld(e,"startAfter()","Query.startAfter()")}var t}endBefore(...e){try{return new Ud(this.firestore,Qc(this._delegate,([...t]=[...e],Jc._create("endBefore",t,!1))))}catch(e){throw Ld(e,"endBefore()","Query.endBefore()")}var t}endAt(...e){try{return new Ud(this.firestore,Qc(this._delegate,([...t]=[...e],Jc._create("endAt",t,!0))))}catch(e){throw Ld(e,"endAt()","Query.endAt()")}var t}isEqual(e){return Xh(this._delegate,e._delegate)}get(e){let t;return(t=("cache"===(null==e?void 0:e.source)?t=>{t=g(t,Gh);let r=g(t.firestore,U),e=sc(r),n=new cd(r);return Lh(e,t._query).then(e=>new ld(r,n,t,e))}:"server"===(null==e?void 0:e.source)?t=>{t=g(t,Gh);let r=g(t.firestore,U),e=sc(r),n=new cd(r);return Fh(e,t._query,{source:"server"}).then(e=>new ld(r,n,t,e))}:t=>{t=g(t,Gh);let r=g(t.firestore,U),e=sc(r),n=new cd(r);return Kc(t._query),Fh(e,t._query).then(e=>new ld(r,n,t,e))})(this._delegate)).then(e=>new qd(this.firestore,new ld(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)))}onSnapshot(...e){var t=Fd(e),r=Vd(e,e=>new qd(this.firestore,new ld(this.firestore._delegate,this._userDataWriter,this._delegate,e._snapshot)));return gd(this._delegate,t,r)}withConverter(e){return new Ud(this.firestore,e?this._delegate.withConverter(Rd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}class Bd{constructor(e,t){this._firestore=e,this._delegate=t}get type(){return this._delegate.type}get doc(){return new Pd(this._firestore,this._delegate.doc)}get oldIndex(){return this._delegate.oldIndex}get newIndex(){return this._delegate.newIndex}}class qd{constructor(e,t){this._firestore=e,this._delegate=t}get query(){return new Ud(this._firestore,this._delegate.query)}get metadata(){return this._delegate.metadata}get size(){return this._delegate.size}get empty(){return this._delegate.empty}get docs(){return this._delegate.docs.map(e=>new Pd(this._firestore,e))}docChanges(e){return this._delegate.docChanges(e).map(e=>new Bd(this._firestore,e))}forEach(t,r){this._delegate.forEach(e=>{t.call(r,new Pd(this._firestore,e))})}isEqual(e){return hd(this._delegate,e._delegate)}}class jd extends Ud{constructor(e,t){super(e,t),this.firestore=e,this._delegate=t}get id(){return this._delegate.id}get path(){return this._delegate.path}get parent(){var e=this._delegate.parent;return e?new Od(this.firestore,e):null}doc(e){try{return void 0===e?new Od(this.firestore,Hh(this._delegate)):new Od(this.firestore,Hh(this._delegate,e))}catch(e){throw Ld(e,"doc()","CollectionReference.doc()")}}add(e){return((e,t)=>{let r=g(e.firestore,U),n=Hh(e),i=id(e.converter,t);return md(r,[Ic(bc(e.firestore),"addDoc",n._key,i,null!==e.converter,{}).toMutation(n._key,V.exists(!1))]).then(()=>n)})(this._delegate,e).then(e=>new Od(this.firestore,e))}isEqual(e){return Wh(this._delegate,e._delegate)}withConverter(e){return new jd(this.firestore,e?this._delegate.withConverter(Rd.getInstance(this.firestore,e)):this._delegate.withConverter(null))}}function zd(e){return g(e,P)}class Kd{static serverTimestamp(){var e=new Sc("serverTimestamp");return e._methodName="FieldValue.serverTimestamp",new Kd(e)}static delete(){var e=new Tc("deleteField");return e._methodName="FieldValue.delete",new Kd(e)}static arrayUnion(...e){[...e]=[...e];var t=new xc("arrayUnion",e);return t._methodName="FieldValue.arrayUnion",new Kd(t)}static arrayRemove(...e){[...e]=[...e];var t=new Cc("arrayRemove",e);return t._methodName="FieldValue.arrayRemove",new Kd(t)}static increment(e){e=e;var t=new Ac("increment",e);return t._methodName="FieldValue.increment",new Kd(t)}constructor(e){this._delegate=e}isEqual(e){return this._delegate.isEqual(e._delegate)}}let Gd={Firestore:Ad,GeoPoint:fc,Timestamp:v,Blob:Sd,Transaction:Nd,WriteBatch:kd,DocumentReference:Od,DocumentSnapshot:Md,Query:Ud,QueryDocumentSnapshot:Pd,QuerySnapshot:qd,CollectionReference:jd,FieldPath:class $d{constructor(...e){this._delegate=new cc(...e)}static documentId(){return new $d(h.keyField().canonicalString())}isEqual(e){return(e=_(e))instanceof cc&&this._delegate._internalPath.isEqual(e._internalPath)}},FieldValue:Kd,setLogLevel:function(e){e=e,we.setLogLevel(e)},CACHE_SIZE_UNLIMITED:-1};n=s.default,ic=(e,t)=>new Ad(e,t,new Cd),n.INTERNAL.registerComponent(new le("firestore-compat",e=>{var t=e.getProvider("app-compat").getImmediate(),r=e.getProvider("firestore").getImmediate();return ic(t,r)},"PUBLIC").setServiceProps(Object.assign({},Gd))),n.registerVersion("@firebase/firestore-compat","0.3.53")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-firestore-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-firestore-compat.js.map
