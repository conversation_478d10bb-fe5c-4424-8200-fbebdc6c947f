{"version": 3, "file": "firebase-functions-compat.js", "sources": ["../functions/src/config.ts", "../util/src/url.ts", "../util/src/emulator.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../functions/src/serializer.ts", "../functions/src/constants.ts", "../functions/src/error.ts", "../functions/src/context.ts", "../functions/src/service.ts", "../functions/src/api.ts", "../functions-compat/src/register.ts", "../functions-compat/src/service.ts", "../functions-compat/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(url: string): boolean {\n  // `isCloudWorkstation` is called without protocol in certain connect*Emulator functions\n  // In HTTP request builders, it's called with the protocol.\n  // If called with protocol prefix, it's a valid URL, so we extract the hostname\n  // If called without, we assume the string is the hostname.\n  try {\n    const host =\n      url.startsWith('http://') || url.startsWith('https://')\n        ? new URL(url).hostname\n        : url;\n    return host.endsWith('.cloudworkstations.dev');\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport { _isFirebaseServerApp, FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  private serverAppAppCheckToken: string | null = null;\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${\n    useSsl ? 's' : ''\n  }://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nconst DEFAULT_REGION = 'us-central1';\n\nconst factory: InstanceFactory<'functions-compat'> = (\n  container: ComponentContainer,\n  { instanceIdentifier: regionOrCustomDomain }: InstanceFactoryOptions\n) => {\n  // Dependencies\n  const app = container.getProvider('app-compat').getImmediate();\n  const functionsServiceExp = container.getProvider('functions').getImmediate({\n    identifier: regionOrCustomDomain ?? DEFAULT_REGION\n  });\n\n  return new FunctionsService(app, functionsServiceExp);\n};\n\nexport function registerFunctions(): void {\n  const namespaceExports = {\n    Functions: FunctionsService\n  };\n  (firebase as _FirebaseNamespace).INTERNAL.registerComponent(\n    new Component('functions-compat', factory, ComponentType.PUBLIC)\n      .setServiceProps(namespaceExports)\n      .setMultipleInstances(true)\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseFunctions, HttpsCallable } from '@firebase/functions-types';\nimport {\n  httpsCallable as httpsCallableExp,\n  httpsCallableFromURL as httpsCallableFromURLExp,\n  connectFunctionsEmulator as useFunctionsEmulatorExp,\n  HttpsCallableOptions,\n  Functions as FunctionsServiceExp\n} from '@firebase/functions';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport { FirebaseError } from '@firebase/util';\n\nexport class FunctionsService implements FirebaseFunctions, _FirebaseService {\n  /**\n   * For testing.\n   * @internal\n   */\n  _region: string;\n  /**\n   * For testing.\n   * @internal\n   */\n  _customDomain: string | null;\n\n  constructor(\n    public app: FirebaseApp,\n    readonly _delegate: FunctionsServiceExp\n  ) {\n    this._region = this._delegate.region;\n    this._customDomain = this._delegate.customDomain;\n  }\n  httpsCallable(name: string, options?: HttpsCallableOptions): HttpsCallable {\n    return httpsCallableExp(this._delegate, name, options);\n  }\n  httpsCallableFromURL(\n    url: string,\n    options?: HttpsCallableOptions\n  ): HttpsCallable {\n    return httpsCallableFromURLExp(this._delegate, url, options);\n  }\n  /**\n   * Deprecated in pre-modularized repo, does not exist in modularized\n   * functions package, need to convert to \"host\" and \"port\" args that\n   * `useFunctionsEmulatorExp` takes.\n   * @deprecated\n   */\n  useFunctionsEmulator(origin: string): void {\n    const match = origin.match('[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?');\n    if (match == null) {\n      throw new FirebaseError(\n        'functions',\n        'No origin provided to useFunctionsEmulator()'\n      );\n    }\n    if (match[2] == null) {\n      throw new FirebaseError(\n        'functions',\n        'Port missing in origin provided to useFunctionsEmulator()'\n      );\n    }\n    return useFunctionsEmulatorExp(this._delegate, match[1], Number(match[2]));\n  }\n  useEmulator(host: string, port: number): void {\n    return useFunctionsEmulatorExp(this._delegate, host, port);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase from '@firebase/app-compat';\nimport { name, version } from '../package.json';\nimport { registerFunctions } from './register';\nimport * as types from '@firebase/functions-types';\n\nregisterFunctions();\nfirebase.registerVersion(name, version);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    functions: {\n      (app?: FirebaseApp): types.FirebaseFunctions;\n      Functions: typeof types.FirebaseFunctions;\n    };\n  }\n  interface FirebaseApp {\n    functions(regionOrCustomDomain?: string): types.FirebaseFunctions;\n  }\n}\n"], "names": ["variant", "isCloudWorkstation", "url", "startsWith", "URL", "hostname", "endsWith", "_a", "emulatorStatus", "let", "previouslyDismissed", "updateEmulatorBanner", "name", "isRunningEmulator", "window", "document", "location", "host", "bannerId", "showError", "key", "summary", "prod", "emulator", "Object", "keys", "push", "length", "prefixedId", "id", "setupCloseBtn", "closeBtn", "createElement", "style", "cursor", "marginLeft", "fontSize", "innerHTML", "onclick", "element", "getElementById", "remove", "setupDom", "prependIcon", "bannerEl", "banner", "parentDiv", "created", "setAttribute", "firebaseTextId", "firebaseText", "learnMoreId", "learnMoreLink", "prependIconId", "createElementNS", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "innerText", "href", "paddingLeft", "textDecoration", "iconId", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LONG_TYPE", "UNSIGNED_LONG_TYPE", "mapValues", "o", "f", "result", "hasOwnProperty", "encode", "Number", "valueOf", "isFinite", "toString", "call", "Date", "toISOString", "Array", "isArray", "map", "x", "decode", "json", "isNaN", "FUNCTIONS_TYPE", "errorCodeMap", "OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "FunctionsError", "details", "_errorForResponse", "status", "bodyJSON", "description", "undefined", "errorJSON", "error", "e", "ContextProvider", "app", "authProvider", "messagingProvider", "appCheckProvider", "auth", "messaging", "appCheck", "serverAppAppCheckToken", "_isFirebaseServerApp", "settings", "appCheckToken", "getImmediate", "optional", "get", "then", "getAuthToken", "token", "await", "getToken", "accessToken", "getMessagingToken", "self", "Notification", "permission", "getAppCheckToken", "limitedUseAppCheckTokens", "getLimitedUseToken", "getContext", "authToken", "messagingToken", "DEFAULT_REGION", "responseLineRE", "FunctionsService", "regionOrCustomDomain", "fetchImpl", "args", "fetch", "emulator<PERSON><PERSON><PERSON>", "contextProvider", "cancelAllRequests", "Promise", "resolve", "deleteService", "customDomain", "origin", "pathname", "region", "_delete", "_url", "projectId", "options", "connectFunctionsEmulator", "functionsInstance", "port", "useSsl", "async", "endpoint", "credentials", "ok", "httpsCallable", "callable", "callAtURL", "stream", "streamAtURL", "makeAuthHeaders", "headers", "context", "failAfterHandle", "millis", "timer", "promise", "reject", "setTimeout", "cancel", "clearTimeout", "timeout", "response", "race", "method", "JSON", "stringify", "responseData", "signal", "Symbol", "asyncIterator", "next", "resultResolver", "resultRejecter", "resultPromise", "reader", "<PERSON><PERSON><PERSON><PERSON>", "rstream", "processLine", "line", "controller", "match", "jsonData", "parse", "enqueue", "decoder", "TextDecoder", "ReadableStream", "start", "currentText", "pump", "aborted", "done", "read", "trim", "close", "lines", "split", "pop", "functionsError", "rreader", "return", "_connectFunctionsEmulator", "httpsCallableFromURL", "_registerComponent", "container", "instanceIdentifier", "get<PERSON><PERSON><PERSON>", "registerVersion", "version", "namespaceExports", "_region", "_customDomain", "_httpsCallable", "httpsCallableFromURLExp", "useFunctionsEmulator", "useFunctionsEmulatorExp", "useEmulator", "factory", "functionsServiceExp", "identifier", "Functions", "firebase", "registerComponent"], "mappings": "uaAqCkCA,SChB5B,SAAUC,EAAmBC,GAKjC,IAKE,OAHEA,EAAIC,WAAW,SAAS,GAAKD,EAAIC,WAAW,UAAU,EAClD,IAAIC,IAAIF,CAAG,EAAEG,SACbH,GACMI,SAAS,wBAAwB,CAG9C,CAFC,MAAAC,GACA,MAAO,CAAA,CACR,CACH,CCgHA,IAAMC,EAAoC,GAkC1CC,IAAIC,EAAsB,CAAA,EAOV,SAAAC,EACdC,EACAC,GAEA,GACoB,aAAlB,OAAOC,QACa,aAApB,OAAOC,UACNd,EAAmBa,OAAOE,SAASC,IAAI,GACxCT,EAAeI,KAAUC,GACzBL,CAAAA,EAAeI,IACfF,CAAAA,EANF,CAWAF,EAAeI,GAAQC,EAKvB,IAAMK,EAAW,qBAEjB,IAAMC,EAAkC,GAvD1C,KACE,IAIWC,EAJLC,EAA2B,CAC/BC,KAAM,GACNC,SAAU,EACX,EACD,IAAWH,KAAOI,OAAOC,KAAKjB,CAAc,GACtCA,EAAeY,GACjBC,EAAQE,SAERF,EAAQC,MAFSI,KAAKN,CAAG,EAK7B,OAAOC,CACT,KA0C4BC,KAAKK,OAL/B,SAASC,EAAWC,GAClB,MAAO,uBAAuBA,CAC/B,CAgCD,SAASC,IACP,IAAMC,EAAWhB,SAASiB,cAAc,MAAM,EAS9C,OARAD,EAASE,MAAMC,OAAS,UACxBH,EAASE,MAAME,WAAa,OAC5BJ,EAASE,MAAMG,SAAW,OAC1BL,EAASM,UAAY,WACrBN,EAASO,QAAU,KAjCrB,IACQC,EAiCJ7B,EAAsB,CAAA,GAjClB6B,EAAUxB,SAASyB,eAAetB,CAAQ,IAE9CqB,EAAQE,OAAM,CAiChB,EACOV,CACR,CAeD,SAASW,IACP,IApCuBC,EAXEC,EA+CnBC,GAhGahB,IACrBpB,IAAIqC,EAAY/B,SAASyB,eAAeX,CAAE,EACtCkB,EAAU,CAAA,EAMd,OALKD,KACHA,EAAY/B,SAASiB,cAAc,KAAK,GAC9BgB,aAAa,KAAMnB,CAAE,EAC/BkB,EAAU,CAAA,GAEL,CAAEA,QAAAA,EAASR,QAASO,EAC7B,GAuFiC5B,CAAQ,EAC/B+B,EAAiBrB,EAAW,MAAM,EAClCsB,EACJnC,SAASyB,eAAeS,CAAc,GAAKlC,SAASiB,cAAc,MAAM,EACpEmB,EAAcvB,EAAW,WAAW,EACpCwB,EACHrC,SAASyB,eAAeW,CAAW,GACpCpC,SAASiB,cAAc,GAAG,EACtBqB,EAAgBzB,EAAW,cAAc,EACzCe,EACH5B,SAASyB,eACRa,CAAa,GAEftC,SAASuC,gBAAgB,6BAA8B,KAAK,EAC1DT,EAAOE,UAEHH,EAAWC,EAAON,SA/DDK,EAgELA,GA/DXX,MAAMsB,QAAU,OACzBX,EAASX,MAAMuB,WAAa,UAC5BZ,EAASX,MAAMwB,SAAW,QAC1Bb,EAASX,MAAMyB,OAAS,MACxBd,EAASX,MAAM0B,KAAO,MACtBf,EAASX,MAAM2B,QAAU,OACzBhB,EAASX,MAAM4B,aAAe,MAC9BjB,EAASX,MAAM6B,WAAa,UA0B5BV,EA+BkBA,GA5BJJ,aAAa,KA4BMG,CA5BW,EAC5CC,EAAcW,UAAY,aAC1BX,EAAcY,KACZ,uEACFZ,EAAcJ,aAAa,SAAU,SAAS,EAC9CI,EAAcnB,MAAMgC,YAAc,MAClCb,EAAcnB,MAAMiC,eAAiB,YAuB7BnC,EAAWD,IAvD6BqC,EAwDjBd,GAxDRV,EAwDLA,GAvDNK,aAAa,QAAS,IAAI,EACtCL,EAAYK,aAAa,KAAMmB,CAAM,EACrCxB,EAAYK,aAAa,SAAU,IAAI,EACvCL,EAAYK,aAAa,UAAW,WAAW,EAC/CL,EAAYK,aAAa,OAAQ,MAAM,EACvCL,EAAYV,MAAME,WAAa,OAmD7BS,EAASwB,OAAOzB,EAAaO,EAAcE,EAAerB,CAAQ,EAClEhB,SAASsD,KAAKC,YAAY1B,CAAQ,GAGhCzB,GACF+B,EAAaa,UAAY,gCACzBpB,EAAYN;;;;;;;WASZM,EAAYN;;;;;;;SAQZa,EAAaa,UAAY,8CAE3Bb,EAAaF,aAAa,KAAMC,CAAc,CAC/C,CAC2B,YAAxBlC,SAASwD,WACXzD,OAAO0D,iBAAiB,mBAAoB9B,CAAQ,EAEpDA,GApHD,CAsHH,OCtPa+B,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIpE,KAdI,gBA6BfY,OAAOyD,eAAeD,KAAMP,EAAcS,SAAS,EAI/CR,MAAMS,mBACRT,MAAMS,kBAAkBH,KAAMI,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXT,YACmBW,EACAC,EACAC,GAFAR,KAAOM,QAAPA,EACAN,KAAWO,YAAXA,EACAP,KAAMQ,OAANA,CACf,CAEJH,OACET,KACGa,GAEH,IAcuCA,EAdjCX,EAAcW,EAAK,IAAoB,GACvCC,EAAcV,KAAKM,QAAR,IAAmBV,EAC9Be,EAAWX,KAAKQ,OAAOZ,GAEvBC,EAAUc,GAUuBF,EAVcX,EAAVa,EAW7BC,QAAQC,EAAS,CAACC,EAAG1E,KACnC,IAAM2E,EAAQN,EAAKrE,GACnB,OAAgB,MAAT2E,EAAgBC,OAAOD,CAAK,MAAQ3E,KAC7C,CAAC,GAdoE,QAE7D6E,EAAiBjB,KAAKO,iBAAgBV,MAAYa,MAIxD,OAFc,IAAIjB,EAAciB,EAAUO,EAAanB,CAAU,CAGlE,CACF,CASD,IAAMe,EAAU,gBClHV,SAAUK,EACdZ,GAEA,OAAIA,GAAYA,EAA+Ba,UACrCb,EAA+Ba,UAEhCb,CAEX,OCDac,EAiBXzB,YACW/D,EACAyF,EACAC,GAFAtB,KAAIpE,KAAJA,EACAoE,KAAeqB,gBAAfA,EACArB,KAAIsB,KAAJA,EAnBXtB,KAAiBuB,kBAAG,CAAA,EAIpBvB,KAAYwB,aAAe,GAE3BxB,KAAAyB,kBAA2C,OAE3CzB,KAAiB0B,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA5B,KAAKyB,kBAAoBG,EAClB5B,IACR,CAED6B,qBAAqBN,GAEnB,OADAvB,KAAKuB,kBAAoBA,EAClBvB,IACR,CAED8B,gBAAgBC,GAEd,OADA/B,KAAKwB,aAAeO,EACb/B,IACR,CAEDgC,2BAA2BC,GAEzB,OADAjC,KAAK0B,kBAAoBO,EAClBjC,IACR,CACF,CCtDD,IAAMkC,EAAY,iDACZC,EAAqB,kDAE3B,SAASC,EAGPC,EACAC,GAEA,IACWlG,EADLmG,EAAqC,GAC3C,IAAWnG,KAAOiG,EACZA,EAAEG,eAAepG,CAAG,IACtBmG,EAAOnG,GAAOkG,EAAED,EAAEjG,EAAI,GAG1B,OAAOmG,CACT,CAQM,SAAUE,EAAOhC,GACrB,GAAY,MAARA,EACF,OAAO,KAKT,GAAoB,UAAhB,OAFFA,EADEA,aAAgBiC,OACXjC,EAAKkC,UAEHlC,IAAqBmC,SAASnC,CAAI,EAG3C,OAAOA,EAET,GAAa,CAAA,IAATA,GAA0B,CAAA,IAATA,EACnB,OAAOA,EAET,GAA6C,oBAAzCjE,OAAO0D,UAAU2C,SAASC,KAAKrC,CAAI,EACrC,OAAOA,EAET,GAAIA,aAAgBsC,KAClB,OAAOtC,EAAKuC,cAEd,GAAIC,MAAMC,QAAQzC,CAAI,EACpB,OAAOA,EAAK0C,IAAIC,GAAKX,EAAOW,CAAC,CAAC,EAEhC,GAAoB,YAAhB,OAAO3C,GAAuC,UAAhB,OAAOA,EACvC,OAAO2B,EAAU3B,EAAO2C,GAAKX,EAAOW,CAAC,CAAC,EAGxC,MAAM,IAAI1D,MAAM,mCAAqCe,CAAI,CAC3D,CAQM,SAAU4C,EAAOC,GACrB,GAAY,MAARA,EACF,OAAOA,EAET,GAAKA,EAAoC,SACvC,OAASA,EAAoC,UAC3C,KAAKpB,EAEL,KAAKC,EAIH,IAAMpB,EAAQ2B,OAAQY,EAA2C,KAAC,EAClE,GAAIC,MAAMxC,CAAK,EACb,MAAM,IAAIrB,MAAM,qCAAuC4D,CAAI,EAE7D,OAAOvC,EAET,QACE,MAAM,IAAIrB,MAAM,qCAAuC4D,CAAI,CAE9D,CAEH,OAAIL,MAAMC,QAAQI,CAAI,EACbA,EAAKH,IAAIC,GAAKC,EAAOD,CAAC,CAAC,EAEZ,YAAhB,OAAOE,GAAuC,UAAhB,OAAOA,EAChClB,EAAUkB,EAAOF,GAAKC,EAAOD,CAAC,CAAC,EAGjCE,CACT,CCxFO,IAAME,EAAiB,YCUxBC,EAAuD,CAC3DC,GAAI,KACJC,UAAW,YACXC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,UAAW,YACXC,eAAgB,iBAChBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,QAAS,UACTC,aAAc,eACdC,cAAe,gBACfC,SAAU,WACVC,YAAa,cACbC,UAAW,mBAUAC,UAAuBlF,EAIlCE,YAKEC,EACAC,EAIS+E,GAET7E,MAASyD,EAAH,IAAqB5D,EAAQC,GAAW,EAAE,EAFvCG,KAAO4E,QAAPA,EAMTpI,OAAOyD,eAAeD,KAAM2E,EAAezE,SAAS,CACrD,CACF,CAiDe,SAAA2E,EACdC,EACAC,GAEAtJ,IAAImE,GA3CqBkF,IAEzB,GAAc,KAAVA,GAAiBA,EAAS,IAC5B,MAAO,KAET,OAAQA,GACN,KAAK,EAEH,MAAO,WACT,KAAK,IACH,MAAO,mBACT,KAAK,IACH,MAAO,kBACT,KAAK,IACH,MAAO,oBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,UACT,KAAK,IACH,MAAO,qBACT,KAAK,IACH,MAAO,YACT,KAAK,IACH,MAAO,WACT,KAAK,IACH,MAAO,gBACT,KAAK,IACH,MAAO,cACT,KAAK,IACH,MAAO,mBAEV,CACD,MAAO,SACT,GAS+BA,CAAM,EAG/BE,EAAsBpF,EAEtBgF,EAAmBK,KAAAA,EAGvB,IACE,IAAMC,EAAYH,GAAYA,EAASI,MACvC,GAAID,EAAW,CACb,IAAMJ,EAASI,EAAUJ,OACzB,GAAsB,UAAlB,OAAOA,EAAqB,CAC9B,GAAI,CAACrB,EAAaqB,GAEhB,OAAO,IAAIH,EAAe,WAAY,UAAU,EAElD/E,EAAO6D,EAAaqB,GAIpBE,EAAcF,CACf,CAED,IAAMjF,EAAUqF,EAAUrF,QACH,UAAnB,OAAOA,IACTmF,EAAcnF,GAIAoF,KAAAA,KADhBL,EAAUM,EAAUN,WAElBA,EAAUvB,EAAOuB,CAAO,EAE3B,CAGF,CAFC,MAAOQ,IAIT,MAAa,OAATxF,EAIK,KAGF,IAAI+E,EAAe/E,EAAMoF,EAAaJ,CAAO,CACtD,OCpIaS,EAKX1F,YACW2F,EACTC,EACAC,EACAC,GAHSzF,KAAGsF,IAAHA,EALHtF,KAAI0F,KAAgC,KACpC1F,KAAS2F,UAA6B,KACtC3F,KAAQ4F,SAAoC,KAC5C5F,KAAsB6F,uBAAkB,KAO1CC,EAAoBA,qBAACR,CAAG,GAAKA,EAAIS,SAASC,gBAC5ChG,KAAK6F,uBAAyBP,EAAIS,SAASC,eAE7ChG,KAAK0F,KAAOH,EAAaU,aAAa,CAAEC,SAAU,CAAA,CAAI,CAAE,EACxDlG,KAAK2F,UAAYH,EAAkBS,aAAa,CAC9CC,SAAU,CAAA,CACX,CAAA,EAEIlG,KAAK0F,MACRH,EAAaY,IAAK,EAACC,KACjBV,GAAS1F,KAAK0F,KAAOA,EACrB,MAEC,EAIA1F,KAAK2F,WACRH,EAAkBW,IAAK,EAACC,KACtBT,GAAc3F,KAAK2F,UAAYA,EAC/B,MAEC,EAIA3F,KAAK4F,UACRH,MAAAA,GAAAA,EAAkBU,IAAG,EAAGC,KACtBR,GAAa5F,KAAK4F,SAAWA,EAC7B,MAEC,CAGN,CAEDS,qBACE,GAAKrG,KAAK0F,KAIV,IACE,IAAMY,EAAQC,MAAMvG,KAAK0F,KAAKc,SAAQ,EACtC,OAAOF,MAAAA,EAAA,KAAA,EAAAA,EAAOG,WAIf,CAHC,MAAOrB,IAIV,CAEDsB,0BACE,GACG1G,KAAK2F,WACJ,iBAAkBgB,MACQ,YAA5BC,aAAaC,WAKf,IACE,OAAON,MAAMvG,KAAK2F,UAAUa,UAO7B,CANC,MAAOpB,IAOV,CAED0B,uBACEC,GAEA,IAIQxE,EAJR,OAAIvC,KAAK6F,yBAGL7F,CAAAA,KAAK4F,WACDrD,EAASwE,EACXR,MAAMvG,KAAK4F,SAASoB,mBAAoB,EACxCT,MAAMvG,KAAK4F,SAASY,YACbrB,MAQN,KAFE5C,EAAO+D,MAGjB,CAEDW,iBAAiBF,GAIf,MAAO,CAAEG,UAHSX,MAAMvG,KAAKqG,eAGTc,eAFGZ,MAAMvG,KAAK0G,oBAEEV,cADdO,MAAMvG,KAAK8G,iBAAiBC,CAAwB,EAE3E,CACF,CCpHM,IAAMK,EAAiB,cAExBC,EAAiB,6BA0DVC,EAYX3H,YACW2F,EACTC,EACAC,EACAC,EACA8B,EAA+BH,EACtBI,EAA0B,IAAIC,IAASC,MAAM,GAAGD,CAAI,GALpDzH,KAAGsF,IAAHA,EAKAtF,KAASwH,UAATA,EAhBXxH,KAAc2H,eAAkB,KAkB9B3H,KAAK4H,gBAAkB,IAAIvC,EACzBC,EACAC,EACAC,EACAC,CAAgB,EAGlBzF,KAAK6H,kBAAoB,IAAIC,QAAQC,IACnC/H,KAAKgI,cAAgB,IACZF,QAAQC,QAAQA,EAAO,CAAE,CAEpC,CAAC,EAGD,IACE,IAAM7M,EAAM,IAAIE,IAAImM,CAAoB,EACxCvH,KAAKiI,aACH/M,EAAIgN,QAA2B,MAAjBhN,EAAIiN,SAAmB,GAAKjN,EAAIiN,UAChDnI,KAAKoI,OAAShB,CAIf,CAHC,MAAOhC,GACPpF,KAAKiI,aAAe,KACpBjI,KAAKoI,OAASb,CACf,CACF,CAEDc,UACE,OAAOrI,KAAKgI,eACb,CAODM,KAAK1M,GACH,IAAM2M,EAAYvI,KAAKsF,IAAIkD,QAAQD,UACnC,OAA4B,OAAxBvI,KAAK2H,kBACQ3H,KAAK2H,kBACAY,KAAavI,KAAKoI,UAAUxM,EAGxB,OAAtBoE,KAAKiI,aACGjI,KAAKiI,aAAR,IAAwBrM,aAGfoE,KAAKoI,UAAUG,wBAAgC3M,CAClE,CACF,CAWe6M,SAAAA,EACdC,EACAzM,EACA0M,GAEA,IAAMC,EAAS3N,EAAmBgB,CAAI,EACtCyM,EAAkBf,sBAChBiB,EAAS,IAAM,QACX3M,KAAQ0M,EAEVC,KThJCC,MAA0BC,IAChBvC,MAAMmB,MAAMoB,EAAU,CACnCC,YAAa,SACd,CAAA,GACaC,IS6IIN,EAAkBf,cAAc,EAChDhM,EAAqB,YAAa,CAAA,CAAI,EAE1C,CAOgBsN,SAAAA,EACdP,EACA9M,EACA4M,GAEA,IAAMU,EAAW,IAGf,OAwHFzI,EAxHuCA,EAyHvC+H,EAzH6CA,GAAW,GA2HlDtN,GALNwN,EAtHcA,GA2HgBJ,KA3HG1M,CA2HM,EAChCuN,EAAUT,EAAmBxN,EAAKuF,EAAM+H,CAAO,EAPxD,IACEE,EAGAF,EAEMtN,CA1HN,EASA,OAPAgO,EAASE,OAAS,CAChB3I,EACA+H,KAEA,OAmMF/H,EAnMyCA,EAoMzC+H,EApM+CA,EAsMzCtN,GALNwN,EAjMgBA,GAsMcJ,KAtMK1M,CAsMI,EAChCyN,EAAYX,EAAmBxN,EAAKuF,EAAM+H,GAAW,EAAE,EAPhE,IACEE,EAKMxN,CArMN,EAEOgO,CACT,CAiFAL,eAAeS,EACbZ,EACAF,GAEA,IAAMe,EAAkC,GAClCC,EAAUjD,MAAMmC,EAAkBd,gBAAgBX,WACtDuB,EAAQzB,wBAAwB,EAWlC,OATIyC,EAAQtC,YACVqC,EAAuB,cAAI,UAAYC,EAAQtC,WAE7CsC,EAAQrC,iBACVoC,EAAQ,8BAAgCC,EAAQrC,gBAEpB,OAA1BqC,EAAQxD,gBACVuD,EAAQ,uBAAyBC,EAAQxD,eAEpCuD,CACT,CAsBAV,eAAeM,EACbT,EACAxN,EACAuF,EACA+H,GAIA,IAAMnJ,EAAO,CAAEoB,KADfA,EAAOgC,EAAOhC,CAAI,GAIZ8I,EAAUhD,MAAM+C,EAAgBZ,EAAmBF,CAAO,EAK1DiB,GAtRWC,IAIjBjO,IAAIkO,EAAoB,KACxB,MAAO,CACLC,QAAS,IAAI9B,QAAQ,CAAChH,EAAG+I,KACvBF,EAAQG,WAAW,KACjBD,EAAO,IAAIlF,EAAe,oBAAqB,mBAAmB,CAAC,CACpE,EAAE+E,CAAM,CACX,CAAC,EACDK,OAAQ,KACFJ,GACFK,aAAaL,CAAK,CAErB,EAEL,GAmQkBnB,EAAQyB,SAAW,GAEM,EACnCC,EAAW3D,MAAMuB,QAAQqC,KAAK,EApGtCtB,MACE3N,EACAmE,EACAkK,EACA/B,KAEA+B,EAAQ,gBAAkB,mBAE1B9N,IAAIyO,EACJ,IACEA,EAAW3D,MAAMiB,EAAUtM,EAAK,CAC9BkP,OAAQ,OACR/K,KAAMgL,KAAKC,UAAUjL,CAAI,EACzBkK,QAAAA,CACD,CAAA,CAUF,CATC,MAAOnE,GAKP,MAAO,CACLN,OAAQ,EACRxB,KAAM,KAET,CACD7H,IAAI6H,EAAgC,KACpC,IACEA,EAAOiD,MAAM2D,EAAS5G,MAGvB,CAFC,MAAO8B,IAGT,MAAO,CACLN,OAAQoF,EAASpF,OACjBxB,KAAAA,EAEJ,GAkEapI,EAAKmE,EAAMkK,EAASb,EAAkBlB,SAAS,EACxDiC,EAAgBG,QAChBlB,EAAkBb,kBACnB,EAMD,GAHA4B,EAAgBM,OAAM,EAGlB,CAACG,EACH,MAAM,IAAIvF,EACR,YACA,0CAA0C,EAKxCQ,EAAQN,EAAkBqF,EAASpF,OAAQoF,EAAS5G,IAAI,EAC9D,GAAI6B,EACF,MAAMA,EAGR,GAAI,CAAC+E,EAAS5G,KACZ,MAAM,IAAIqB,EAAe,WAAY,oCAAoC,EAG3ElJ,IAAI8O,EAAeL,EAAS5G,KAAK7C,KAMjC,GAA4B,KAAA,KAF1B8J,EAD0B,KAAA,IAAjBA,EACML,EAAS5G,KAAKf,OAEpBgI,GAET,MAAM,IAAI5F,EAAe,WAAY,iCAAiC,EAMxE,MAAO,CAAElE,KAFW4C,EAAOkH,CAAY,EAGzC,CAwBA1B,eAAeQ,EACbX,EACAxN,EACAuF,EACA+H,OAIMnJ,EAAO,CAAEoB,KADfA,EAAOgC,EAAOhC,CAAI,GAIZ8I,EAAUhD,MAAM+C,EAAgBZ,EAAmBF,CAAO,EAChEe,EAAQ,gBAAkB,mBAC1BA,EAAgB,OAAI,oBAEpB9N,IAAIyO,EACJ,IACEA,EAAW3D,MAAMmC,EAAkBlB,UAAUtM,EAAK,CAChDkP,OAAQ,OACR/K,KAAMgL,KAAKC,UAAUjL,CAAI,EACzBkK,QAAAA,EACAiB,OAAQhC,MAAAA,EAAA,KAAA,EAAAA,EAASgC,MAClB,CAAA,CAmCF,CAlCC,MAAOpF,GACP,GAAIA,aAAa1F,OAAoB,eAAX0F,EAAExJ,KAAuB,CACjD,IAAMuJ,EAAQ,IAAIR,EAAe,YAAa,wBAAwB,EACtE,MAAO,CACLlE,KAAMqH,QAAQ+B,OAAO1E,CAAK,EAC1BiE,OAAQ,EACLqB,OAAOC,iBACN,MAAO,CACLC,OACE,OAAO7C,QAAQ+B,OAAO1E,CAAK,CAC5B,EAEJ,CACF,EAEJ,CAKD,IAAMA,EAAQN,EAAkB,EAAG,IAAI,EACvC,MAAO,CACLpE,KAAMqH,QAAQ+B,OAAO1E,CAAK,EAE1BiE,OAAQ,EACLqB,OAAOC,iBACN,MAAO,CACLC,OACE,OAAO7C,QAAQ+B,OAAO1E,CAAK,CAC5B,EAEJ,CACF,EAEJ,CACD1J,IAAImP,EACAC,EACEC,EAAgB,IAAIhD,QAAiB,CAACC,EAAS8B,KACnDe,EAAiB7C,EACjB8C,EAAiBhB,CACnB,CAAC,EACD,OAAAtO,EAAAiN,MAAAA,EAAO,KAAA,EAAPA,EAASgC,SAAMjP,EAAEiE,iBAAiB,QAAS,KACzC,IAAM2F,EAAQ,IAAIR,EAAe,YAAa,wBAAwB,EACtEkG,EAAe1F,CAAK,CACtB,CAAC,EACK4F,EAASb,EAAS7K,KAAM2L,UAAS,EACvC,IAAMC,GAyCR,CACEF,EACAH,EACAC,EACAL,KAEA,IAAMU,EAAc,CAClBC,EACAC,KAEA,IAAMC,EAAQF,EAAKE,MAAMhE,CAAc,EAEvC,GAAKgE,EAAL,CAGM5K,EAAO4K,EAAM,GACnB,IACE,IAUQlG,EAVFmG,EAAWjB,KAAKkB,MAAM9K,CAAI,EAC5B,WAAY6K,EACdV,EAAevH,EAAOiI,EAAS/I,MAAM,CAAC,EAGpC,YAAa+I,EACfF,EAAWI,QAAQnI,EAAOiI,EAASzL,OAAO,CAAC,EAGzC,UAAWyL,IACPnG,EAAQN,EAAkB,EAAGyG,CAAQ,EAC3CF,EAAWjG,MAAMA,CAAK,EACtB0F,EAAe1F,CAAK,EAUvB,CAPC,MAAOA,GACHA,aAAiBR,IACnByG,EAAWjG,MAAMA,CAAK,EACtB0F,EAAe1F,CAAK,EAIvB,CAzBA,CA0BH,EAEMsG,EAAU,IAAIC,YACpB,OAAO,IAAIC,eAAe,CACxBC,MAAMR,GACJ3P,IAAIoQ,EAAc,GAClB,OACAhD,eAAeiD,IACb,GAAItB,MAAAA,GAAAA,EAAQuB,QAAS,CACnB,IAAM5G,EAAQ,IAAIR,EAChB,YACA,uBAAuB,EAIzB,OAFAyG,EAAWjG,MAAMA,CAAK,EACtB0F,EAAe1F,CAAK,EACb2C,QAAQC,SAChB,CACD,IACE,GAAM,CAAEhH,MAAAA,EAAOiL,KAAAA,CAAI,EAAKzF,MAAMwE,EAAOkB,KAAI,EACzC,GAAID,EACEH,EAAYK,QACdhB,EAAYW,EAAYK,KAAM,EAAEd,CAAU,EAE5CA,EAAWe,MAAK,MAJlB,CAOA,GAAI3B,MAAAA,GAAAA,CAAAA,EAAQuB,QAAZ,CAWA,IAAMK,GADNP,GAAeJ,EAAQpI,OAAOtC,EAAO,CAAEqI,OAAQ,CAAA,CAAI,CAAE,GAC3BiD,MAAM,IAAI,EACpCR,EAAcO,EAAME,IAAK,GAAI,GAC7B,IAAK,IAAMnB,KAAQiB,EACbjB,EAAKe,QACPhB,EAAYC,EAAKe,KAAM,EAAEd,CAAU,EAGvC,OAAOU,EAAI,CATV,CAToB,CACnB,IAAM3G,EAAQ,IAAIR,EAChB,YACA,uBAAuB,EAEzByG,EAAWjG,MAAMA,CAAK,EACtB0F,EAAe1F,CAAK,EADpBiG,KAEA7E,MAAMwE,EAAOhB,QAEd,CAVA,CA2BF,CAPC,MAAO5E,GACP,IAAMoH,EACJpH,aAAiBR,EACbQ,EACAN,EAAkB,EAAG,IAAI,EAC/BuG,EAAWjG,MAAMoH,CAAc,EAC/B1B,EAAe0B,CAAc,CAC9B,CACF,EA/CU,CAgDZ,EACDxC,SACE,OAAOgB,EAAOhB,QACf,CACF,CAAA,CACH,GA3IIgB,EACAH,EACAC,EACArC,MAAAA,EAAO,KAAA,EAAPA,EAASgC,MAAM,EAEjB,MAAO,CACLpB,OAAQ,EACLqB,OAAOC,iBACN,IAAM8B,EAAUvB,EAAQD,YACxB,MAAO,CACLL,aACE,GAAM,CAAE5J,MAAAA,EAAOiL,KAAAA,CAAI,EAAKzF,MAAMiG,EAAQP,KAAI,EAC1C,MAAO,CAAElL,MAAOA,EAAkBiL,KAAAA,EACnC,EACDS,eAEE,OADAlG,MAAMiG,EAAQzC,SACP,CAAEiC,KAAM,CAAA,EAAMjL,MAAOkE,KAAAA,CAAS,CACtC,EAEJ,CACF,EACDxE,KAAMqK,EAEV,wCCxbgB,SAAArC,EACdC,EACAzM,EACA0M,GAEA+D,EACExL,EAAqCwH,CAAqC,EAC1EzM,EACA0M,CAAI,CAER,CA4BgB,SAAAgE,EAKdjE,EACAxN,EACAsN,GAEA,OD4GAE,EC3GExH,EAAqCwH,CAAqC,ED4G5ExN,EC3GEA,ED4GFsN,EC3GEA,GD6GIU,EAAW,GAGRC,EAAUT,EAAmBxN,EAAKuF,EAAM+H,GAAW,EAAE,GAGrDY,OAAS,CAChB3I,EACA+H,IAEOa,EAAYX,EAAmBxN,EAAKuF,EAAM+H,GAAW,EAAE,EAEzDU,EArBOyD,IAKdjE,EACAxN,EACAsN,EAEMU,CC3GR,CXnEE0D,qBACE,IAAIxL,EACFoC,EAtB0C,CAC5CqJ,EACA,CAAEC,mBAAoBvF,CAAoB,KAG1C,IAAMjC,EAAMuH,EAAUE,YAAY,KAAK,EAAE9G,aAAY,EAC/CV,EAAesH,EAAUE,YAbkB,eAaY,EACvDvH,EAAoBqH,EAAUE,YAVtC,oBAUyE,EACjEtH,EAAmBoH,EAAUE,YAbrC,oBAawE,EAGtE,OAAO,IAAIzF,EACThC,EACAC,EACAC,EACAC,EACA8B,CAAoB,CAExB,EAOG,UAAC1F,qBAAqB,CAAA,CAAI,CAAC,EAG9BmL,EAAAA,gBAAgBpR,EAAMqR,EAASjS,CAAO,EAEtCgS,EAAAA,gBAAgBpR,EAAMqR,EAAS,SAAkB,MYzB3CC,QCfK5F,EAYX3H,YACS2F,EACEnE,GADFnB,KAAGsF,IAAHA,EACEtF,KAASmB,UAATA,EAETnB,KAAKmN,QAAUnN,KAAKmB,UAAUiH,OAC9BpI,KAAKoN,cAAgBpN,KAAKmB,UAAU8G,YACrC,CACDgB,cAAcrN,EAAc4M,GAC1B,OFmDK6E,EACLnM,EEpDwBlB,KAAKmB,SFoD6C,EEpDlCvF,EAAM4M,CFsDvC,CErDR,CACDmE,qBACEzR,EACAsN,GAEA,OAAO8E,EAAwBtN,KAAKmB,UAAWjG,EAAKsN,CAAO,CAC5D,CAOD+E,qBAAqBrF,GACnB,IAAMmD,EAAQnD,EAAOmD,MAAM,4CAA4C,EACvE,GAAa,MAATA,EACF,MAAM,IAAI5L,EACR,YACA,8CAA8C,EAGlD,GAAgB,MAAZ4L,EAAM,GACR,MAAM,IAAI5L,EACR,YACA,2DAA2D,EAG/D,OAAO+N,EAAwBxN,KAAKmB,UAAWkK,EAAM,GAAI3I,OAAO2I,EAAM,EAAE,CAAC,CAC1E,CACDoC,YAAYxR,EAAc0M,GACxB,OAAO6E,EAAwBxN,KAAKmB,UAAWlF,EAAM0M,CAAI,CAC1D,CACF,CDtDD,IAAMvB,EAAiB,cAEjBsG,EAA+C,CACnDb,EACA,CAAEC,mBAAoBvF,CAAoB,KAG1C,IAAMjC,EAAMuH,EAAUE,YAAY,YAAY,EAAE9G,aAAY,EACtD0H,EAAsBd,EAAUE,YAAY,WAAW,EAAE9G,aAAa,CAC1E2H,WAAYrG,MAAAA,EAAAA,EAAwBH,CACrC,CAAA,EAED,OAAO,IAAIE,EAAiBhC,EAAKqI,CAAmB,CACtD,EAGQT,EAAmB,CACvBW,UAAWvG,GAEZwG,EAA+B,QAACtJ,SAASuJ,kBACxC,IAAI3M,EAAU,mBAAoBsM,EAA8B,QAAA,EAC7D5L,gBAAgBoL,CAAgB,EAChCrL,qBAAqB,CAAA,CAAI,CAAC,EE1BjCiM,EAAAA,QAASd,qDAA6B"}