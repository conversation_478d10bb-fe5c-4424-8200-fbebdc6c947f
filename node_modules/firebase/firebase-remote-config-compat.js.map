{"version": 3, "file": "firebase-remote-config-compat.js", "sources": ["../logger/src/logger.ts", "../../node_modules/idb/build/index.js", "../util/src/environment.ts", "../util/src/errors.ts", "../util/src/compat.ts", "../component/src/component.ts", "../../node_modules/idb/build/wrap-idb-value.js", "../installations/src/util/constants.ts", "../remote-config-compat/src/index.ts", "../installations/src/util/errors.ts", "../installations/src/functions/common.ts", "../installations/src/util/sleep.ts", "../installations/src/helpers/generate-fid.ts", "../installations/src/helpers/buffer-to-base64-url-safe.ts", "../installations/src/util/get-key.ts", "../installations/src/helpers/fid-changed.ts", "../installations/src/helpers/idb-manager.ts", "../installations/src/helpers/get-installation-entry.ts", "../installations/src/functions/create-installation-request.ts", "../installations/src/functions/generate-auth-token-request.ts", "../installations/src/helpers/refresh-auth-token.ts", "../installations/src/api/get-token.ts", "../installations/src/helpers/extract-app-config.ts", "../installations/src/functions/config.ts", "../installations/src/api/get-id.ts", "../installations/src/index.ts", "../remote-config/src/client/remote_config_fetch_client.ts", "../remote-config/src/constants.ts", "../remote-config/src/errors.ts", "../remote-config/src/value.ts", "../remote-config/src/api.ts", "../remote-config/src/client/caching_client.ts", "../remote-config/src/client/rest_client.ts", "../remote-config/src/language.ts", "../remote-config/src/client/retrying_client.ts", "../util/src/exponential_backoff.ts", "../remote-config/src/remote_config.ts", "../remote-config/src/storage/storage.ts", "../remote-config/src/storage/storage_cache.ts", "../remote-config/src/api2.ts", "../remote-config/src/register.ts", "../remote-config-compat/src/remoteConfig.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport firebase, { _FirebaseNamespace } from '@firebase/app-compat';\nimport {\n  Component,\n  ComponentContainer,\n  ComponentType,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { RemoteConfigCompatImpl, isSupported } from './remoteConfig';\nimport { name as packageName, version } from '../package.json';\nimport { RemoteConfig as RemoteConfigCompat } from '@firebase/remote-config-types';\n\nfunction registerRemoteConfigCompat(\n  firebaseInstance: _FirebaseNamespace\n): void {\n  firebaseInstance.INTERNAL.registerComponent(\n    new Component(\n      'remoteConfig-compat',\n      remoteConfigFactory,\n      ComponentType.PUBLIC\n    )\n      .setMultipleInstances(true)\n      .setServiceProps({ isSupported })\n  );\n\n  firebaseInstance.registerVersion(packageName, version);\n}\n\nfunction remoteConfigFactory(\n  container: ComponentContainer,\n  { instanceIdentifier: namespace }: InstanceFactoryOptions\n): RemoteConfigCompatImpl {\n  const app = container.getProvider('app-compat').getImmediate();\n  // The following call will always succeed because rc `import {...} from '@firebase/remote-config'`\n  const remoteConfig = container.getProvider('remote-config').getImmediate({\n    identifier: namespace\n  });\n\n  return new RemoteConfigCompatImpl(app, remoteConfig);\n}\n\nregisterRemoteConfigCompat(firebase as _FirebaseNamespace);\n\ndeclare module '@firebase/app-compat' {\n  interface FirebaseNamespace {\n    remoteConfig: {\n      (app?: FirebaseApp): RemoteConfigCompat;\n    };\n  }\n  interface FirebaseApp {\n    remoteConfig(): RemoteConfigCompat;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CustomSignals, FetchResponse } from '../public_types';\n\n/**\n * Defines a client, as in https://en.wikipedia.org/wiki/Client%E2%80%93server_model, for the\n * Remote Config server (https://firebase.google.com/docs/reference/remote-config/rest).\n *\n * <p>Abstracts throttle, response cache and network implementation details.\n *\n * <p>Modeled after the native {@link GlobalFetch} interface, which is relatively modern and\n * convenient, but simplified for Remote Config's use case.\n *\n * Disambiguation: {@link GlobalFetch} interface and the Remote Config service define \"fetch\"\n * methods. The RestClient uses the former to make HTTP calls. This interface abstracts the latter.\n */\nexport interface RemoteConfigFetchClient {\n  /**\n   * @throws if response status is not 200 or 304.\n   */\n  fetch(request: FetchRequest): Promise<FetchResponse>;\n}\n\n/**\n * Shims a minimal AbortSignal.\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class RemoteConfigAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\n * Defines per-request inputs for the Remote Config fetch request.\n *\n * <p>Modeled after the native {@link Request} interface, but simplified for Remote Config's\n * use case.\n */\nexport interface FetchRequest {\n  /**\n   * Uses cached config if it is younger than this age.\n   *\n   * <p>Required because it's defined by settings, which always have a value.\n   *\n   * <p>Comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the native\n   * Fetch API.\n   */\n  cacheMaxAgeMillis: number;\n\n  /**\n   * An event bus for the signal to abort a request.\n   *\n   * <p>Required because all requests should be abortable.\n   *\n   * <p>Comparable to the native\n   * Fetch API's \"signal\" field on its request configuration object\n   * https://fetch.spec.whatwg.org/#dom-requestinit-signal.\n   *\n   * <p>Disambiguation: Remote Config commonly refers to API inputs as\n   * \"signals\". See the private ConfigFetchRequestBody interface for those:\n   * http://google3/firebase/remote_config/web/src/core/rest_client.ts?l=14&rcl=255515243.\n   */\n  signal: RemoteConfigAbortSignal;\n\n  /**\n   * The ETag header value from the last response.\n   *\n   * <p>Optional in case this is the first request.\n   *\n   * <p>Comparable to passing `headers = { 'If-None-Match': <eTag> }` to the native Fetch API.\n   */\n  eTag?: string;\n\n  /** The custom signals stored for the app instance.\n   *\n   * <p>Optional in case no custom signals are set for the instance.\n   */\n  customSignals?: CustomSignals;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const RC_COMPONENT_NAME = 'remote-config';\nexport const RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS = 100;\nexport const RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH = 250;\nexport const RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH = 500;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\n\nexport const enum ErrorCode {\n  ALREADY_INITIALIZED = 'already-initialized',\n  REGISTRATION_WINDOW = 'registration-window',\n  REGISTRATION_PROJECT_ID = 'registration-project-id',\n  REGISTRATION_API_KEY = 'registration-api-key',\n  REGISTRATION_APP_ID = 'registration-app-id',\n  STORAGE_OPEN = 'storage-open',\n  STORAGE_GET = 'storage-get',\n  STORAGE_SET = 'storage-set',\n  STORAGE_DELETE = 'storage-delete',\n  FETCH_NETWORK = 'fetch-client-network',\n  FETCH_TIMEOUT = 'fetch-timeout',\n  FETCH_THROTTLE = 'fetch-throttle',\n  FETCH_PARSE = 'fetch-client-parse',\n  FETCH_STATUS = 'fetch-status',\n  INDEXED_DB_UNAVAILABLE = 'indexed-db-unavailable',\n  CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS = 'custom-signal-max-allowed-signals'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.ALREADY_INITIALIZED]: 'Remote Config already initialized',\n  [ErrorCode.REGISTRATION_WINDOW]:\n    'Undefined window object. This SDK only supports usage in a browser environment.',\n  [ErrorCode.REGISTRATION_PROJECT_ID]:\n    'Undefined project identifier. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_API_KEY]:\n    'Undefined API key. Check Firebase app initialization.',\n  [ErrorCode.REGISTRATION_APP_ID]:\n    'Undefined app identifier. Check Firebase app initialization.',\n  [ErrorCode.STORAGE_OPEN]:\n    'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_GET]:\n    'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_SET]:\n    'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.STORAGE_DELETE]:\n    'Error thrown when deleting from storage. Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_NETWORK]:\n    'Fetch client failed to connect to a network. Check Internet connection.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_TIMEOUT]:\n    'The config fetch request timed out. ' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.',\n  [ErrorCode.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Configure timeout using \"fetchTimeoutMillis\" SDK setting.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [ErrorCode.FETCH_PARSE]:\n    'Fetch client could not parse response.' +\n    ' Original error: {$originalErrorMessage}.',\n  [ErrorCode.FETCH_STATUS]:\n    'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [ErrorCode.INDEXED_DB_UNAVAILABLE]:\n    'Indexed DB is not supported by current browser',\n  [ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS]:\n    'Setting more than {$maxSignals} custom signals is not supported.'\n};\n\n// Note this is effectively a type system binding a code to params. This approach overlaps with the\n// role of TS interfaces, but works well for a few reasons:\n// 1) JS is unaware of TS interfaces, eg we can't test for interface implementation in JS\n// 2) callers should have access to a human-readable summary of the error and this interpolates\n//    params into an error message;\n// 3) callers should be able to programmatically access data associated with an error, which\n//    ErrorData provides.\ninterface ErrorParams {\n  [ErrorCode.STORAGE_OPEN]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_GET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_SET]: { originalErrorMessage: string | undefined };\n  [ErrorCode.STORAGE_DELETE]: { originalErrorMessage: string | undefined };\n  [ErrorCode.FETCH_NETWORK]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [ErrorCode.FETCH_PARSE]: { originalErrorMessage: string };\n  [ErrorCode.FETCH_STATUS]: { httpStatus: number };\n  [ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS]: { maxSignals: number };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  'remoteconfig' /* service */,\n  'Remote Config' /* service name */,\n  ERROR_DESCRIPTION_MAP\n);\n\n// Note how this is like typeof/instanceof, but for ErrorCode.\nexport function hasErrorCode(e: Error, errorCode: ErrorCode): boolean {\n  return e instanceof FirebaseError && e.code.indexOf(errorCode) !== -1;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Value as ValueType, ValueSource } from '@firebase/remote-config-types';\n\nconst DEFAULT_VALUE_FOR_BOOLEAN = false;\nconst DEFAULT_VALUE_FOR_STRING = '';\nconst DEFAULT_VALUE_FOR_NUMBER = 0;\n\nconst BOOLEAN_TRUTHY_VALUES = ['1', 'true', 't', 'yes', 'y', 'on'];\n\nexport class Value implements ValueType {\n  constructor(\n    private readonly _source: ValueSource,\n    private readonly _value: string = DEFAULT_VALUE_FOR_STRING\n  ) {}\n\n  asString(): string {\n    return this._value;\n  }\n\n  asBoolean(): boolean {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_BOOLEAN;\n    }\n    return BOOLEAN_TRUTHY_VALUES.indexOf(this._value.toLowerCase()) >= 0;\n  }\n\n  asNumber(): number {\n    if (this._source === 'static') {\n      return DEFAULT_VALUE_FOR_NUMBER;\n    }\n    let num = Number(this._value);\n    if (isNaN(num)) {\n      num = DEFAULT_VALUE_FOR_NUMBER;\n    }\n    return num;\n  }\n\n  getSource(): ValueSource {\n    return this._source;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { deepEqual, getModularInstance } from '@firebase/util';\nimport {\n  CustomSignals,\n  LogLevel as RemoteConfigLogLevel,\n  RemoteConfig,\n  Value,\n  RemoteConfigOptions\n} from './public_types';\nimport { RemoteConfigAbortSignal } from './client/remote_config_fetch_client';\nimport {\n  RC_COMPONENT_NAME,\n  RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH,\n  RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH\n} from './constants';\nimport { ERROR_FACTORY, ErrorCode, hasErrorCode } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { Value as ValueImpl } from './value';\nimport { LogLevel as FirebaseLogLevel } from '@firebase/logger';\n\n/**\n *\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n * @param options - Optional. The {@link RemoteConfigOptions} with which to instantiate the\n *     Remote Config instance.\n * @returns A {@link RemoteConfig} instance.\n *\n * @public\n */\nexport function getRemoteConfig(\n  app: FirebaseApp = getApp(),\n  options: RemoteConfigOptions = {}\n): RemoteConfig {\n  app = getModularInstance(app);\n  const rcProvider = _getProvider(app, RC_COMPONENT_NAME);\n  if (rcProvider.isInitialized()) {\n    const initialOptions = rcProvider.getOptions() as RemoteConfigOptions;\n    if (deepEqual(initialOptions, options)) {\n      return rcProvider.getImmediate();\n    }\n    throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n  }\n  rcProvider.initialize({ options });\n  const rc = rcProvider.getImmediate() as RemoteConfigImpl;\n\n  if (options.initialFetchResponse) {\n    // We use these initial writes as the initialization promise since they will hydrate the same\n    // fields that `storageCache.loadFromStorage` would set.\n    rc._initializePromise = Promise.all([\n      rc._storage.setLastSuccessfulFetchResponse(options.initialFetchResponse),\n      rc._storage.setActiveConfigEtag(options.initialFetchResponse?.eTag || ''),\n      rc._storageCache.setLastSuccessfulFetchTimestampMillis(Date.now()),\n      rc._storageCache.setLastFetchStatus('success'),\n      rc._storageCache.setActiveConfig(\n        options.initialFetchResponse?.config || {}\n      )\n    ]).then();\n    // The `storageCache` methods above set their in-memory fields synchronously, so it's\n    // safe to declare our initialization complete at this point.\n    rc._isInitializationComplete = true;\n  }\n\n  return rc;\n}\n\n/**\n * Makes the last fetched config available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function activate(remoteConfig: RemoteConfig): Promise<boolean> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  const [lastSuccessfulFetchResponse, activeConfigEtag] = await Promise.all([\n    rc._storage.getLastSuccessfulFetchResponse(),\n    rc._storage.getActiveConfigEtag()\n  ]);\n  if (\n    !lastSuccessfulFetchResponse ||\n    !lastSuccessfulFetchResponse.config ||\n    !lastSuccessfulFetchResponse.eTag ||\n    lastSuccessfulFetchResponse.eTag === activeConfigEtag\n  ) {\n    // Either there is no successful fetched config, or is the same as current active\n    // config.\n    return false;\n  }\n  await Promise.all([\n    rc._storageCache.setActiveConfig(lastSuccessfulFetchResponse.config),\n    rc._storage.setActiveConfigEtag(lastSuccessfulFetchResponse.eTag)\n  ]);\n  return true;\n}\n\n/**\n * Ensures the last activated config are available to the getters.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` that resolves when the last activated config is available to the getters.\n * @public\n */\nexport function ensureInitialized(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._initializePromise) {\n    rc._initializePromise = rc._storageCache.loadFromStorage().then(() => {\n      rc._isInitializationComplete = true;\n    });\n  }\n  return rc._initializePromise;\n}\n\n/**\n * Fetches and caches configuration from the Remote Config service.\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @public\n */\nexport async function fetchConfig(remoteConfig: RemoteConfig): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  // Aborts the request after the given timeout, causing the fetch call to\n  // reject with an `AbortError`.\n  //\n  // <p>Aborting after the request completes is a no-op, so we don't need a\n  // corresponding `clearTimeout`.\n  //\n  // Locating abort logic here because:\n  // * it uses a developer setting (timeout)\n  // * it applies to all retries (like curl's max-time arg)\n  // * it is consistent with the Fetch API's signal input\n  const abortSignal = new RemoteConfigAbortSignal();\n\n  setTimeout(async () => {\n    // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n    abortSignal.abort();\n  }, rc.settings.fetchTimeoutMillis);\n\n  const customSignals = rc._storageCache.getCustomSignals();\n  if (customSignals) {\n    rc._logger.debug(\n      `Fetching config with custom signals: ${JSON.stringify(customSignals)}`\n    );\n  }\n  // Catches *all* errors thrown by client so status can be set consistently.\n  try {\n    await rc._client.fetch({\n      cacheMaxAgeMillis: rc.settings.minimumFetchIntervalMillis,\n      signal: abortSignal,\n      customSignals\n    });\n\n    await rc._storageCache.setLastFetchStatus('success');\n  } catch (e) {\n    const lastFetchStatus = hasErrorCode(e as Error, ErrorCode.FETCH_THROTTLE)\n      ? 'throttle'\n      : 'failure';\n    await rc._storageCache.setLastFetchStatus(lastFetchStatus);\n    throw e;\n  }\n}\n\n/**\n * Gets all config.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @returns All config.\n *\n * @public\n */\nexport function getAll(remoteConfig: RemoteConfig): Record<string, Value> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  return getAllKeys(\n    rc._storageCache.getActiveConfig(),\n    rc.defaultConfig\n  ).reduce((allConfigs, key) => {\n    allConfigs[key] = getValue(remoteConfig, key);\n    return allConfigs;\n  }, {} as Record<string, Value>);\n}\n\n/**\n * Gets the value for the given key as a boolean.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asBoolean()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a boolean.\n * @public\n */\nexport function getBoolean(remoteConfig: RemoteConfig, key: string): boolean {\n  return getValue(getModularInstance(remoteConfig), key).asBoolean();\n}\n\n/**\n * Gets the value for the given key as a number.\n *\n * Convenience method for calling <code>remoteConfig.getValue(key).asNumber()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a number.\n *\n * @public\n */\nexport function getNumber(remoteConfig: RemoteConfig, key: string): number {\n  return getValue(getModularInstance(remoteConfig), key).asNumber();\n}\n\n/**\n * Gets the value for the given key as a string.\n * Convenience method for calling <code>remoteConfig.getValue(key).asString()</code>.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key as a string.\n *\n * @public\n */\nexport function getString(remoteConfig: RemoteConfig, key: string): string {\n  return getValue(getModularInstance(remoteConfig), key).asString();\n}\n\n/**\n * Gets the {@link Value} for the given key.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param key - The name of the parameter.\n *\n * @returns The value for the given key.\n *\n * @public\n */\nexport function getValue(remoteConfig: RemoteConfig, key: string): Value {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (!rc._isInitializationComplete) {\n    rc._logger.debug(\n      `A value was requested for key \"${key}\" before SDK initialization completed.` +\n        ' Await on ensureInitialized if the intent was to get a previously activated value.'\n    );\n  }\n  const activeConfig = rc._storageCache.getActiveConfig();\n  if (activeConfig && activeConfig[key] !== undefined) {\n    return new ValueImpl('remote', activeConfig[key]);\n  } else if (rc.defaultConfig && rc.defaultConfig[key] !== undefined) {\n    return new ValueImpl('default', String(rc.defaultConfig[key]));\n  }\n  rc._logger.debug(\n    `Returning static value for key \"${key}\".` +\n      ' Define a default or remote value if this is unintentional.'\n  );\n  return new ValueImpl('static');\n}\n\n/**\n * Defines the log level to use.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param logLevel - The log level to set.\n *\n * @public\n */\nexport function setLogLevel(\n  remoteConfig: RemoteConfig,\n  logLevel: RemoteConfigLogLevel\n): void {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  switch (logLevel) {\n    case 'debug':\n      rc._logger.logLevel = FirebaseLogLevel.DEBUG;\n      break;\n    case 'silent':\n      rc._logger.logLevel = FirebaseLogLevel.SILENT;\n      break;\n    default:\n      rc._logger.logLevel = FirebaseLogLevel.ERROR;\n  }\n}\n\n/**\n * Dedupes and returns an array of all the keys of the received objects.\n */\nfunction getAllKeys(obj1: {} = {}, obj2: {} = {}): string[] {\n  return Object.keys({ ...obj1, ...obj2 });\n}\n\n/**\n * Sets the custom signals for the app instance.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n * @param customSignals - Map (key, value) of the custom signals to be set for the app instance. If\n * a key already exists, the value is overwritten. Setting the value of a custom signal to null\n * unsets the signal. The signals will be persisted locally on the client.\n *\n * @public\n */\nexport async function setCustomSignals(\n  remoteConfig: RemoteConfig,\n  customSignals: CustomSignals\n): Promise<void> {\n  const rc = getModularInstance(remoteConfig) as RemoteConfigImpl;\n  if (Object.keys(customSignals).length === 0) {\n    return;\n  }\n\n  // eslint-disable-next-line guard-for-in\n  for (const key in customSignals) {\n    if (key.length > RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH) {\n      rc._logger.error(\n        `Custom signal key ${key} is too long, max allowed length is ${RC_CUSTOM_SIGNAL_KEY_MAX_LENGTH}.`\n      );\n      return;\n    }\n    const value = customSignals[key];\n    if (\n      typeof value === 'string' &&\n      value.length > RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH\n    ) {\n      rc._logger.error(\n        `Value supplied for custom signal ${key} is too long, max allowed length is ${RC_CUSTOM_SIGNAL_VALUE_MAX_LENGTH}.`\n      );\n      return;\n    }\n  }\n\n  try {\n    await rc._storageCache.setCustomSignals(customSignals);\n  } catch (error) {\n    rc._logger.error(\n      `Error encountered while setting custom signals: ${error}`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageCache } from '../storage/storage_cache';\nimport { FetchResponse } from '../public_types';\nimport {\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { Storage } from '../storage/storage';\nimport { Logger } from '@firebase/logger';\n\n/**\n * Implements the {@link RemoteConfigClient} abstraction with success response caching.\n *\n * <p>Comparable to the browser's Cache API for responses, but the Cache API requires a Service\n * Worker, which requires HTTPS, which would significantly complicate SDK installation. Also, the\n * Cache API doesn't support matching entries by time.\n */\nexport class CachingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage,\n    private readonly storageCache: StorageCache,\n    private readonly logger: Logger\n  ) {}\n\n  /**\n   * Returns true if the age of the cached fetched configs is less than or equal to\n   * {@link Settings#minimumFetchIntervalInSeconds}.\n   *\n   * <p>This is comparable to passing `headers = { 'Cache-Control': max-age <maxAge> }` to the\n   * native Fetch API.\n   *\n   * <p>Visible for testing.\n   */\n  isCachedDataFresh(\n    cacheMaxAgeMillis: number,\n    lastSuccessfulFetchTimestampMillis: number | undefined\n  ): boolean {\n    // Cache can only be fresh if it's populated.\n    if (!lastSuccessfulFetchTimestampMillis) {\n      this.logger.debug('Config fetch cache check. Cache unpopulated.');\n      return false;\n    }\n\n    // Calculates age of cache entry.\n    const cacheAgeMillis = Date.now() - lastSuccessfulFetchTimestampMillis;\n\n    const isCachedDataFresh = cacheAgeMillis <= cacheMaxAgeMillis;\n\n    this.logger.debug(\n      'Config fetch cache check.' +\n        ` Cache age millis: ${cacheAgeMillis}.` +\n        ` Cache max age millis (minimumFetchIntervalMillis setting): ${cacheMaxAgeMillis}.` +\n        ` Is cache hit: ${isCachedDataFresh}.`\n    );\n\n    return isCachedDataFresh;\n  }\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    // Reads from persisted storage to avoid cache miss if callers don't wait on initialization.\n    const [lastSuccessfulFetchTimestampMillis, lastSuccessfulFetchResponse] =\n      await Promise.all([\n        this.storage.getLastSuccessfulFetchTimestampMillis(),\n        this.storage.getLastSuccessfulFetchResponse()\n      ]);\n\n    // Exits early on cache hit.\n    if (\n      lastSuccessfulFetchResponse &&\n      this.isCachedDataFresh(\n        request.cacheMaxAgeMillis,\n        lastSuccessfulFetchTimestampMillis\n      )\n    ) {\n      return lastSuccessfulFetchResponse;\n    }\n\n    // Deviates from pure decorator by not honoring a passed ETag since we don't have a public API\n    // that allows the caller to pass an ETag.\n    request.eTag =\n      lastSuccessfulFetchResponse && lastSuccessfulFetchResponse.eTag;\n\n    // Falls back to service on cache miss.\n    const response = await this.client.fetch(request);\n\n    // Fetch throws for non-success responses, so success is guaranteed here.\n\n    const storageOperations = [\n      // Uses write-through cache for consistency with synchronous public API.\n      this.storageCache.setLastSuccessfulFetchTimestampMillis(Date.now())\n    ];\n\n    if (response.status === 200) {\n      // Caches response only if it has changed, ie non-304 responses.\n      storageOperations.push(\n        this.storage.setLastSuccessfulFetchResponse(response)\n      );\n    }\n\n    await Promise.all(storageOperations);\n\n    return response;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomSignals,\n  FetchResponse,\n  FirebaseRemoteConfigObject\n} from '../public_types';\nimport {\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { getUserLanguage } from '../language';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\n/**\n * Defines request body parameters required to call the fetch API:\n * https://firebase.google.com/docs/reference/remote-config/rest\n *\n * <p>Not exported because this file encapsulates REST API specifics.\n *\n * <p>Not passing User Properties because Analytics' source of truth on Web is server-side.\n */\ninterface FetchRequestBody {\n  // Disables camelcase linting for request body params.\n  /* eslint-disable camelcase*/\n  sdk_version: string;\n  app_instance_id: string;\n  app_instance_id_token: string;\n  app_id: string;\n  language_code: string;\n  custom_signals?: CustomSignals;\n  /* eslint-enable camelcase */\n}\n\n/**\n * Implements the Client abstraction for the Remote Config REST API.\n */\nexport class RestClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly firebaseInstallations: _FirebaseInstallationsInternal,\n    private readonly sdkVersion: string,\n    private readonly namespace: string,\n    private readonly projectId: string,\n    private readonly apiKey: string,\n    private readonly appId: string\n  ) {}\n\n  /**\n   * Fetches from the Remote Config REST API.\n   *\n   * @throws a {@link ErrorCode.FETCH_NETWORK} error if {@link GlobalFetch#fetch} can't\n   * connect to the network.\n   * @throws a {@link ErrorCode.FETCH_PARSE} error if {@link Response#json} can't parse the\n   * fetch response.\n   * @throws a {@link ErrorCode.FETCH_STATUS} error if the service returns an HTTP error status.\n   */\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const [installationId, installationToken] = await Promise.all([\n      this.firebaseInstallations.getId(),\n      this.firebaseInstallations.getToken()\n    ]);\n\n    const urlBase =\n      window.FIREBASE_REMOTE_CONFIG_URL_BASE ||\n      'https://firebaseremoteconfig.googleapis.com';\n\n    const url = `${urlBase}/v1/projects/${this.projectId}/namespaces/${this.namespace}:fetch?key=${this.apiKey}`;\n\n    const headers = {\n      'Content-Type': 'application/json',\n      'Content-Encoding': 'gzip',\n      // Deviates from pure decorator by not passing max-age header since we don't currently have\n      // service behavior using that header.\n      'If-None-Match': request.eTag || '*'\n    };\n\n    const requestBody: FetchRequestBody = {\n      /* eslint-disable camelcase */\n      sdk_version: this.sdkVersion,\n      app_instance_id: installationId,\n      app_instance_id_token: installationToken,\n      app_id: this.appId,\n      language_code: getUserLanguage(),\n      custom_signals: request.customSignals\n      /* eslint-enable camelcase */\n    };\n\n    const options = {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(requestBody)\n    };\n\n    // This logic isn't REST-specific, but shimming abort logic isn't worth another decorator.\n    const fetchPromise = fetch(url, options);\n    const timeoutPromise = new Promise((_resolve, reject) => {\n      // Maps async event listener to Promise API.\n      request.signal.addEventListener(() => {\n        // Emulates https://heycam.github.io/webidl/#aborterror\n        const error = new Error('The operation was aborted.');\n        error.name = 'AbortError';\n        reject(error);\n      });\n    });\n\n    let response;\n    try {\n      await Promise.race([fetchPromise, timeoutPromise]);\n      response = await fetchPromise;\n    } catch (originalError) {\n      let errorCode = ErrorCode.FETCH_NETWORK;\n      if ((originalError as Error)?.name === 'AbortError') {\n        errorCode = ErrorCode.FETCH_TIMEOUT;\n      }\n      throw ERROR_FACTORY.create(errorCode, {\n        originalErrorMessage: (originalError as Error)?.message\n      });\n    }\n\n    let status = response.status;\n\n    // Normalizes nullable header to optional.\n    const responseEtag = response.headers.get('ETag') || undefined;\n\n    let config: FirebaseRemoteConfigObject | undefined;\n    let state: string | undefined;\n\n    // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n    // Requesting application/json and checking for a 200 ensures there's JSON data.\n    if (response.status === 200) {\n      let responseBody;\n      try {\n        responseBody = await response.json();\n      } catch (originalError) {\n        throw ERROR_FACTORY.create(ErrorCode.FETCH_PARSE, {\n          originalErrorMessage: (originalError as Error)?.message\n        });\n      }\n      config = responseBody['entries'];\n      state = responseBody['state'];\n    }\n\n    // Normalizes based on legacy state.\n    if (state === 'INSTANCE_STATE_UNSPECIFIED') {\n      status = 500;\n    } else if (state === 'NO_CHANGE') {\n      status = 304;\n    } else if (state === 'NO_TEMPLATE' || state === 'EMPTY_CONFIG') {\n      // These cases can be fixed remotely, so normalize to safe value.\n      config = {};\n    }\n\n    // Normalize to exception-based control flow for non-success cases.\n    // Encapsulates HTTP specifics in this class as much as possible. Status is still the best for\n    // differentiating success states (200 from 304; the state body param is undefined in a\n    // standard 304).\n    if (status !== 304 && status !== 200) {\n      throw ERROR_FACTORY.create(ErrorCode.FETCH_STATUS, {\n        httpStatus: status\n      });\n    }\n\n    return { status, eTag: responseEtag, config };\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Attempts to get the most accurate browser language setting.\n *\n * <p>Adapted from getUserLanguage in packages/auth/src/utils.js for TypeScript.\n *\n * <p>Defers default language specification to server logic for consistency.\n *\n * @param navigatorLanguage Enables tests to override read-only {@link NavigatorLanguage}.\n */\nexport function getUserLanguage(\n  navigatorLanguage: NavigatorLanguage = navigator\n): string {\n  return (\n    // Most reliable, but only supported in Chrome/Firefox.\n    (navigatorLanguage.languages && navigatorLanguage.languages[0]) ||\n    // Supported in most browsers, but returns the language of the browser\n    // UI, not the language set in browser settings.\n    navigatorLanguage.language\n    // Polyfill otherwise.\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchResponse } from '../public_types';\nimport {\n  RemoteConfigAbortSignal,\n  RemoteConfigFetchClient,\n  FetchRequest\n} from './remote_config_fetch_client';\nimport { ThrottleMetadata, Storage } from '../storage/storage';\nimport { ErrorCode, ERROR_FACTORY } from '../errors';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nexport function setAbortableTimeout(\n  signal: RemoteConfigAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(ErrorCode.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Decorates a Client with retry logic.\n *\n * <p>Comparable to CachingClient, but uses backoff logic instead of cache max age and doesn't cache\n * responses (because the SDK has no use for error responses).\n */\nexport class RetryingClient implements RemoteConfigFetchClient {\n  constructor(\n    private readonly client: RemoteConfigFetchClient,\n    private readonly storage: Storage\n  ) {}\n\n  async fetch(request: FetchRequest): Promise<FetchResponse> {\n    const throttleMetadata = (await this.storage.getThrottleMetadata()) || {\n      backoffCount: 0,\n      throttleEndTimeMillis: Date.now()\n    };\n\n    return this.attemptFetch(request, throttleMetadata);\n  }\n\n  /**\n   * A recursive helper for attempting a fetch request repeatedly.\n   *\n   * @throws any non-retriable errors.\n   */\n  async attemptFetch(\n    request: FetchRequest,\n    { throttleEndTimeMillis, backoffCount }: ThrottleMetadata\n  ): Promise<FetchResponse> {\n    // Starts with a (potentially zero) timeout to support resumption from stored state.\n    // Ensures the throttle end time is honored if the last attempt timed out.\n    // Note the SDK will never make a request if the fetch timeout expires at this point.\n    await setAbortableTimeout(request.signal, throttleEndTimeMillis);\n\n    try {\n      const response = await this.client.fetch(request);\n\n      // Note the SDK only clears throttle state if response is success or non-retriable.\n      await this.storage.deleteThrottleMetadata();\n\n      return response;\n    } catch (e) {\n      if (!isRetriableError(e as Error)) {\n        throw e;\n      }\n\n      // Increments backoff state.\n      const throttleMetadata = {\n        throttleEndTimeMillis:\n          Date.now() + calculateBackoffMillis(backoffCount),\n        backoffCount: backoffCount + 1\n      };\n\n      // Persists state.\n      await this.storage.setThrottleMetadata(throttleMetadata);\n\n      return this.attemptFetch(request, throttleMetadata);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  RemoteConfig as RemoteConfigType,\n  FetchStatus,\n  RemoteConfigSettings\n} from './public_types';\nimport { StorageCache } from './storage/storage_cache';\nimport { RemoteConfigFetchClient } from './client/remote_config_fetch_client';\nimport { Storage } from './storage/storage';\nimport { Logger } from '@firebase/logger';\n\nconst DEFAULT_FETCH_TIMEOUT_MILLIS = 60 * 1000; // One minute\nconst DEFAULT_CACHE_MAX_AGE_MILLIS = 12 * 60 * 60 * 1000; // Twelve hours.\n\n/**\n * Encapsulates business logic mapping network and storage dependencies to the public SDK API.\n *\n * See {@link https://github.com/firebase/firebase-js-sdk/blob/main/packages/firebase/compat/index.d.ts|interface documentation} for method descriptions.\n */\nexport class RemoteConfig implements RemoteConfigType {\n  /**\n   * Tracks completion of initialization promise.\n   * @internal\n   */\n  _isInitializationComplete = false;\n\n  /**\n   * De-duplicates initialization calls.\n   * @internal\n   */\n  _initializePromise?: Promise<void>;\n\n  settings: RemoteConfigSettings = {\n    fetchTimeoutMillis: DEFAULT_FETCH_TIMEOUT_MILLIS,\n    minimumFetchIntervalMillis: DEFAULT_CACHE_MAX_AGE_MILLIS\n  };\n\n  defaultConfig: { [key: string]: string | number | boolean } = {};\n\n  get fetchTimeMillis(): number {\n    return this._storageCache.getLastSuccessfulFetchTimestampMillis() || -1;\n  }\n\n  get lastFetchStatus(): FetchStatus {\n    return this._storageCache.getLastFetchStatus() || 'no-fetch-yet';\n  }\n\n  constructor(\n    // Required by FirebaseServiceFactory interface.\n    readonly app: FirebaseApp,\n    // JS doesn't support private yet\n    // (https://github.com/tc39/proposal-class-fields#private-fields), so we hint using an\n    // underscore prefix.\n    /**\n     * @internal\n     */\n    readonly _client: RemoteConfigFetchClient,\n    /**\n     * @internal\n     */\n    readonly _storageCache: StorageCache,\n    /**\n     * @internal\n     */\n    readonly _storage: Storage,\n    /**\n     * @internal\n     */\n    readonly _logger: Logger\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus, CustomSignals } from '@firebase/remote-config-types';\nimport { FetchResponse, FirebaseRemoteConfigObject } from '../public_types';\nimport { ERROR_FACTORY, ErrorCode } from '../errors';\nimport { RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS } from '../constants';\nimport { FirebaseError } from '@firebase/util';\n\n/**\n * Converts an error event associated with a {@link IDBRequest} to a {@link FirebaseError}.\n */\nfunction toFirebaseError(event: Event, errorCode: ErrorCode): FirebaseError {\n  const originalError = (event.target as IDBRequest).error || undefined;\n  return ERROR_FACTORY.create(errorCode, {\n    originalErrorMessage: originalError && (originalError as Error)?.message\n  });\n}\n\n/**\n * A general-purpose store keyed by app + namespace + {@link\n * ProjectNamespaceKeyFieldValue}.\n *\n * <p>The Remote Config SDK can be used with multiple app installations, and each app can interact\n * with multiple namespaces, so this store uses app (ID + name) and namespace as common parent keys\n * for a set of key-value pairs. See {@link Storage#createCompositeKey}.\n *\n * <p>Visible for testing.\n */\nexport const APP_NAMESPACE_STORE = 'app_namespace_store';\n\nconst DB_NAME = 'firebase_remote_config';\nconst DB_VERSION = 1;\n\n/**\n * Encapsulates metadata concerning throttled fetch requests.\n */\nexport interface ThrottleMetadata {\n  // The number of times fetch has backed off. Used for resuming backoff after a timeout.\n  backoffCount: number;\n  // The Unix timestamp in milliseconds when callers can retry a request.\n  throttleEndTimeMillis: number;\n}\n\n/**\n * Provides type-safety for the \"key\" field used by {@link APP_NAMESPACE_STORE}.\n *\n * <p>This seems like a small price to avoid potentially subtle bugs caused by a typo.\n */\ntype ProjectNamespaceKeyFieldValue =\n  | 'active_config'\n  | 'active_config_etag'\n  | 'last_fetch_status'\n  | 'last_successful_fetch_timestamp_millis'\n  | 'last_successful_fetch_response'\n  | 'settings'\n  | 'throttle_metadata'\n  | 'custom_signals';\n\n// Visible for testing.\nexport function openDatabase(): Promise<IDBDatabase> {\n  return new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onerror = event => {\n        reject(toFirebaseError(event, ErrorCode.STORAGE_OPEN));\n      };\n      request.onsuccess = event => {\n        resolve((event.target as IDBOpenDBRequest).result);\n      };\n      request.onupgradeneeded = event => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(APP_NAMESPACE_STORE, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (error) {\n      reject(\n        ERROR_FACTORY.create(ErrorCode.STORAGE_OPEN, {\n          originalErrorMessage: (error as Error)?.message\n        })\n      );\n    }\n  });\n}\n\n/**\n * Abstracts data persistence.\n */\nexport abstract class Storage {\n  getLastFetchStatus(): Promise<FetchStatus | undefined> {\n    return this.get<FetchStatus>('last_fetch_status');\n  }\n\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    return this.set<FetchStatus>('last_fetch_status', status);\n  }\n\n  // This is comparable to a cache entry timestamp. If we need to expire other data, we could\n  // consider adding timestamp to all storage records and an optional max age arg to getters.\n  getLastSuccessfulFetchTimestampMillis(): Promise<number | undefined> {\n    return this.get<number>('last_successful_fetch_timestamp_millis');\n  }\n\n  setLastSuccessfulFetchTimestampMillis(timestamp: number): Promise<void> {\n    return this.set<number>(\n      'last_successful_fetch_timestamp_millis',\n      timestamp\n    );\n  }\n\n  getLastSuccessfulFetchResponse(): Promise<FetchResponse | undefined> {\n    return this.get<FetchResponse>('last_successful_fetch_response');\n  }\n\n  setLastSuccessfulFetchResponse(response: FetchResponse): Promise<void> {\n    return this.set<FetchResponse>('last_successful_fetch_response', response);\n  }\n\n  getActiveConfig(): Promise<FirebaseRemoteConfigObject | undefined> {\n    return this.get<FirebaseRemoteConfigObject>('active_config');\n  }\n\n  setActiveConfig(config: FirebaseRemoteConfigObject): Promise<void> {\n    return this.set<FirebaseRemoteConfigObject>('active_config', config);\n  }\n\n  getActiveConfigEtag(): Promise<string | undefined> {\n    return this.get<string>('active_config_etag');\n  }\n\n  setActiveConfigEtag(etag: string): Promise<void> {\n    return this.set<string>('active_config_etag', etag);\n  }\n\n  getThrottleMetadata(): Promise<ThrottleMetadata | undefined> {\n    return this.get<ThrottleMetadata>('throttle_metadata');\n  }\n\n  setThrottleMetadata(metadata: ThrottleMetadata): Promise<void> {\n    return this.set<ThrottleMetadata>('throttle_metadata', metadata);\n  }\n\n  deleteThrottleMetadata(): Promise<void> {\n    return this.delete('throttle_metadata');\n  }\n\n  getCustomSignals(): Promise<CustomSignals | undefined> {\n    return this.get<CustomSignals>('custom_signals');\n  }\n\n  abstract setCustomSignals(\n    customSignals: CustomSignals\n  ): Promise<CustomSignals>;\n  abstract get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T | undefined>;\n  abstract set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void>;\n  abstract delete(key: ProjectNamespaceKeyFieldValue): Promise<void>;\n}\n\nexport class IndexedDbStorage extends Storage {\n  /**\n   * @param appId enables storage segmentation by app (ID + name).\n   * @param appName enables storage segmentation by app (ID + name).\n   * @param namespace enables storage segmentation by namespace.\n   */\n  constructor(\n    private readonly appId: string,\n    private readonly appName: string,\n    private readonly namespace: string,\n    private readonly openDbPromise = openDatabase()\n  ) {\n    super();\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<CustomSignals> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n    const storedSignals = await this.getWithTransaction<CustomSignals>(\n      'custom_signals',\n      transaction\n    );\n    const updatedSignals = mergeCustomSignals(\n      customSignals,\n      storedSignals || {}\n    );\n    await this.setWithTransaction<CustomSignals>(\n      'custom_signals',\n      updatedSignals,\n      transaction\n    );\n    return updatedSignals;\n  }\n\n  /**\n   * Gets a value from the database using the provided transaction.\n   *\n   * @param key The key of the value to get.\n   * @param transaction The transaction to use for the operation.\n   * @returns The value associated with the key, or undefined if no such value exists.\n   */\n  async getWithTransaction<T>(\n    key: ProjectNamespaceKeyFieldValue,\n    transaction: IDBTransaction\n  ): Promise<T | undefined> {\n    return new Promise((resolve, reject) => {\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.get(compositeKey);\n        request.onerror = event => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_GET));\n        };\n        request.onsuccess = event => {\n          const result = (event.target as IDBRequest).result;\n          if (result) {\n            resolve(result.value);\n          } else {\n            resolve(undefined);\n          }\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_GET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  /**\n   * Sets a value in the database using the provided transaction.\n   *\n   * @param key The key of the value to set.\n   * @param value The value to set.\n   * @param transaction The transaction to use for the operation.\n   * @returns A promise that resolves when the operation is complete.\n   */\n  async setWithTransaction<T>(\n    key: ProjectNamespaceKeyFieldValue,\n    value: T,\n    transaction: IDBTransaction\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.put({\n          compositeKey,\n          value\n        });\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_SET));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_SET, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  async get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T | undefined> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readonly');\n    return this.getWithTransaction<T>(key, transaction);\n  }\n\n  async set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void> {\n    const db = await this.openDbPromise;\n    const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n    return this.setWithTransaction<T>(key, value, transaction);\n  }\n\n  async delete(key: ProjectNamespaceKeyFieldValue): Promise<void> {\n    const db = await this.openDbPromise;\n    return new Promise((resolve, reject) => {\n      const transaction = db.transaction([APP_NAMESPACE_STORE], 'readwrite');\n      const objectStore = transaction.objectStore(APP_NAMESPACE_STORE);\n      const compositeKey = this.createCompositeKey(key);\n      try {\n        const request = objectStore.delete(compositeKey);\n        request.onerror = (event: Event) => {\n          reject(toFirebaseError(event, ErrorCode.STORAGE_DELETE));\n        };\n        request.onsuccess = () => {\n          resolve();\n        };\n      } catch (e) {\n        reject(\n          ERROR_FACTORY.create(ErrorCode.STORAGE_DELETE, {\n            originalErrorMessage: (e as Error)?.message\n          })\n        );\n      }\n    });\n  }\n\n  // Facilitates composite key functionality (which is unsupported in IE).\n  createCompositeKey(key: ProjectNamespaceKeyFieldValue): string {\n    return [this.appId, this.appName, this.namespace, key].join();\n  }\n}\n\nexport class InMemoryStorage extends Storage {\n  private storage: { [key: string]: unknown } = {};\n\n  async get<T>(key: ProjectNamespaceKeyFieldValue): Promise<T> {\n    return Promise.resolve(this.storage[key] as T);\n  }\n\n  async set<T>(key: ProjectNamespaceKeyFieldValue, value: T): Promise<void> {\n    this.storage[key] = value;\n    return Promise.resolve(undefined);\n  }\n\n  async delete(key: ProjectNamespaceKeyFieldValue): Promise<void> {\n    this.storage[key] = undefined;\n    return Promise.resolve();\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<CustomSignals> {\n    const storedSignals = (this.storage['custom_signals'] ||\n      {}) as CustomSignals;\n    this.storage['custom_signals'] = mergeCustomSignals(\n      customSignals,\n      storedSignals\n    );\n    return Promise.resolve(this.storage['custom_signals'] as CustomSignals);\n  }\n}\n\nfunction mergeCustomSignals(\n  customSignals: CustomSignals,\n  storedSignals: CustomSignals\n): CustomSignals {\n  const combinedSignals = {\n    ...storedSignals,\n    ...customSignals\n  };\n\n  // Filter out key-value assignments with null values since they are signals being unset\n  const updatedSignals = Object.fromEntries(\n    Object.entries(combinedSignals)\n      .filter(([_, v]) => v !== null)\n      .map(([k, v]) => {\n        // Stringify numbers to store a map of string keys and values which can be sent\n        // as-is in a fetch call.\n        if (typeof v === 'number') {\n          return [k, v.toString()];\n        }\n        return [k, v];\n      })\n  );\n\n  // Throw an error if the number of custom signals to be stored exceeds the limit\n  if (\n    Object.keys(updatedSignals).length > RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS\n  ) {\n    throw ERROR_FACTORY.create(ErrorCode.CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS, {\n      maxSignals: RC_CUSTOM_SIGNAL_MAX_ALLOWED_SIGNALS\n    });\n  }\n  return updatedSignals;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FetchStatus, CustomSignals } from '@firebase/remote-config-types';\nimport { FirebaseRemoteConfigObject } from '../public_types';\nimport { Storage } from './storage';\n\n/**\n * A memory cache layer over storage to support the SDK's synchronous read requirements.\n */\nexport class StorageCache {\n  constructor(private readonly storage: Storage) {}\n\n  /**\n   * Memory caches.\n   */\n  private lastFetchStatus?: FetchStatus;\n  private lastSuccessfulFetchTimestampMillis?: number;\n  private activeConfig?: FirebaseRemoteConfigObject;\n  private customSignals?: CustomSignals;\n\n  /**\n   * Memory-only getters\n   */\n  getLastFetchStatus(): FetchStatus | undefined {\n    return this.lastFetchStatus;\n  }\n\n  getLastSuccessfulFetchTimestampMillis(): number | undefined {\n    return this.lastSuccessfulFetchTimestampMillis;\n  }\n\n  getActiveConfig(): FirebaseRemoteConfigObject | undefined {\n    return this.activeConfig;\n  }\n\n  getCustomSignals(): CustomSignals | undefined {\n    return this.customSignals;\n  }\n\n  /**\n   * Read-ahead getter\n   */\n  async loadFromStorage(): Promise<void> {\n    const lastFetchStatusPromise = this.storage.getLastFetchStatus();\n    const lastSuccessfulFetchTimestampMillisPromise =\n      this.storage.getLastSuccessfulFetchTimestampMillis();\n    const activeConfigPromise = this.storage.getActiveConfig();\n    const customSignalsPromise = this.storage.getCustomSignals();\n\n    // Note:\n    // 1. we consistently check for undefined to avoid clobbering defined values\n    //   in memory\n    // 2. we defer awaiting to improve readability, as opposed to destructuring\n    //   a Promise.all result, for example\n\n    const lastFetchStatus = await lastFetchStatusPromise;\n    if (lastFetchStatus) {\n      this.lastFetchStatus = lastFetchStatus;\n    }\n\n    const lastSuccessfulFetchTimestampMillis =\n      await lastSuccessfulFetchTimestampMillisPromise;\n    if (lastSuccessfulFetchTimestampMillis) {\n      this.lastSuccessfulFetchTimestampMillis =\n        lastSuccessfulFetchTimestampMillis;\n    }\n\n    const activeConfig = await activeConfigPromise;\n    if (activeConfig) {\n      this.activeConfig = activeConfig;\n    }\n\n    const customSignals = await customSignalsPromise;\n    if (customSignals) {\n      this.customSignals = customSignals;\n    }\n  }\n\n  /**\n   * Write-through setters\n   */\n  setLastFetchStatus(status: FetchStatus): Promise<void> {\n    this.lastFetchStatus = status;\n    return this.storage.setLastFetchStatus(status);\n  }\n\n  setLastSuccessfulFetchTimestampMillis(\n    timestampMillis: number\n  ): Promise<void> {\n    this.lastSuccessfulFetchTimestampMillis = timestampMillis;\n    return this.storage.setLastSuccessfulFetchTimestampMillis(timestampMillis);\n  }\n\n  setActiveConfig(activeConfig: FirebaseRemoteConfigObject): Promise<void> {\n    this.activeConfig = activeConfig;\n    return this.storage.setActiveConfig(activeConfig);\n  }\n\n  async setCustomSignals(customSignals: CustomSignals): Promise<void> {\n    this.customSignals = await this.storage.setCustomSignals(customSignals);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { RemoteConfig } from './public_types';\nimport { activate, fetchConfig } from './api';\nimport {\n  getModularInstance,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\n\n// This API is put in a separate file, so we can stub fetchConfig and activate in tests.\n// It's not possible to stub standalone functions from the same module.\n/**\n *\n * Performs fetch and activate operations, as a convenience.\n *\n * @param remoteConfig - The {@link RemoteConfig} instance.\n *\n * @returns A `Promise` which resolves to true if the current call activated the fetched configs.\n * If the fetched configs were already activated, the `Promise` will resolve to false.\n *\n * @public\n */\nexport async function fetchAndActivate(\n  remoteConfig: RemoteConfig\n): Promise<boolean> {\n  remoteConfig = getModularInstance(remoteConfig);\n  await fetchConfig(remoteConfig);\n  return activate(remoteConfig);\n}\n\n/**\n * This method provides two different checks:\n *\n * 1. Check if IndexedDB exists in the browser environment.\n * 2. Check if the current browser context allows IndexedDB `open()` calls.\n *\n * @returns A `Promise` which resolves to true if a {@link RemoteConfig} instance\n * can be initialized in this environment, or false if it cannot.\n * @public\n */\nexport async function isSupported(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { isIndexedDBAvailable } from '@firebase/util';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer\n} from '@firebase/component';\nimport { Logger, LogLevel as FirebaseLogLevel } from '@firebase/logger';\nimport { RemoteConfig, RemoteConfigOptions } from './public_types';\nimport { name as packageName, version } from '../package.json';\nimport { ensureInitialized } from './api';\nimport { CachingClient } from './client/caching_client';\nimport { RestClient } from './client/rest_client';\nimport { RetryingClient } from './client/retrying_client';\nimport { RC_COMPONENT_NAME } from './constants';\nimport { ErrorCode, ERROR_FACTORY } from './errors';\nimport { RemoteConfig as RemoteConfigImpl } from './remote_config';\nimport { IndexedDbStorage, InMemoryStorage } from './storage/storage';\nimport { StorageCache } from './storage/storage_cache';\n// This needs to be in the same file that calls `getProvider()` on the component\n// or it will get tree-shaken out.\nimport '@firebase/installations';\n\nexport function registerRemoteConfig(): void {\n  _registerComponent(\n    new Component(\n      RC_COMPONENT_NAME,\n      remoteConfigFactory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(packageName, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(packageName, version, '__BUILD_TARGET__');\n\n  function remoteConfigFactory(\n    container: ComponentContainer,\n    { options }: { options?: RemoteConfigOptions }\n  ): RemoteConfig {\n    /* Dependencies */\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    // The following call will always succeed because rc has `import '@firebase/installations'`\n    const installations = container\n      .getProvider('installations-internal')\n      .getImmediate();\n\n    // Normalizes optional inputs.\n    const { projectId, apiKey, appId } = app.options;\n    if (!projectId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_PROJECT_ID);\n    }\n    if (!apiKey) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_API_KEY);\n    }\n    if (!appId) {\n      throw ERROR_FACTORY.create(ErrorCode.REGISTRATION_APP_ID);\n    }\n    const namespace = options?.templateId || 'firebase';\n\n    const storage = isIndexedDBAvailable()\n      ? new IndexedDbStorage(appId, app.name, namespace)\n      : new InMemoryStorage();\n    const storageCache = new StorageCache(storage);\n\n    const logger = new Logger(packageName);\n\n    // Sets ERROR as the default log level.\n    // See RemoteConfig#setLogLevel for corresponding normalization to ERROR log level.\n    logger.logLevel = FirebaseLogLevel.ERROR;\n\n    const restClient = new RestClient(\n      installations,\n      // Uses the JS SDK version, by which the RC package version can be deduced, if necessary.\n      SDK_VERSION,\n      namespace,\n      projectId,\n      apiKey,\n      appId\n    );\n    const retryingClient = new RetryingClient(restClient, storage);\n    const cachingClient = new CachingClient(\n      retryingClient,\n      storage,\n      storageCache,\n      logger\n    );\n\n    const remoteConfigInstance = new RemoteConfigImpl(\n      app,\n      cachingClient,\n      storageCache,\n      storage,\n      logger\n    );\n\n    // Starts warming cache.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    ensureInitialized(remoteConfigInstance);\n\n    return remoteConfigInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app-compat';\nimport {\n  Value as ValueCompat,\n  FetchStatus as FetchSTatusCompat,\n  Settings as SettingsCompat,\n  LogLevel as RemoteConfigLogLevel,\n  RemoteConfig as RemoteConfigCompat\n} from '@firebase/remote-config-types';\nimport {\n  RemoteConfig,\n  setLogLevel,\n  activate,\n  ensureInitialized,\n  fetchAndActivate,\n  fetchConfig,\n  getAll,\n  getBoolean,\n  getNumber,\n  getString,\n  getValue,\n  isSupported\n} from '@firebase/remote-config';\n\nexport { isSupported };\n\nexport class RemoteConfigCompatImpl\n  implements RemoteConfigCompat, _FirebaseService\n{\n  constructor(public app: FirebaseApp, readonly _delegate: RemoteConfig) {}\n\n  get defaultConfig(): { [key: string]: string | number | boolean } {\n    return this._delegate.defaultConfig;\n  }\n\n  set defaultConfig(value: { [key: string]: string | number | boolean }) {\n    this._delegate.defaultConfig = value;\n  }\n\n  get fetchTimeMillis(): number {\n    return this._delegate.fetchTimeMillis;\n  }\n\n  get lastFetchStatus(): FetchSTatusCompat {\n    return this._delegate.lastFetchStatus;\n  }\n\n  get settings(): SettingsCompat {\n    return this._delegate.settings;\n  }\n\n  set settings(value: SettingsCompat) {\n    this._delegate.settings = value;\n  }\n\n  activate(): Promise<boolean> {\n    return activate(this._delegate);\n  }\n\n  ensureInitialized(): Promise<void> {\n    return ensureInitialized(this._delegate);\n  }\n\n  /**\n   * @throws a {@link ErrorCode.FETCH_CLIENT_TIMEOUT} if the request takes longer than\n   * {@link Settings.fetchTimeoutInSeconds} or\n   * {@link DEFAULT_FETCH_TIMEOUT_SECONDS}.\n   */\n  fetch(): Promise<void> {\n    return fetchConfig(this._delegate);\n  }\n\n  fetchAndActivate(): Promise<boolean> {\n    return fetchAndActivate(this._delegate);\n  }\n\n  getAll(): { [key: string]: ValueCompat } {\n    return getAll(this._delegate);\n  }\n\n  getBoolean(key: string): boolean {\n    return getBoolean(this._delegate, key);\n  }\n\n  getNumber(key: string): number {\n    return getNumber(this._delegate, key);\n  }\n\n  getString(key: string): string {\n    return getString(this._delegate, key);\n  }\n\n  getValue(key: string): ValueCompat {\n    return getValue(this._delegate, key);\n  }\n\n  // Based on packages/firestore/src/util/log.ts but not static because we need per-instance levels\n  // to differentiate 2p and 3p use-cases.\n  setLogLevel(logLevel: RemoteConfigLogLevel): void {\n    setLogLevel(this._delegate, logLevel);\n  }\n}\n"], "names": ["LogLevel", "isIndexedDBAvailable", "indexedDB", "e", "FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "getModularInstance", "_delegate", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "instanceOfAny", "object", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "let", "idbProxyTraps", "get", "target", "prop", "receiver", "IDBTransaction", "objectStoreNames", "undefined", "objectStore", "wrap", "set", "has", "wrapFunction", "func", "IDBDatabase", "transaction", "IDBCursor", "advance", "continue", "continuePrimaryKey", "includes", "apply", "unwrap", "storeNames", "tx", "call", "sort", "transformCachableValue", "done", "Promise", "resolve", "reject", "unlisten", "removeEventListener", "complete", "DOMException", "addEventListener", "IDBObjectStore", "IDBIndex", "Proxy", "request", "newValue", "IDBRequest", "promise", "success", "result", "then", "catch", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "targetFuncName", "useIndex", "isWrite", "async", "storeName", "store", "index", "shift", "all", "oldTraps", "PENDING_TIMEOUT_MS", "PACKAGE_VERSION", "version", "INTERNAL_AUTH_VERSION", "INSTALLATIONS_API_URL", "TOKEN_EXPIRATION_BUFFER", "firebaseInstance", "ERROR_FACTORY", "missing-app-config-values", "not-registered", "installation-not-found", "request-failed", "app-offline", "delete-pending-registration", "isServerError", "getInstallationsEndpoint", "projectId", "extractAuthTokenInfoFromResponse", "response", "token", "requestStatus", "expiresIn", "Number", "creationTime", "getErrorFromResponse", "requestName", "errorData", "await", "json", "serverCode", "serverMessage", "serverStatus", "status", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Content-Type", "Accept", "x-goog-api-key", "getHeadersWithAuth", "appConfig", "refreshToken", "headers", "append", "retryIfServerError", "fn", "sleep", "ms", "setTimeout", "VALID_FID_PATTERN", "INVALID_FID", "generateFid", "fidByteArray", "Uint8Array", "fid", "self", "crypto", "msCrypto", "getRandomValues", "array", "btoa", "fromCharCode", "substr", "test", "_a", "<PERSON><PERSON><PERSON>", "appName", "appId", "fidChangeCallbacks", "fidChanged", "callFidChangeCallbacks", "channel", "broadcastChannel", "BroadcastChannel", "onmessage", "postMessage", "size", "close", "callbacks", "DATABASE_NAME", "DATABASE_VERSION", "OBJECT_STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "blocked", "upgrade", "blocking", "terminated", "open", "openPromise", "event", "oldVersion", "newVersion", "db", "createObjectStore", "oldValue", "put", "remove", "delete", "update", "updateFn", "getInstallationEntry", "installations", "registrationPromise", "installationEntry", "oldEntry", "clearTimedOutRequest", "registrationStatus", "entryWithPromise", "inProgressEntry", "navigator", "onLine", "registrationTime", "registeredInstallationEntry", "heartbeatServiceProvider", "endpoint", "body", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "authVersion", "sdkVersion", "JSON", "stringify", "fetch", "ok", "responseValue", "authToken", "registrationPromiseWithError", "entry", "updateInstallationRequest", "generateAuthTokenRequest", "getGenerateAuthTokenEndpoint", "installation", "refreshAuthToken", "forceRefresh", "tokenPromise", "isEntryRegistered", "oldAuthToken", "updateAuthTokenRequest", "inProgressAuthToken", "requestTime", "assign", "updatedInstallationEntry", "getToken", "installationsImpl", "getMissingValueError", "valueName", "INSTALLATIONS_NAME", "publicFactory", "app", "container", "get<PERSON><PERSON><PERSON>", "options", "keyName", "_get<PERSON><PERSON><PERSON>", "_delete", "internalFactory", "getId", "_registerComponent", "registerVersion", "RemoteConfigAbortSignal", "listeners", "listener", "push", "abort", "for<PERSON>ach", "already-initialized", "registration-window", "registration-project-id", "registration-api-key", "registration-app-id", "storage-open", "storage-get", "storage-set", "storage-delete", "fetch-client-network", "fetch-timeout", "fetch-throttle", "fetch-client-parse", "fetch-status", "indexed-db-unavailable", "custom-signal-max-allowed-signals", "BOOLEAN_TRUTHY_VALUES", "Value", "_source", "_value", "asString", "asBoolean", "indexOf", "toLowerCase", "asNumber", "num", "isNaN", "getSource", "activate", "remoteConfig", "rc", "lastSuccessfulFetchResponse", "activeConfigEtag", "_storage", "getLastSuccessfulFetchResponse", "getActiveConfigEtag", "config", "eTag", "_storageCache", "setActiveConfig", "setActiveConfigEtag", "ensureInitialized", "_initializePromise", "loadFromStorage", "_isInitializationComplete", "fetchConfig", "abortSignal", "settings", "fetchTimeoutMillis", "customSignals", "getCustomSignals", "_logger", "_client", "cacheMaxAgeMillis", "minimumFetchIntervalMillis", "signal", "setLastFetchStatus", "errorCode", "lastFetchStatus", "getAll", "obj1", "obj2", "getAllKeys", "getActiveConfig", "defaultConfig", "keys", "reduce", "allConfigs", "getValue", "activeConfig", "ValueImpl", "CachingClient", "client", "storage", "storageCache", "logger", "isCachedDataFresh", "lastSuccessfulFetchTimestampMillis", "cacheAgeMillis", "getLastSuccessfulFetchTimestampMillis", "storageOperations", "setLastSuccessfulFetchTimestampMillis", "setLastSuccessfulFetchResponse", "RestClient", "firebaseInstallations", "namespace", "navigator<PERSON><PERSON><PERSON>ge", "installationId", "installationToken", "url", "window", "FIREBASE_REMOTE_CONFIG_URL_BASE", "Content-Encoding", "If-None-Match", "requestBody", "sdk_version", "app_instance_id", "app_instance_id_token", "app_id", "language_code", "languages", "language", "custom_signals", "fetchPromise", "timeoutPromise", "_resolve", "race", "originalError", "originalErrorMessage", "responseEtag", "state", "responseBody", "httpStatus", "RetryingClient", "throttleMetadata", "getThrottleMetadata", "backoffCount", "throttleEnd<PERSON>imeMill<PERSON>", "attemptFetch", "randomWait", "backoff<PERSON><PERSON><PERSON>", "Math", "max", "timeout", "clearTimeout", "deleteThrottleMetadata", "backoffFactor", "currBaseValue", "pow", "round", "random", "min", "setThrottleMetadata", "RemoteConfig", "fetchTimeMillis", "getLastFetchStatus", "toFirebaseError", "APP_NAMESPACE_STORE", "Storage", "timestamp", "etag", "metadata", "IndexedDbStorage", "openDbPromise", "onerror", "onsuccess", "onupgradeneeded", "keyP<PERSON>", "setCustomSignals", "updatedSignals", "mergeCustomSignals", "getWithTransaction", "setWithTransaction", "compositeKey", "createCompositeKey", "join", "InMemoryStorage", "storedSignals", "combinedSignals", "fromEntries", "entries", "filter", "v", "map", "k", "toString", "length", "maxSignals", "StorageCache", "lastFetchStatusPromise", "lastSuccessfulFetchTimestampMillisPromise", "activeConfigPromise", "customSignalsPromise", "timestampMillis", "isSupported", "preExist", "DB_CHECK_NAME", "deleteDatabase", "templateId", "packageName", "restClient", "FirebaseLogLevel", "SDK_VERSION", "retryingClient", "cachingClient", "remoteConfigInstance", "RemoteConfigImpl", "RemoteConfigCompatImpl", "fetchAndActivate", "getBoolean", "getNumber", "getString", "remoteConfigFactory", "instanceIdentifier", "identifier", "firebase", "INTERNAL", "registerComponent"], "mappings": "yaAsDYA,ECkCC,mBCsGGC,IACd,IACE,MAA4B,UAArB,OAAOC,SAGf,CAFC,MAAOC,IAGX,OC3HaC,UAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,CAAO,EALJG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA6BfC,OAAOC,eAAeH,KAAMP,EAAcW,SAAS,EAI/CV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,EAAaF,UAAUG,MAAM,CAE9D,CACF,OAEYD,EAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,CACf,CAEJH,OACEX,KACGe,GAEH,IAcuCA,EAdjCb,EAAca,EAAK,IAAoB,GACvCC,EAAcZ,KAAKQ,QAAR,IAAmBZ,EAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,GAUuBF,EAVcb,EAAVe,EAW7BC,QAAQC,EAAS,CAACC,EAAGC,KACnC,IAAMC,EAAQP,EAAKM,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,CAAK,MAAQD,KAC7C,CAAC,GAdoE,QAE7DG,EAAiBpB,KAAKS,iBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,EAAcmB,EAAUQ,EAAatB,CAAU,CAGlE,CACF,CASD,IAAMiB,EAAU,gBClHV,SAAUM,EACdb,GAEA,OAAIA,GAAYA,EAA+Bc,UACrCd,EAA+Bc,UAEhCd,CAEX,OCDae,EAiBX5B,YACWM,EACAuB,EACAC,GAFAzB,KAAIC,KAAJA,EACAD,KAAewB,gBAAfA,EACAxB,KAAIyB,KAAJA,EAnBXzB,KAAiB0B,kBAAG,CAAA,EAIpB1B,KAAY2B,aAAe,GAE3B3B,KAAA4B,kBAA2C,OAE3C5B,KAAiB6B,kBAAwC,IAYrD,CAEJC,qBAAqBC,GAEnB,OADA/B,KAAK4B,kBAAoBG,EAClB/B,IACR,CAEDgC,qBAAqBN,GAEnB,OADA1B,KAAK0B,kBAAoBA,EAClB1B,IACR,CAEDiC,gBAAgBC,GAEd,OADAlC,KAAK2B,aAAeO,EACblC,IACR,CAEDmC,2BAA2BC,GAEzB,OADApC,KAAK6B,kBAAoBO,EAClBpC,IACR,CACF,ELhBWX,EAAAA,EAAAA,GAOX,IANCA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SAGF,IAAMgD,EAA2D,CAC/DC,MAASjD,EAASkD,MAClBC,QAAWnD,EAASoD,QACpBC,KAAQrD,EAASsD,KACjBC,KAAQvD,EAASwD,KACjBC,MAASzD,EAAS0D,MAClBC,OAAU3D,EAAS4D,QAMfC,EAA4B7D,EAASsD,KAmBrCQ,EAAgB,EACnB9D,EAASkD,OAAQ,OACjBlD,EAASoD,SAAU,OACnBpD,EAASsD,MAAO,QAChBtD,EAASwD,MAAO,QAChBxD,EAAS0D,OAAQ,SAQdK,EAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAAA,EAAUD,EAASG,UAAvB,CAGA,IAAMC,GAAM,IAAIC,MAAOC,YAAW,EAC5BC,EAAST,EAAcG,GAC7B,GAAIM,CAAAA,EAMF,MAAM,IAAIlE,oEACsD4D,IAAU,EAN1EO,QAAQD,OACFH,OAASJ,EAASpD,QACtB,GAAGsD,CAAI,CANV,CAaH,QAEaO,EAOXnE,YAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAAS+D,UAAGb,EAsBZlD,KAAWgE,YAAeZ,EAc1BpD,KAAeiE,gBAAsB,IAzC5C,CAODT,eACE,OAAOxD,KAAK+D,SACb,CAEDP,aAAaU,GACX,GAAI,EAAEA,KAAO7E,GACX,MAAM,IAAI8E,4BAA4BD,6BAA+B,EAEvElE,KAAK+D,UAAYG,CAClB,CAGDE,YAAYF,GACVlE,KAAK+D,UAA2B,UAAf,OAAOG,EAAmB7B,EAAkB6B,GAAOA,CACrE,CAODG,iBACE,OAAOrE,KAAKgE,WACb,CACDK,eAAeH,GACb,GAAmB,YAAf,OAAOA,EACT,MAAM,IAAIC,UAAU,mDAAmD,EAEzEnE,KAAKgE,YAAcE,CACpB,CAMDI,qBACE,OAAOtE,KAAKiE,eACb,CACDK,mBAAmBJ,GACjBlE,KAAKiE,gBAAkBC,CACxB,CAMD5B,SAASiB,GACPvD,KAAKiE,iBAAmBjE,KAAKiE,gBAAgBjE,KAAMX,EAASkD,MAAO,GAAGgB,CAAI,EAC1EvD,KAAKgE,YAAYhE,KAAMX,EAASkD,MAAO,GAAGgB,CAAI,CAC/C,CACDgB,OAAOhB,GACLvD,KAAKiE,iBACHjE,KAAKiE,gBAAgBjE,KAAMX,EAASoD,QAAS,GAAGc,CAAI,EACtDvD,KAAKgE,YAAYhE,KAAMX,EAASoD,QAAS,GAAGc,CAAI,CACjD,CACDb,QAAQa,GACNvD,KAAKiE,iBAAmBjE,KAAKiE,gBAAgBjE,KAAMX,EAASsD,KAAM,GAAGY,CAAI,EACzEvD,KAAKgE,YAAYhE,KAAMX,EAASsD,KAAM,GAAGY,CAAI,CAC9C,CACDX,QAAQW,GACNvD,KAAKiE,iBAAmBjE,KAAKiE,gBAAgBjE,KAAMX,EAASwD,KAAM,GAAGU,CAAI,EACzEvD,KAAKgE,YAAYhE,KAAMX,EAASwD,KAAM,GAAGU,CAAI,CAC9C,CACDT,SAASS,GACPvD,KAAKiE,iBAAmBjE,KAAKiE,gBAAgBjE,KAAMX,EAAS0D,MAAO,GAAGQ,CAAI,EAC1EvD,KAAKgE,YAAYhE,KAAMX,EAAS0D,MAAO,GAAGQ,CAAI,CAC/C,CACF,CMnND,IAAMiB,EAAgB,CAACC,EAAQC,IAAiBA,EAAaC,KAAK,GAAOF,aAAkBG,CAAC,EAExFC,EACAC,EAqBJ,IAAMC,EAAmB,IAAIC,QACvBC,EAAqB,IAAID,QACzBE,EAA2B,IAAIF,QAC/BG,EAAiB,IAAIH,QACrBI,EAAwB,IAAIJ,QA0DlCK,IAAIC,EAAgB,CAChBC,IAAIC,EAAQC,EAAMC,GACd,GAAIF,aAAkBG,eAAgB,CAElC,GAAa,SAATF,EACA,OAAOR,EAAmBM,IAAIC,CAAM,EAExC,GAAa,qBAATC,EACA,OAAOD,EAAOI,kBAAoBV,EAAyBK,IAAIC,CAAM,EAGzE,GAAa,UAATC,EACA,OAAOC,EAASE,iBAAiB,GAC3BC,KAAAA,EACAH,EAASI,YAAYJ,EAASE,iBAAiB,EAAE,CAE9D,CAED,OAAOG,EAAKP,EAAOC,EAAK,CAC3B,EACDO,IAAIR,EAAQC,EAAMvE,GAEd,OADAsE,EAAOC,GAAQvE,EACR,CAAA,CACV,EACD+E,IAAIT,EAAQC,GACR,OAAID,aAAkBG,iBACR,SAATF,GAA4B,UAATA,IAGjBA,KAAQD,CAClB,CACL,EAIA,SAASU,EAAaC,GAIlB,OAAIA,IAASC,YAAYhG,UAAUiG,aAC7B,qBAAsBV,eAAevF,WA7GnC0E,EAAAA,GACoB,CACpBwB,UAAUlG,UAAUmG,QACpBD,UAAUlG,UAAUoG,SACpBF,UAAUlG,UAAUqG,qBAqHEC,SAASP,CAAI,EAChC,YAAa5C,GAIhB,OADA4C,EAAKQ,MAAMC,EAAO5G,IAAI,EAAGuD,CAAI,EACtBwC,EAAKhB,EAAiBQ,IAAIvF,IAAI,CAAC,CAClD,EAEW,YAAauD,GAGhB,OAAOwC,EAAKI,EAAKQ,MAAMC,EAAO5G,IAAI,EAAGuD,CAAI,CAAC,CAClD,EAvBe,SAAUsD,KAAetD,GAC5B,IAAMuD,EAAKX,EAAKY,KAAKH,EAAO5G,IAAI,EAAG6G,EAAY,GAAGtD,CAAI,EAEtD,OADA2B,EAAyBc,IAAIc,EAAID,EAAWG,KAAOH,EAAWG,KAAM,EAAG,CAACH,EAAW,EAC5Ed,EAAKe,CAAE,CAC1B,CAoBA,CACA,SAASG,EAAuB/F,GAC5B,IA5FoC4F,EAI9BI,EAwFN,MAAqB,YAAjB,OAAOhG,EACAgF,EAAahF,CAAK,GAGzBA,aAAiByE,iBAhGemB,EAiGD5F,EA/F/B+D,EAAmBgB,IAAIa,CAAE,IAEvBI,EAAO,IAAIC,QAAQ,CAACC,EAASC,KAC/B,IAAMC,EAAW,KACbR,EAAGS,oBAAoB,WAAYC,CAAQ,EAC3CV,EAAGS,oBAAoB,QAASzE,CAAK,EACrCgE,EAAGS,oBAAoB,QAASzE,CAAK,CACjD,EACc0E,EAAW,KACbJ,IACAE,GACZ,EACcxE,EAAQ,KACVuE,EAAOP,EAAGhE,OAAS,IAAI2E,aAAa,aAAc,YAAY,CAAC,EAC/DH,GACZ,EACQR,EAAGY,iBAAiB,WAAYF,CAAQ,EACxCV,EAAGY,iBAAiB,QAAS5E,CAAK,EAClCgE,EAAGY,iBAAiB,QAAS5E,CAAK,CAC1C,CAAK,EAEDmC,EAAmBe,IAAIc,EAAII,CAAI,IA2E3B1C,EAActD,EAzJV2D,EAAAA,GACiB,CACjBuB,YACAuB,eACAC,SACAtB,UACAX,eAmJuC,EACpC,IAAIkC,MAAM3G,EAAOoE,CAAa,EAElCpE,EACX,CACA,SAAS6E,EAAK7E,GAGV,IA1IsB4G,EAgJhBC,EANN,OAAI7G,aAAiB8G,YA1ICF,EA2IM5G,GA1ItB+G,EAAU,IAAId,QAAQ,CAACC,EAASC,KAClC,IAAMC,EAAW,KACbQ,EAAQP,oBAAoB,UAAWW,CAAO,EAC9CJ,EAAQP,oBAAoB,QAASzE,CAAK,CACtD,EACcoF,EAAU,KACZd,EAAQrB,EAAK+B,EAAQK,MAAM,CAAC,EAC5Bb,GACZ,EACcxE,EAAQ,KACVuE,EAAOS,EAAQhF,KAAK,EACpBwE,GACZ,EACQQ,EAAQJ,iBAAiB,UAAWQ,CAAO,EAC3CJ,EAAQJ,iBAAiB,QAAS5E,CAAK,CAC/C,CAAK,GAEIsF,KAAK,IAGFlH,aAAiBoF,WACjBvB,EAAiBiB,IAAI9E,EAAO4G,CAAO,CAG/C,CAAK,EACIO,MAAM,MAAS,EAGpBjD,EAAsBY,IAAIiC,EAASH,CAAO,EACnCG,GAgHH9C,EAAec,IAAI/E,CAAK,EACjBiE,EAAeI,IAAIrE,CAAK,IAC7B6G,EAAWd,EAAuB/F,CAAK,KAG5BA,IACbiE,EAAea,IAAI9E,EAAO6G,CAAQ,EAClC3C,EAAsBY,IAAI+B,EAAU7G,CAAK,GAEtC6G,EACX,CACA,IAAMnB,EAAS,GAAWxB,EAAsBG,IAAIrE,CAAK,ELrIzD,IAAMoH,EAAc,CAAC,MAAO,SAAU,SAAU,aAAc,SACxDC,EAAe,CAAC,MAAO,MAAO,SAAU,SACxCC,EAAgB,IAAIC,IAC1B,SAASC,EAAUlD,EAAQC,GACvB,GAAMD,aAAkBY,aAClB,EAAAX,KAAQD,IACM,UAAhB,OAAOC,EAFX,CAKA,GAAI+C,EAAcjD,IAAIE,CAAI,EACtB,OAAO+C,EAAcjD,IAAIE,CAAI,EACjC,IAAMkD,EAAiBlD,EAAK3E,QAAQ,aAAc,EAAE,EAC9C8H,EAAWnD,IAASkD,EACpBE,EAAUN,EAAa7B,SAASiC,CAAc,EACpD,IAMM/E,EANN,OAEE+E,KAAmBC,EAAWhB,SAAWD,gBAAgBvH,YACrDyI,GAAWP,EAAY5B,SAASiC,CAAc,IAG9C/E,EAASkF,eAAgBC,KAAcxF,GAEzC,IAAMuD,EAAK9G,KAAKqG,YAAY0C,EAAWF,EAAU,YAAc,UAAU,EACzExD,IAAIG,EAASsB,EAAGkC,MAQhB,OAPIJ,IACApD,EAASA,EAAOyD,MAAM1F,EAAK2F,MAAO,CAAA,IAM/B,MAAO/B,QAAQgC,IAAI,CACtB3D,EAAOmD,GAAgB,GAAGpF,CAAI,EAC9BsF,GAAW/B,EAAGI,KACjB,GAAG,EACZ,EACIsB,EAAcxC,IAAIP,EAAM7B,CAAM,EACvBA,GAvBP,KAAA,CANC,CA8BL,CKgCI0B,EL/BwB,CACxB,GADS,EK+BgBA,EL7BzBC,IAAK,CAACC,EAAQC,EAAMC,IAAagD,EAAUlD,EAAQC,CAAI,GAAK2D,EAAS7D,IAAIC,EAAQC,EAAMC,CAAQ,EAC/FO,IAAK,CAACT,EAAQC,IAAS,CAAC,CAACiD,EAAUlD,EAAQC,CAAI,GAAK2D,EAASnD,IAAIT,EAAQC,CAAI,CAChF,6CMzEM,IAAM4D,EAAqB,IAErBC,EAAkB,KAAKC,EACvBC,GAAwB,SAExBC,GACX,kDAEWC,GAA0B,KAEhC,ICALC,ECsBK,IAAMC,EAAgB,IAAItJ,EFtBV,gBACK,gBED2C,CACrEuJ,4BACE,kDACFC,iBAA4B,2CAC5BC,yBAAoC,mCACpCC,iBACE,6FACFC,cAAyB,kDACzBC,8BACE,2EAgBmB,EAYjB,SAAUC,GAAcrH,GAC5B,OACEA,aAAiBrD,GACjBqD,EAAMlD,KAAK8G,SAAQ,iBAEvB,CCxCgB,SAAA0D,GAAyB,CAAEC,UAAAA,IACzC,OAAUZ,gBAAkCY,iBAC9C,CAEM,SAAUC,GACdC,GAEA,MAAO,CACLC,MAAOD,EAASC,MAChBC,cAAsC,EACtCC,UAgEKC,OAhEwCJ,EAASG,UAgExB5J,QAAQ,IAAK,KAAK,CAAC,EA/DjD8J,aAAclH,KAAKD,IAAK,EAE5B,CAEOqF,eAAe+B,GACpBC,EACAP,GAEA,IACMQ,GAD8BC,MAAMT,EAASU,QACpBnI,MAC/B,OAAO8G,EAAcrJ,OAAiC,iBAAA,CACpDuK,YAAAA,EACAI,WAAYH,EAAUnL,KACtBuL,cAAeJ,EAAUlL,QACzBuL,aAAcL,EAAUM,MACzB,CAAA,CACH,CAEgB,SAAAC,GAAW,CAAEC,OAAAA,IAC3B,OAAO,IAAIC,QAAQ,CACjBC,eAAgB,mBAChBC,OAAQ,mBACRC,iBAAkBJ,CACnB,CAAA,CACH,CAEgB,SAAAK,GACdC,EACA,CAAEC,aAAAA,IAEF,IAAMC,EAAUT,GAAWO,CAAS,EAEpC,OADAE,EAAQC,OAAO,iBAmCeF,EAnCyBA,EAoC7CtC,GAAH,IAA4BsC,EApCiC,EAC7DC,CACT,CAeOjD,eAAemD,GACpBC,GAEA,IAAM/D,EAAS6C,MAAMkB,IAErB,OAAqB,KAAjB/D,EAAOkD,QAAiBlD,EAAOkD,OAAS,IAEnCa,EAAE,EAGJ/D,CACT,CCnFM,SAAUgE,GAAMC,GACpB,OAAO,IAAIjF,QAAcC,IACvBiF,WAAWjF,EAASgF,CAAE,CACxB,CAAC,CACH,CCHO,IAAME,GAAoB,oBACpBC,EAAc,GAMX,SAAAC,KACd,IAGE,IAAMC,EAAe,IAAIC,WAAW,EAAE,EAQhCC,IANJC,KAAKC,QAAWD,KAAyCE,UACpDC,gBAAgBN,CAAY,EAGnCA,EAAa,GAAK,IAAcA,EAAa,GAAK,ICnBhBO,GACxBC,KAAK9L,OAAO+L,aAAa,GAAGF,CAAK,CAAC,EACnClM,QAAQ,MAAO,GAAG,EAAEA,QAAQ,MAAO,GAAG,GDmB5B2L,CAW+B,EAInCU,OAAO,EAAG,EAAE,GAb3B,OAAOb,GAAkBc,KAAKT,CAAG,EAAIA,EAAMJ,CAI5C,CAHC,MAAAc,GAEA,OAAOd,CACR,CACH,CEzBM,SAAUe,EAAOzB,GACrB,OAAUA,EAAU0B,QAAb,IAAwB1B,EAAU2B,KAC3C,CCDA,IAAMC,GAA2D,IAAIhF,IAMrD,SAAAiF,GAAW7B,EAAsBc,GAC/C,IAAM1L,EAAMqM,EAAOzB,CAAS,EAwDF5K,GAtD1B0M,GAAuB1M,EAAK0L,CAAG,EACZ1L,GAsDb2M,GASR,KACM,CAACC,GAAoB,qBAAsBjB,QAC7CiB,EAAmB,IAAIC,iBAAiB,uBAAuB,GAC9CC,UAAYvO,IAC3BmO,GAAuBnO,EAAEmB,KAAKM,IAAKzB,EAAEmB,KAAKgM,GAAG,CAC/C,GAEKkB,MAfHD,GACFA,EAAQI,YAAY,CAAE/M,IAAAA,EAAK0L,IAAAA,CAAK,CAAA,EAkBF,IAA5Bc,GAAmBQ,MAAcJ,IACnCA,EAAiBK,MAAK,EACtBL,EAAmB,KA3EvB,CAyCA,SAASF,GAAuB1M,EAAa0L,GAC3C,IAAMwB,EAAYV,GAAmBlI,IAAItE,CAAG,EAC5C,GAAKkN,EAIL,IAAK,IAAM/L,KAAY+L,EACrB/L,EAASuK,CAAG,CAEhB,CAUAtH,IAAIwI,EAA4C,KCrEhD,IAAMO,GAAgB,kCAChBC,GAAmB,EACnBC,EAAoB,+BAStBC,GAA2D,KAC/D,SAASC,IAgBP,OAfKD,GAAAA,Kf1BP,CAAgBtO,EAAMsJ,EAAS,CAAEkF,QAAAA,EAASC,QAAAA,EAASC,SAAAA,EAAUC,WAAAA,CAAY,KACrE,IAAM9G,EAAUvI,UAAUsP,KAAK5O,EAAMsJ,CAAO,EAC5C,IAAMuF,EAAc/I,EAAK+B,CAAO,EAoBhC,OAnBI4G,GACA5G,EAAQJ,iBAAiB,gBAAiB,IACtCgH,EAAQ3I,EAAK+B,EAAQK,MAAM,EAAG4G,EAAMC,WAAYD,EAAME,WAAYlJ,EAAK+B,EAAQzB,WAAW,EAAG0I,CAAK,CAC9G,CAAS,EAEDN,GACA3G,EAAQJ,iBAAiB,UAAW,GAAW+G,EAE/CM,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,EAE9CD,EACK1G,KAAK,IACFwG,GACAM,EAAGxH,iBAAiB,QAAS,IAAMkH,EAAY,CAAA,EAC/CD,GACAO,EAAGxH,iBAAiB,gBAAiB,GAAWiH,EAASI,EAAMC,WAAYD,EAAME,WAAYF,CAAK,CAAC,CAE/G,CAAK,EACI1G,MAAM,MAAS,EACbyG,CACX,GeIuBV,GAAeC,GAAkB,CAClDK,QAAS,CAACQ,EAAIF,KAOL,IADCA,GAEJE,EAAGC,kBAAkBb,CAAiB,CAE3C,CACF,CAAA,CAGL,CAeOxF,eAAe9C,EACpB6F,EACA3K,GAEA,IAAMD,EAAMqM,EAAOzB,CAAS,EAEtB/E,GADKkE,MAAMwD,KACHnI,YAAYiI,EAAmB,WAAW,EAClDxI,EAAcgB,EAAGhB,YAAYwI,CAAiB,EAC9Cc,EAAQ,MAAUtJ,EAAYP,IAAItE,CAAG,EAQ3C,OAPA+J,MAAMlF,EAAYuJ,IAAInO,EAAOD,CAAG,EAChC+J,MAAMlE,EAAGI,KAEJkI,GAAYA,EAASzC,MAAQzL,EAAMyL,KACtCe,GAAW7B,EAAW3K,EAAMyL,GAAG,EAG1BzL,CACT,CAGO4H,eAAewG,GAAOzD,GAC3B,IAAM5K,EAAMqM,EAAOzB,CAAS,EAEtB/E,GADKkE,MAAMwD,KACHnI,YAAYiI,EAAmB,WAAW,EACxDtD,MAAMlE,EAAGhB,YAAYwI,CAAiB,EAAEiB,OAAOtO,CAAG,EAClD+J,MAAMlE,EAAGI,IACX,CAQO4B,eAAe0G,EACpB3D,EACA4D,GAEA,IAAMxO,EAAMqM,EAAOzB,CAAS,EAEtB/E,GADKkE,MAAMwD,KACHnI,YAAYiI,EAAmB,WAAW,EAClDtF,EAAQlC,EAAGhB,YAAYwI,CAAiB,EACxCc,EAAQ,MAAyCpG,EAAMzD,IAC3DtE,CAAG,EAEC8G,EAAW0H,EAASL,CAAQ,EAalC,OAXiBvJ,KAAAA,IAAbkC,EACFiD,MAAMhC,EAAMuG,OAAOtO,CAAG,EAEtB+J,MAAMhC,EAAMqG,IAAItH,EAAU9G,CAAG,EAE/B+J,MAAMlE,EAAGI,KAELa,CAAAA,GAAcqH,GAAYA,EAASzC,MAAQ5E,EAAS4E,KACtDe,GAAW7B,EAAW9D,EAAS4E,GAAG,EAG7B5E,CACT,CClFOe,eAAe4G,EACpBC,GAEAtK,IAAIuK,EAEJ,IAAMC,EAAoB7E,MAAMwE,EAAOG,EAAc9D,UAAWiE,IAC9D,IAAMD,EAgCDE,GAhCqDD,GA2Bf,CAC3CnD,IAAKH,GAAa,EAClBwD,mBAA6C,EAGd,EA/BzBC,GAyCV,CACEN,EACAE,KAEA,IAaQK,EAKAN,EAlBR,OAAwC,IAApCC,EAAkBG,mBACfG,UAAUC,QAYTF,EAA+C,CACnDvD,IAAKkD,EAAkBlD,IACvBqD,mBAA6C,EAC7CK,iBAAkB3M,KAAKD,IAAK,GAExBmM,GAkBV9G,MACE6G,EACAE,KAEA,IACE,IAAMS,EAA8BtF,MCxGjClC,MACL,CAAE+C,UAAAA,EAAW0E,yBAAAA,CAAwB,EACrC,CAAE5D,IAAAA,CAAG,KAEL,IAAM6D,EAAWpG,GAAyByB,CAAS,EAEnD,IAAME,EAAUT,GAAWO,CAAS,EAa9B4E,IAPFC,EAHqBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,KAEOC,EAAmB7F,MAAM0F,EAAiBI,wBAE9C/E,EAAQC,OAAO,oBAAqB6E,CAAgB,EAI3C,CACXlE,IAAAA,EACAoE,YAAavH,GACbgE,MAAO3B,EAAU2B,MACjBwD,WAAY1H,IAGd,IAAMxB,EAAuB,CAC3BlE,OAAQ,OACRmI,QAAAA,EACA0E,KAAMQ,KAAKC,UAAUT,CAAI,GAI3B,IADMlG,EAAWS,MAAMiB,GAAmB,IAAMkF,MAAMX,EAAU1I,CAAO,CAAC,GAC3DsJ,GAQX,MANiE,CAC/DzE,KAFI0E,EAA4CrG,MAAMT,EAASU,QAE5C0B,KAAOA,EAC1BqD,mBAA2C,EAC3ClE,aAAcuF,EAAcvF,aAC5BwF,UAAWhH,GAAiC+G,EAAcC,SAAS,GAIrE,MAAMtG,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,GD4DMoF,EACAE,CAAiB,EAEnB,OAAO7J,EAAI2J,EAAc9D,UAAWyE,CAA2B,CAchE,CAbC,MAAO9Q,GAYP,MAXI2K,GAAc3K,CAAC,GAAiC,MAA5BA,EAAEM,WAAWoL,WAGnCF,MAAMsE,GAAOK,EAAc9D,SAAS,EAGpCb,MAAMhF,EAAI2J,EAAc9D,UAAW,CACjCc,IAAKkD,EAAkBlD,IACvBqD,mBAA6C,CAC9C,CAAA,EAEGxQ,CACP,CACH,GAzCMmQ,EACAO,CAAe,EAEV,CAAEL,kBAAmBK,EAAiBN,oBAAAA,KAnBrC2B,EAA+BpK,QAAQE,OAC3CuC,EAAcrJ,OAA6B,aAAA,CAAA,EAEtC,CACLsP,kBAAAA,EACAD,oBAAqB2B,IAgBW,IAApC1B,EAAkBG,mBAEX,CACLH,kBAAAA,EACAD,qBAmCN9G,MACE6G,IAMAtK,IAAImM,EAA2BxG,MAAMyG,GACnC9B,EAAc9D,SAAS,EAEzB,KAA+B,IAAxB2F,EAAMxB,oBAEXhF,MAAMmB,GAAM,GAAG,EAEfqF,EAAQxG,MAAMyG,GAA0B9B,EAAc9D,SAAS,EAGjE,IAEUgE,EAAmBD,EAF7B,OAA4B,IAAxB4B,EAAMxB,mBAaHwB,GAXC,CAAE3B,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAC5C5E,MAAM0E,EAAqBC,CAAa,EAEtCC,GAIKC,EAKb,GAlEoDF,CAAa,GAGtD,CAAEE,kBAAAA,CAAiB,CAE9B,GA7EMF,EACAE,CAAiB,EAGnB,OADAD,EAAsBK,EAAiBL,oBAChCK,EAAiBJ,iBAC1B,CAAC,EAED,OAAIA,EAAkBlD,MAAQJ,EAErB,CAAEsD,kBAAmB7E,MAAM4E,GAG7B,CACLC,kBAAAA,EACAD,oBAAAA,EAEJ,CAoIA,SAAS6B,GACP5F,GAEA,OAAO2D,EAAO3D,EAAWiE,IACvB,GAAKA,EAGL,OAAOC,GAAqBD,CAAQ,EAFlC,MAAMlG,EAAcrJ,OAAM,yBAG9B,CAAC,CACH,CAEA,SAASwP,GAAqByB,GAC5B,IAWA3B,EAXA,OAcoE,KAHpEA,EAXmC2B,GAcfxB,oBAClBH,EAAkBQ,iBAAmBhH,EAAqB3F,KAAKD,IAAG,EAd3D,CACLkJ,IAAK6E,EAAM7E,IACXqD,mBAA6C,GAI1CwB,CACT,CEzLO1I,eAAe4I,GACpB,CAAE7F,UAAAA,EAAW0E,yBAAAA,CAAwB,EACrCV,GAEiB8B,CAwCjB9F,EACEc,GAzCegF,CAA6B9F,EAAWgE,OAAzD,IAAMW,EA2CIpG,GAAyByB,CAAS,MAAKc,wBAJnD,IACEd,EACEc,EAvCIZ,EAAUH,GAAmBC,EAAWgE,CAAiB,EAGzDa,EAAmBH,EAAyBI,aAAa,CAC7DC,SAAU,CAAA,CACX,CAAA,EAQKH,GAPFC,IACIG,EAAmB7F,MAAM0F,EAAiBI,wBAE9C/E,EAAQC,OAAO,oBAAqB6E,CAAgB,EAI3C,CACXe,aAAc,CACZZ,WAAY1H,EACZkE,MAAO3B,EAAU2B,KAClB,IAGH,IAAM1F,EAAuB,CAC3BlE,OAAQ,OACRmI,QAAAA,EACA0E,KAAMQ,KAAKC,UAAUT,CAAI,GAGrBlG,EAAWS,MAAMiB,GAAmB,IAAMkF,MAAMX,EAAU1I,CAAO,CAAC,EACxE,GAAIyC,EAAS6G,GAIX,OADE9G,GAF+CU,MAAMT,EAASU,MAEhB,EAGhD,MAAMD,MAAMH,GAAqB,sBAAuBN,CAAQ,CAEpE,CCnCOzB,eAAe+I,EACpBlC,EACAmC,EAAe,CAAA,GAEfzM,IAAI0M,EACJ,IAAMP,EAAQxG,MAAMwE,EAAOG,EAAc9D,UAAWiE,IAClD,GAAI,CAACkC,GAAkBlC,CAAQ,EAC7B,MAAMlG,EAAcrJ,OAAM,kBAG5B,IAgIsB+Q,EAhIhBW,EAAenC,EAASwB,UAC9B,GAAKQ,GAiI8C,KAF7BR,EA/HgBW,GAiI5BxH,gBAKc6G,IAC1B,IAAM7N,EAAMC,KAAKD,MACjB,OACEA,EAAM6N,EAAU1G,cAChB0G,EAAU1G,aAAe0G,EAAU5G,UAAYjH,EAAMiG,EAEzD,GAVwB4H,CAAS,EA/HtB,CAAA,GAA8B,IAA1BW,EAAaxH,cAGtB,OADAsH,GA0BNjJ,MACE6G,EACAmC,KAMAzM,IAAImM,EAAQxG,MAAMkH,GAAuBvC,EAAc9D,SAAS,EAChE,KAAoC,IAA7B2F,EAAMF,UAAU7G,eAErBO,MAAMmB,GAAM,GAAG,EAEfqF,EAAQxG,MAAMkH,GAAuBvC,EAAc9D,SAAS,EAG9D,IAAMyF,EAAYE,EAAMF,UACxB,OAA2B,IAAvBA,EAAU7G,cAELoH,EAAiBlC,EAAemC,CAAY,EAE5CR,CAEX,GAjD+C3B,EAAemC,CAAY,EAC7DhC,EAGP,GAAKK,UAAUC,OAMf,OAiIJN,EAnIgEA,EAqI1DqC,EAA2C,CAC/C1H,cAAwC,EACxC2H,YAAa1O,KAAKD,IAAK,GAvIfyM,EAyIVhQ,OAAAmS,OAAAnS,OAAAmS,OAAA,GACKvC,CAAQ,EAAA,CACXwB,UAAWa,CAAmB,CAC9B,EA3IEJ,GAsENjJ,MACE6G,EACAE,KAEA,IACE,IAAMyB,EAAYtG,MAAM0G,GACtB/B,EACAE,CAAiB,EAEbyC,EACDpS,OAAAmS,OAAAnS,OAAAmS,OAAA,GAAAxC,CAAiB,EACpB,CAAAyB,UAAAA,CAAS,GAGX,OADAtG,MAAMhF,EAAI2J,EAAc9D,UAAWyG,CAAwB,EACpDhB,CAiBR,CAhBC,MAAO9R,GACP,IAQQ8S,EAMR,KAbEnI,CAAAA,GAAc3K,CAAC,GACc,MAA5BA,EAAEM,WAAWoL,YAAkD,MAA5B1L,EAAEM,WAAWoL,YAM3CoH,EACDpS,OAAAmS,OAAAnS,OAAAmS,OAAA,GAAAxC,CAAiB,EACpB,CAAAyB,UAAW,CAAE7G,cAAa,CAAA,CAA6B,CAAA,EAEzDO,MAAMhF,EAAI2J,EAAc9D,UAAWyG,CAAwB,GAN3DtH,MAAMsE,GAAOK,EAAc9D,SAAS,EAQhCrM,CACP,CACH,GAtG8CmQ,EAAeO,CAAe,EAC/DA,EALL,MAAMtG,EAAcrJ,OAAM,cAM7B,CAdC,OAAOuP,CAeX,CAAC,EAKD,OAHkBiC,EACd/G,MAAM+G,EACLP,EAAMF,SAEb,CAyCA,SAASY,GACPrG,GAEA,OAAO2D,EAAO3D,EAAWiE,IACvB,IAIMmC,EAoF2BX,EAxFjC,GAAKU,GAAkBlC,CAAQ,EAK/B,OADMmC,EAAenC,EAASwB,UAsFuB,KAFpBA,EAnFDW,GAqFtBxH,eACV6G,EAAUc,YAAc/I,EAAqB3F,KAAKD,IAAG,EApF9CvD,OAAAmS,OAAAnS,OAAAmS,OAAA,GAAAvC,CAAQ,EACX,CAAAwB,UAAW,CAAE7G,cAAa,CAAA,CAC1B,CAAA,EAGGqF,EAXL,MAAMlG,EAAcrJ,OAAM,iBAY9B,CAAC,CACH,CAoCA,SAASyR,GACPnC,GAEA,OACwBhK,KAAAA,IAAtBgK,GACgE,IAAhEA,EAAkBG,kBAEtB,CCnJOlH,eAAeyJ,GACpB5C,EACAmC,EAAe,CAAA,GAEf,IAAMU,EAAoB7C,EAKpB2B,GAJNtG,MAaI4E,EAFIA,GAAwB5E,MAAM0E,EAXC8C,CAWiC,GAA3C,sBAI3BxH,CAAAA,MAAM4E,GAXU5E,MAAM6G,EAAiBW,EAAmBV,CAAY,GACxE,OAAOR,EAAU9G,KACnB,CCWA,SAASiI,EAAqBC,GAC5B,OAAO9I,EAAcrJ,OAA4C,4BAAA,CAC/DmS,UAAAA,CACD,CAAA,CACH,CC3BA,IAAMC,GAAqB,gBAGrBC,GAAkD,IAGtD,IAAMC,EAAMC,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAWrD,MANqD,KACnDkC,EACAhH,WDpB6BgH,IAC/B,GAAI,CAACA,GAAO,CAACA,EAAIG,QACf,MAAMP,EAAqB,mBAAmB,EAGhD,GAAI,CAACI,EAAI5S,KACP,MAAMwS,EAAqB,UAAU,EAIvC,IAMWQ,EAAX,IAAWA,IANsC,CAC/C,YACA,SACA,SAIA,GAAI,CAACJ,EAAIG,QAAQC,GACf,MAAMR,EAAqBQ,CAAO,EAItC,MAAO,CACL1F,QAASsF,EAAI5S,KACboK,UAAWwI,EAAIG,QAAQ3I,UACvBkB,OAAQsH,EAAIG,QAAQzH,OACpBiC,MAAOqF,EAAIG,QAAQxF,MAEvB,GCbqCqF,CAAG,EAMpCtC,yBAL+B2C,GAAAA,aAAaL,EAAK,WAAW,EAM5DM,QAAS,IAAMhM,QAAQC,QAAS,EAGpC,EAEMgM,GAA6D,IAGjE,IAAMP,EAAMC,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAErD,IAAMhB,EAAgBuD,GAAAA,aAAaL,EAAKF,EAAkB,EAAEhC,aAAY,EAMxE,MAJ8D,CAC5D0C,MAAO,KC5BJvK,MAAqB6G,IAC1B,IAAM6C,EAAoB7C,EACpB,CAAEE,kBAAAA,EAAmBD,oBAAAA,CAAmB,EAAK5E,MAAM0E,EACvD8C,CAAiB,EAWnB,OARI5C,GAKFiC,EAAiBW,CAAiB,GAJdnK,MAAMxE,QAAQf,KAAK,EAOlC+M,EAAkBlD,GAC3B,GDauBgD,CAAa,EAChC4C,SAAU,GAA4BA,GAAS5C,EAAemC,CAAY,EAG9E,EAGEwB,GAAkBA,mBAChB,IAAI/R,EAAUoR,GAAoBC,GAAoC,QAAA,CAAA,EAExEU,GAAkBA,mBAChB,IAAI/R,EAtC4B,yBAwC9B6R,GAED,SAAA,CAAA,EExCLG,GAAAA,gBAAgBtT,EAAMsJ,CAAO,EAE7BgK,GAAAA,gBAAgBtT,EAAMsJ,EAAS,SAAkB,wCCYpCiK,GAAb7T,cACEK,KAASyT,UAAsB,EAOhC,CANC/L,iBAAiBgM,GACf1T,KAAKyT,UAAUE,KAAKD,CAAQ,CAC7B,CACDE,QACE5T,KAAKyT,UAAUI,QAAQH,GAAYA,EAAU,CAAA,CAC9C,CACF,CCrCM,IC+EM9J,EAAgB,IAAItJ,EAC/B,eACA,gBA5DqE,CACrEwT,sBAAiC,oCACjCC,sBACE,kFACFC,0BACE,mEACFC,uBACE,wDACFC,sBACE,+DACFC,eACE,8EACFC,cACE,mFACFC,cACE,iFACFC,iBACE,oFACFC,uBACE,mHAEFC,gBACE,iGAEFC,iBACE,mOAGFC,qBACE,kFAEFC,eACE,0EACFC,yBACE,iDACFC,oCACE,mEAyBmB,EChFvB,IAIMC,GAAwB,CAAC,IAAK,OAAQ,IAAK,MAAO,IAAK,YAEhDC,EACXpV,YACmBqV,EACAC,EARY,IAOZjV,KAAOgV,QAAPA,EACAhV,KAAMiV,OAANA,CACf,CAEJC,WACE,OAAOlV,KAAKiV,MACb,CAEDE,YACE,MAAqB,WAAjBnV,KAAKgV,SAG0D,GAA5DF,GAAsBM,QAAQpV,KAAKiV,OAAOI,YAAW,CAAE,CAC/D,CAEDC,WACE,GAAqB,WAAjBtV,KAAKgV,QACP,OAvB2B,EAyB7B3P,IAAIkQ,EAAM5K,OAAO3K,KAAKiV,MAAM,EAI5B,OAFEM,EADEC,MAAMD,CAAG,EA1BgB,EA6BtBA,CACR,CAEDE,YACE,OAAOzV,KAAKgV,OACb,CACF,CCkCMlM,eAAe4M,GAASC,GAC7B,IAAMC,EAAKvU,EAAmBsU,CAAY,EACpC,CAACE,EAA6BC,GAAoB9K,MAAM7D,QAAQgC,IAAI,CACxEyM,EAAGG,SAASC,+BAAgC,EAC5CJ,EAAGG,SAASE,oBAAqB,EAClC,EACD,MACE,CAAA,EAACJ,GACAA,EAA4BK,QAC5BL,EAA4BM,MAC7BN,EAA4BM,OAASL,KAMvC9K,MAAM7D,QAAQgC,IAAI,CAChByM,EAAGQ,cAAcC,gBAAgBR,EAA4BK,MAAM,EACnEN,EAAGG,SAASO,oBAAoBT,EAA4BM,IAAI,EACjE,EACM,CAAA,EACT,CASM,SAAUI,GAAkBZ,GAChC,IAAMC,EAAKvU,EAAmBsU,CAAY,EAM1C,OALKC,EAAGY,qBACNZ,EAAGY,mBAAqBZ,EAAGQ,cAAcK,gBAAe,EAAGrO,KAAK,KAC9DwN,EAAGc,0BAA4B,CAAA,CACjC,CAAC,GAEId,EAAGY,kBACZ,CAOO1N,eAAe6N,GAAYhB,GAChC,IAAMC,EAAKvU,EAAmBsU,CAAY,EAW1C,IAAMiB,EAAc,IAAIpD,GAExBnH,WAAWvD,UAET8N,EAAYhD,MAAK,CACnB,EAAGgC,EAAGiB,SAASC,kBAAkB,EAEjC,IFnD2BtX,EEmDrBuX,EAAgBnB,EAAGQ,cAAcY,iBAAgB,EACnDD,GACFnB,EAAGqB,QAAQ3U,MACT,wCAAwC2O,KAAKC,UAAU6F,CAAa,CAAG,EAI3E,IACE/L,MAAM4K,EAAGsB,QAAQ/F,MAAM,CACrBgG,kBAAmBvB,EAAGiB,SAASO,2BAC/BC,OAAQT,EACRG,cAAAA,CACD,CAAA,EAED/L,MAAM4K,EAAGQ,cAAckB,mBAAmB,SAAS,CAOpD,CANC,MAAO9X,GFlE4B+X,EEmEuC,iBAApEC,GFnEmBhY,EEmEYA,aFlEnBC,GAA+C,CAAC,IAA/BD,EAAEI,KAAKwV,QAAQmC,CAAS,EEmEvD,WACA,UAEJ,MADAvM,MAAM4K,EAAGQ,cAAckB,mBAAmBE,CAAe,EACnDhY,CACP,CACH,CAUM,SAAUiY,GAAO9B,GACrB,IAmHkB+B,EAAeC,EAnH3B/B,EAAKvU,EAAmBsU,CAAY,EAC1C,MAAOiC,CAkHWF,EAAW,GAAIC,EAAW,IAlHrCC,CACLhC,EAAGQ,cAAcyB,gBAAiB,EAClCjC,EAAGkC,eAiHE5X,OAAO6X,KAAI7X,OAAAmS,OAAAnS,OAAAmS,OAAA,GAAMqF,CAAI,EAAKC,CAAI,GAhHnCK,OAAO,CAACC,EAAYhX,KACpBgX,EAAWhX,GAAOiX,EAASvC,EAAc1U,CAAG,EACrCgX,GACN,EAA2B,CAChC,CA0DgB,SAAAC,EAASvC,EAA4B1U,GACnD,IAAM2U,EAAKvU,EAAmBsU,CAAY,EAOpCwC,GANDvC,EAAGc,2BACNd,EAAGqB,QAAQ3U,wCACyBrB,0CAChC,oFAAoF,EAGrE2U,EAAGQ,cAAcyB,gBAAe,GACrD,OAAIM,GAAsCtS,KAAAA,IAAtBsS,EAAalX,GACxB,IAAImX,EAAU,SAAUD,EAAalX,EAAI,EACvC2U,EAAGkC,eAA2CjS,KAAAA,IAA1B+P,EAAGkC,cAAc7W,GACvC,IAAImX,EAAU,UAAWjX,OAAOyU,EAAGkC,cAAc7W,EAAI,CAAC,GAE/D2U,EAAGqB,QAAQ3U,yCAC0BrB,MACjC,6DAA6D,EAE1D,IAAImX,EAAU,QAAQ,EAC/B,OC/OaC,GACX1Y,YACmB2Y,EACAC,EACAC,EACAC,GAHAzY,KAAMsY,OAANA,EACAtY,KAAOuY,QAAPA,EACAvY,KAAYwY,aAAZA,EACAxY,KAAMyY,OAANA,CACf,CAWJC,kBACEvB,EACAwB,GAGA,IAQMD,EARN,OAAKC,GAMCC,EAAiBlV,KAAKD,IAAG,EAAKkV,EAIpC3Y,KAAKyY,OAAOnW,MACV,kDACwBsW,oEACyCzB,uBAL7DuB,EAAoBE,GAAkBzB,IAMF,EAGnCuB,IAhBL1Y,KAAKyY,OAAOnW,MAAM,8CAA8C,EACzD,CAAA,EAgBV,CAED6O,YAAYrJ,GAEV,GAAM,CAAC6Q,EAAoC9C,GACzC7K,MAAM7D,QAAQgC,IAAI,CAChBnJ,KAAKuY,QAAQM,sCAAuC,EACpD7Y,KAAKuY,QAAQvC,+BAAgC,EAC9C,EAGH,GACEH,GACA7V,KAAK0Y,kBACH5Q,EAAQqP,kBACRwB,CAAkC,EAGpC,OAAO9C,EAKT/N,EAAQqO,KACNN,GAA+BA,EAA4BM,KAGvD5L,EAAWS,MAAMhL,KAAKsY,OAAOnH,MAAMrJ,CAAO,EAI1CgR,EAAoB,CAExB9Y,KAAKwY,aAAaO,sCAAsCrV,KAAKD,IAAG,CAAE,GAYpE,OATwB,MAApB8G,EAASc,QAEXyN,EAAkBnF,KAChB3T,KAAKuY,QAAQS,+BAA+BzO,CAAQ,CAAC,EAIzDS,MAAM7D,QAAQgC,IAAI2P,CAAiB,EAE5BvO,CACR,CACF,OCnEY0O,GACXtZ,YACmBuZ,EACAlI,EACAmI,EACA9O,EACAkB,EACAiC,GALAxN,KAAqBkZ,sBAArBA,EACAlZ,KAAUgR,WAAVA,EACAhR,KAASmZ,UAATA,EACAnZ,KAASqK,UAATA,EACArK,KAAMuL,OAANA,EACAvL,KAAKwN,MAALA,CACf,CAWJ2D,YAAYrJ,GACV,IC9CFsR,ED8CQ,CAACC,EAAgBC,GAAqBtO,MAAM7D,QAAQgC,IAAI,CAC5DnJ,KAAKkZ,sBAAsB7F,MAAO,EAClCrT,KAAKkZ,sBAAsB3G,SAAU,EACtC,EAMKgH,KAHJC,OAAOC,iCACP,6DAEoCzZ,KAAKqK,wBAAwBrK,KAAKmZ,uBAAuBnZ,KAAKuL,OAE9FQ,EAAU,CACdN,eAAgB,mBAChBiO,mBAAoB,OAGpBC,gBAAiB7R,EAAQqO,MAAQ,KAG7ByD,EAAgC,CAEpCC,YAAa7Z,KAAKgR,WAClB8I,gBAAiBT,EACjBU,sBAAuBT,EACvBU,OAAQha,KAAKwN,MACbyM,eCvEJb,EAAuCjJ,WAIlB+J,WAAad,EAAkBc,UAAU,IAG5Dd,EAAkBe,SDiEhBC,eAAgBtS,EAAQiP,eAIpB/D,EAAU,CACdpP,OAAQ,OACRmI,QAAAA,EACA0E,KAAMQ,KAAKC,UAAU0I,CAAW,GAI5BS,EAAelJ,MAAMoI,EAAKvG,CAAO,EACjCsH,EAAiB,IAAInT,QAAQ,CAACoT,EAAUlT,KAE5CS,EAAQuP,OAAO3P,iBAAiB,KAE9B,IAAM5E,EAAQ,IAAIpD,MAAM,4BAA4B,EACpDoD,EAAM7C,KAAO,aACboH,EAAOvE,CAAK,CACd,CAAC,CACH,CAAC,EAEDuC,IAAIkF,EACJ,IACES,MAAM7D,QAAQqT,KAAK,CAACH,EAAcC,EAAe,EACjD/P,EAAWS,MAAMqP,CASlB,CARC,MAAOI,GACPpV,IAAIkS,EAAoC,uBAIxC,KAHuC,gBAAlCkD,MAAAA,EAAa,KAAA,EAAbA,EAAyBxa,QAC5BsX,EAAoC,iBAEhC3N,EAAcrJ,OAAOgX,EAAW,CACpCmD,qBAAuBD,MAAAA,EAAA,KAAA,EAAAA,EAAyB5a,OACjD,CAAA,CACF,CAEDwF,IAAIgG,EAASd,EAASc,OAGhBsP,EAAepQ,EAASwB,QAAQxG,IAAI,MAAM,GAAKM,KAAAA,EAErDR,IAAI6Q,EACA0E,EAIJ,GAAwB,MAApBrQ,EAASc,OAAgB,CAC3BhG,IAAIwV,EACJ,IACEA,EAAe7P,MAAMT,EAASU,MAK/B,CAJC,MAAOwP,GACP,MAAM7Q,EAAcrJ,OAA8B,qBAAA,CAChDma,qBAAuBD,MAAAA,EAAA,KAAA,EAAAA,EAAyB5a,OACjD,CAAA,CACF,CACDqW,EAAS2E,EAAsB,QAC/BD,EAAQC,EAAoB,KAC7B,CAgBD,GAbc,+BAAVD,EACFvP,EAAS,IACU,cAAVuP,EACTvP,EAAS,IACU,gBAAVuP,GAAqC,iBAAVA,IAEpC1E,EAAS,IAOI,MAAX7K,GAA6B,MAAXA,EACpB,MAAMzB,EAAcrJ,OAA+B,eAAA,CACjDua,WAAYzP,CACb,CAAA,EAGH,MAAO,CAAEA,OAAAA,EAAQ8K,KAAMwE,EAAczE,OAAAA,CAAM,CAC5C,CACF,OE3FY6E,GACXpb,YACmB2Y,EACAC,GADAvY,KAAMsY,OAANA,EACAtY,KAAOuY,QAAPA,CACf,CAEJpH,YAAYrJ,GACV,IAAMkT,EAAmB,MAAOhb,KAAKuY,QAAQ0C,oBAAmB,GAAO,CACrEC,aAAc,EACdC,sBAAuBzX,KAAKD,IAAK,GAGnC,OAAOzD,KAAKob,aAAatT,EAASkT,CAAgB,CACnD,CAODI,mBACEtT,EACA,CAAEqT,sBAAAA,EAAuBD,aAAAA,CAAY,GAxEzB,IACd7D,EACA8D,ECqBME,EDtBNhE,EA4E4BvP,EAAQuP,OA3EpC8D,EA2E4CA,EAA1CnQ,MAzEK,IAAI7D,QAAQ,CAACC,EAASC,KAE3B,IAAMiU,EAAgBC,KAAKC,IAAIL,EAAwBzX,KAAKD,IAAG,EAAI,CAAC,EAEpE,IAAMgY,EAAUpP,WAAWjF,EAASkU,CAAa,EAGjDjE,EAAO3P,iBAAiB,KACtBgU,aAAaD,CAAO,EAGpBpU,EACEuC,EAAcrJ,OAAiC,iBAAA,CAC7C4a,sBAAAA,CACD,CAAA,CAAC,CAEN,CAAC,CACH,CAAC,EA0DC,IACE,IAAM5Q,EAAWS,MAAMhL,KAAKsY,OAAOnH,MAAMrJ,CAAO,EAKhD,OAFAkD,MAAMhL,KAAKuY,QAAQoD,yBAEZpR,CAiBR,CAhBC,MAAO/K,GACP,IA3DoBA,IACxB,IAKMsb,EALN,OAAMtb,aAAaC,GAAmBD,EAAEM,aAQvB,OAHXgb,EAAanQ,OAAOnL,EAAEM,WAAuB,UAAC,IAInC,MAAfgb,GACe,MAAfA,GACe,MAAfA,EAEJ,GA6C4Btb,CAAU,EAchC,OATMwb,EAAmB,CACvBG,sBACEzX,KAAKD,OChFbmY,EA3B6B,EAgCvBC,EAtCwB,IAsCSN,KAAKO,IAAIF,ED2EJV,CC3E+B,EAIrEG,EAAaE,KAAKQ,MAnBG,GAuBvBF,GAGCN,KAAKS,OAAQ,EAAG,IACjB,CAAC,EAIET,KAAKU,IAzCkB,MAyCIJ,EAAgBR,CAAU,GD4DtDH,aAAcA,EAAe,GAI/BlQ,MAAMhL,KAAKuY,QAAQ2D,oBAAoBlB,CAAgB,EAEhDhb,KAAKob,aAAatT,EAASkT,CAAgB,EAbhD,MAAMxb,CAcT,CACF,CACF,OE3GY2c,GAoBXC,sBACE,OAAOpc,KAAKoW,cAAcyC,sCAAuC,GAAI,CAAC,CACvE,CAEDrB,sBACE,OAAOxX,KAAKoW,cAAciG,mBAAkB,GAAM,cACnD,CAED1c,YAEWkT,EAOAqE,EAIAd,EAIAL,EAIAkB,GAnBAjX,KAAG6S,IAAHA,EAOA7S,KAAOkX,QAAPA,EAIAlX,KAAaoW,cAAbA,EAIApW,KAAQ+V,SAARA,EAIA/V,KAAOiX,QAAPA,EA5CXjX,KAAyB0W,0BAAG,CAAA,EAQ5B1W,KAAA6W,SAAiC,CAC/BC,mBAtBiC,IAuBjCM,2BAtBiC,OAyBnCpX,KAAa8X,cAAiD,EAgC1D,CACL,CC7DD,SAASwE,EAAgBvN,EAAcwI,GACrC,IAAMkD,EAAiB1L,EAAMvJ,OAAsB1C,OAAS+C,KAAAA,EAC5D,OAAO+D,EAAcrJ,OAAOgX,EAAW,CACrCmD,qBAAsBD,IAAkBA,MAAAA,EAAA,KAAA,EAAAA,EAAyB5a,QAClE,CAAA,CACH,CAYO,IAAM0c,EAAsB,4BAqEbC,GACpBH,qBACE,OAAOrc,KAAKuF,IAAiB,mBAAmB,CACjD,CAED+R,mBAAmBjM,GACjB,OAAOrL,KAAKgG,IAAiB,oBAAqBqF,CAAM,CACzD,CAIDwN,wCACE,OAAO7Y,KAAKuF,IAAY,wCAAwC,CACjE,CAEDwT,sCAAsC0D,GACpC,OAAOzc,KAAKgG,IACV,yCACAyW,CAAS,CAEZ,CAEDzG,iCACE,OAAOhW,KAAKuF,IAAmB,gCAAgC,CAChE,CAEDyT,+BAA+BzO,GAC7B,OAAOvK,KAAKgG,IAAmB,iCAAkCuE,CAAQ,CAC1E,CAEDsN,kBACE,OAAO7X,KAAKuF,IAAgC,eAAe,CAC5D,CAED8Q,gBAAgBH,GACd,OAAOlW,KAAKgG,IAAgC,gBAAiBkQ,CAAM,CACpE,CAEDD,sBACE,OAAOjW,KAAKuF,IAAY,oBAAoB,CAC7C,CAED+Q,oBAAoBoG,GAClB,OAAO1c,KAAKgG,IAAY,qBAAsB0W,CAAI,CACnD,CAEDzB,sBACE,OAAOjb,KAAKuF,IAAsB,mBAAmB,CACtD,CAED2W,oBAAoBS,GAClB,OAAO3c,KAAKgG,IAAsB,oBAAqB2W,CAAQ,CAChE,CAEDhB,yBACE,OAAO3b,KAAKuP,OAAO,mBAAmB,CACvC,CAEDyH,mBACE,OAAOhX,KAAKuF,IAAmB,gBAAgB,CAChD,CAQF,OAEYqX,WAAyBJ,GAMpC7c,YACmB6N,EACAD,EACA4L,EACA0D,GAtHL,IACP,IAAI1V,QAAQ,CAACC,EAASC,KAC3B,IACE,IAAMS,EAAUvI,UAAUsP,KAhChB,yBACG,CA+BqC,EAClD/G,EAAQgV,QAAU/N,IAChB1H,EAAOiV,EAAgBvN,EAAK,cAAyB,CAAA,CACvD,EACAjH,EAAQiV,UAAYhO,IAClB3H,EAAS2H,EAAMvJ,OAA4B2C,MAAM,CACnD,EACAL,EAAQkV,gBAAkBjO,IACxB,IAAMG,EAAMH,EAAMvJ,OAA4B2C,OAQvC,IADC4G,EAAMC,YAEVE,EAAGC,kBAAkBoN,EAAqB,CACxCU,QAAS,cACV,CAAA,CAEP,CAOD,CANC,MAAOna,GACPuE,EACEuC,EAAcrJ,OAA+B,eAAA,CAC3Cma,qBAAuB5X,MAAAA,EAAA,KAAA,EAAAA,EAAiBjD,OACzC,CAAA,CAAC,CAEL,CACH,CAAC,GAsF8C,GAE7CE,QALiBC,KAAKwN,MAALA,EACAxN,KAAOuN,QAAPA,EACAvN,KAASmZ,UAATA,EACAnZ,KAAa6c,cAAbA,CAGlB,CAEDK,uBAAuBnG,GACrB,IACM1Q,GADK2E,MAAMhL,KAAK6c,eACCxW,YAAY,CAACkW,GAAsB,WAAW,EAK/DY,EAAiBC,GACrBrG,EALoB/L,MAAMhL,KAAKqd,mBAC/B,iBACAhX,CAAW,GAIM,EAAE,EAOrB,OALA2E,MAAMhL,KAAKsd,mBACT,iBACAH,EACA9W,CAAW,EAEN8W,CACR,CASDE,yBACEpc,EACAoF,GAEA,OAAO,IAAIc,QAAQ,CAACC,EAASC,KAC3B,IAAMvB,EAAcO,EAAYP,YAAYyW,CAAmB,EACzDgB,EAAevd,KAAKwd,mBAAmBvc,CAAG,EAChD,IACE,IAAM6G,EAAUhC,EAAYP,IAAIgY,CAAY,EAC5CzV,EAAQgV,QAAU/N,IAChB1H,EAAOiV,EAAgBvN,EAAK,aAAwB,CAAA,CACtD,EACAjH,EAAQiV,UAAYhO,IAClB,IAAM5G,EAAU4G,EAAMvJ,OAAsB2C,OAE1Cf,EADEe,EACMA,EAAOjH,MAEP2E,KAAAA,CAFY,CAIxB,CAOD,CANC,MAAOrG,GACP6H,EACEuC,EAAcrJ,OAA8B,cAAA,CAC1Cma,qBAAuBlb,MAAAA,EAAA,KAAA,EAAAA,EAAaK,OACrC,CAAA,CAAC,CAEL,CACH,CAAC,CACF,CAUDyd,yBACErc,EACAC,EACAmF,GAEA,OAAO,IAAIc,QAAQ,CAACC,EAASC,KAC3B,IAAMvB,EAAcO,EAAYP,YAAYyW,CAAmB,EACzDgB,EAAevd,KAAKwd,mBAAmBvc,CAAG,EAChD,IACE,IAAM6G,EAAUhC,EAAYuJ,IAAI,CAC9BkO,aAAAA,EACArc,MAAAA,CACD,CAAA,EACD4G,EAAQgV,QAAU,IAChBzV,EAAOiV,EAAgBvN,EAAK,aAAwB,CAAA,CACtD,EACAjH,EAAQiV,UAAY,KAClB3V,GACF,CAOD,CANC,MAAO5H,GACP6H,EACEuC,EAAcrJ,OAA8B,cAAA,CAC1Cma,qBAAuBlb,MAAAA,EAAA,KAAA,EAAAA,EAAaK,OACrC,CAAA,CAAC,CAEL,CACH,CAAC,CACF,CAED0F,UAAatE,GACX,IACMoF,GADK2E,MAAMhL,KAAK6c,eACCxW,YAAY,CAACkW,GAAsB,UAAU,EACpE,OAAOvc,KAAKqd,mBAAsBpc,EAAKoF,CAAW,CACnD,CAEDL,UAAa/E,EAAoCC,GAC/C,IACMmF,GADK2E,MAAMhL,KAAK6c,eACCxW,YAAY,CAACkW,GAAsB,WAAW,EACrE,OAAOvc,KAAKsd,mBAAsBrc,EAAKC,EAAOmF,CAAW,CAC1D,CAEDkJ,aAAatO,GACX,IAAMiO,EAAKlE,MAAMhL,KAAK6c,cACtB,OAAO,IAAI1V,QAAQ,CAACC,EAASC,KAC3B,IACMvB,EADcoJ,EAAG7I,YAAY,CAACkW,GAAsB,WAAW,EACrCzW,YAAYyW,CAAmB,EACzDgB,EAAevd,KAAKwd,mBAAmBvc,CAAG,EAChD,IACE,IAAM6G,EAAUhC,EAAYyJ,OAAOgO,CAAY,EAC/CzV,EAAQgV,QAAU,IAChBzV,EAAOiV,EAAgBvN,EAAK,gBAA2B,CAAA,CACzD,EACAjH,EAAQiV,UAAY,KAClB3V,GACF,CAOD,CANC,MAAO5H,GACP6H,EACEuC,EAAcrJ,OAAiC,iBAAA,CAC7Cma,qBAAuBlb,MAAAA,EAAA,KAAA,EAAAA,EAAaK,OACrC,CAAA,CAAC,CAEL,CACH,CAAC,CACF,CAGD2d,mBAAmBvc,GACjB,MAAO,CAACjB,KAAKwN,MAAOxN,KAAKuN,QAASvN,KAAKmZ,UAAWlY,GAAKwc,MACxD,CACF,OAEYC,WAAwBlB,GAArC7c,kCACUK,KAAOuY,QAA+B,EAyB/C,CAvBChT,UAAatE,GACX,OAAOkG,QAAQC,QAAQpH,KAAKuY,QAAQtX,EAAS,CAC9C,CAED+E,UAAa/E,EAAoCC,GAE/C,OADAlB,KAAKuY,QAAQtX,GAAOC,EACbiG,QAAQC,QAAQvB,KAAAA,CAAS,CACjC,CAED0J,aAAatO,GAEX,OADAjB,KAAKuY,QAAQtX,GAAO4E,KAAAA,EACbsB,QAAQC,SAChB,CAED8V,uBAAuBnG,GACrB,IAAM4G,EAAiB3d,KAAKuY,QAAwB,gBAClD,GAKF,OAJAvY,KAAKuY,QAAwB,eAAI6E,GAC/BrG,EACA4G,CAAa,EAERxW,QAAQC,QAAQpH,KAAKuY,QAAwB,cAAkB,CACvE,CACF,CAED,SAAS6E,GACPrG,EACA4G,GAEA,IAAMC,EACD1d,OAAAmS,OAAAnS,OAAAmS,OAAA,GAAAsL,CAAa,EACb5G,CAAa,EAIZoG,EAAiBjd,OAAO2d,YAC5B3d,OAAO4d,QAAQF,CAAe,EAC3BG,OAAO,CAAA,CAAE/c,CAAGgd,KAAa,OAANA,CAAU,EAC7BC,IAAI,CAAA,CAAEC,EAAGF,KAGS,UAAb,OAAOA,EACF,CAACE,EAAGF,EAAEG,SAAU,GAElB,CAACD,EAAGF,EACZ,CAAC,EAIN,GV9WkD,IU+WhD9d,OAAO6X,KAAKoF,CAAc,EAAEiB,OAE5B,MAAMxU,EAAcrJ,OAAoD,oCAAA,CACtE8d,WVlX8C,GUmX/C,CAAA,EAEH,OAAOlB,CACT,OChXamB,GACX3e,YAA6B4Y,GAAAvY,KAAOuY,QAAPA,CAAoB,CAajD8D,qBACE,OAAOrc,KAAKwX,eACb,CAEDqB,wCACE,OAAO7Y,KAAK2Y,kCACb,CAEDd,kBACE,OAAO7X,KAAKmY,YACb,CAEDnB,mBACE,OAAOhX,KAAK+W,aACb,CAKDN,wBACE,IAAM8H,EAAyBve,KAAKuY,QAAQ8D,mBAAkB,EACxDmC,EACJxe,KAAKuY,QAAQM,sCAAqC,EAC9C4F,EAAsBze,KAAKuY,QAAQV,gBAAe,EAClD6G,EAAuB1e,KAAKuY,QAAQvB,iBAAgB,EAQpDQ,EAAkBxM,MAAMuT,EAKxB5F,GAJFnB,IACFxX,KAAKwX,gBAAkBA,GAIvBxM,MAAMwT,GAMFrG,GALFQ,IACF3Y,KAAK2Y,mCACHA,GAGiB3N,MAAMyT,GAKrB1H,GAJFoB,IACFnY,KAAKmY,aAAeA,GAGAnN,MAAM0T,GACxB3H,IACF/W,KAAK+W,cAAgBA,EAExB,CAKDO,mBAAmBjM,GAEjB,OADArL,KAAKwX,gBAAkBnM,EAChBrL,KAAKuY,QAAQjB,mBAAmBjM,CAAM,CAC9C,CAED0N,sCACE4F,GAGA,OADA3e,KAAK2Y,mCAAqCgG,EACnC3e,KAAKuY,QAAQQ,sCAAsC4F,CAAe,CAC1E,CAEDtI,gBAAgB8B,GAEd,OADAnY,KAAKmY,aAAeA,EACbnY,KAAKuY,QAAQlC,gBAAgB8B,CAAY,CACjD,CAED+E,uBAAuBnG,GACrB/W,KAAK+W,cAAgB/L,MAAMhL,KAAKuY,QAAQ2E,iBAAiBnG,CAAa,CACvE,CACF,CC5DMjO,eAAe8V,KACpB,GAAI,CAACtf,EAAoB,EACvB,MAAO,CAAA,EAGT,IAEE,OAD8B0L,MrCgJzB,IAAI7D,QAAQ,CAACC,EAASC,KAC3B,IACEhC,IAAIwZ,EAAoB,CAAA,EAClBC,EACJ,0DACIhX,EAAU8E,KAAKrN,UAAUsP,KAAKiQ,CAAa,EACjDhX,EAAQiV,UAAY,KAClBjV,EAAQK,OAAO+F,QAEV2Q,GACHjS,KAAKrN,UAAUwf,eAAeD,CAAa,EAE7C1X,EAAQ,CAAA,CAAI,CACd,EACAU,EAAQkV,gBAAkB,KACxB6B,EAAW,CAAA,CACb,EAEA/W,EAAQgV,QAAU,WAChBzV,GAAO,OAAAgG,EAAAvF,EAAQhF,OAAK,KAAA,EAAAuK,EAAExN,UAAW,EAAE,CACrC,CAGD,CAFC,MAAOiD,GACPuE,EAAOvE,CAAK,CACb,CACH,CAAC,CqCpKA,CAFC,MAAOA,GACP,MAAO,CAAA,CACR,CACH,CCvBEwQ,sBACE,IAAI/R,Eb5ByB,gBauC/B,SACEuR,EACA,CAAEE,QAAAA,IAIF,IAAMH,EAAMC,EAAUC,YAAY,KAAK,EAAEpC,aAAY,EAE/ChB,EAAgBmD,EACnBC,YAAY,wBAAwB,EACpCpC,eAGG,CAAEtG,UAAAA,EAAWkB,OAAAA,EAAQiC,MAAAA,CAAK,EAAKqF,EAAIG,QACzC,GAAI,CAAC3I,EACH,MAAMT,EAAcrJ,OAAM,2BAE5B,GAAI,CAACgL,EACH,MAAM3B,EAAcrJ,OAAM,wBAE5B,GAAI,CAACiN,EACH,MAAM5D,EAAcrJ,OAAM,uBAE5B,IAAM4Y,GAAYnG,MAAAA,EAAA,KAAA,EAAAA,EAASgM,aAAc,WAEnCzG,EAAUjZ,EAAsB,EAClC,IAAIsd,GAAiBpP,EAAOqF,EAAI5S,KAAMkZ,CAAS,EAC/C,IAAIuE,GACFlF,EAAe,IAAI8F,GAAa/F,CAAO,EAEvCE,EAAS,IAAI3U,EAAOmb,CAAW,EAM/BC,GAFNzG,EAAOjV,SAAW2b,EAAiBpc,MAEhB,IAAIkW,GACrBtJ,EAEAyP,GAAAA,YACAjG,EACA9O,EACAkB,EACAiC,CAAK,GAED6R,EAAiB,IAAItE,GAAemE,EAAY3G,CAAO,EACvD+G,EAAgB,IAAIjH,GACxBgH,EACA9G,EACAC,EACAC,CAAM,EAGF8G,EAAuB,IAAIC,GAC/B3M,EACAyM,EACA9G,EACAD,EACAE,CAAM,EAOR,OAFAlC,GAAkBgJ,CAAoB,EAE/BA,CACR,EAzEE,UAACvd,qBAAqB,CAAA,CAAI,CAAC,EAG9BuR,mBAAgB0L,SAAoB,EAEpC1L,GAAAA,gBAAgB0L,UAAsB,SAAkB,QCZ7CQ,GAGX9f,YAAmBkT,EAA2BvR,GAA3BtB,KAAG6S,IAAHA,EAA2B7S,KAASsB,UAATA,CAA2B,CAEzEwW,oBACE,OAAO9X,KAAKsB,UAAUwW,aACvB,CAEDA,kBAAkB5W,GAChBlB,KAAKsB,UAAUwW,cAAgB5W,CAChC,CAEDkb,sBACE,OAAOpc,KAAKsB,UAAU8a,eACvB,CAED5E,sBACE,OAAOxX,KAAKsB,UAAUkW,eACvB,CAEDX,eACE,OAAO7W,KAAKsB,UAAUuV,QACvB,CAEDA,aAAa3V,GACXlB,KAAKsB,UAAUuV,SAAW3V,CAC3B,CAEDwU,WACE,OAAOA,GAAS1V,KAAKsB,SAAS,CAC/B,CAEDiV,oBACE,OAAOA,GAAkBvW,KAAKsB,SAAS,CACxC,CAOD6P,QACE,OAAOwF,GAAY3W,KAAKsB,SAAS,CAClC,CAEDoe,mBACE,OFnDG5W,MACL6M,IAGA3K,MAAM2L,GADNhB,EAAetU,EAAmBsU,CAAY,CAChB,EACvBD,GAASC,CAAY,IE8CF3V,KAAKsB,SAAS,CACvC,CAEDmW,SACE,OAAOA,GAAOzX,KAAKsB,SAAS,CAC7B,CAEDqe,WAAW1e,GACT,OXgHKiX,EAAS7W,EWhHIrB,KAAKsB,SXgHsB,EWhHXL,CXgHiB,EAAEkU,WW/GtD,CAEDyK,UAAU3e,GACR,OX4HKiX,EAAS7W,EW5HGrB,KAAKsB,SX4HuB,EW5HZL,CX4HkB,EAAEqU,UW3HtD,CAEDuK,UAAU5e,GACR,OXuIKiX,EAAS7W,EWvIGrB,KAAKsB,SXuIuB,EWvIZL,CXuIkB,EAAEiU,UWtItD,CAEDgD,SAASjX,GACP,OAAOiX,EAASlY,KAAKsB,UAAWL,CAAG,CACpC,CAIDmD,YAAYZ,GACVY,IXwKFuR,EWxKc3V,KAAKsB,UX2KbsU,EAAKvU,EAAmBsU,CAAY,EAC1C,OAAQnS,GACN,IAAK,QACHoS,EAAGqB,QAAQzT,SAAW2b,EAAiB5c,MACvC,MACF,IAAK,SACHqT,EAAGqB,QAAQzT,SAAW2b,EAAiBlc,OACvC,MACF,QACE2S,EAAGqB,QAAQzT,SAAW2b,EAAiBpc,KAC1C,CWpLA,CACF,CjCzED,SAAS+c,GACPhN,EACA,CAAEiN,mBAAoB5G,CAAS,GAE/B,IAAMtG,EAAMC,EAAUC,YAAY,YAAY,EAAEpC,aAAY,EAEtDgF,EAAe7C,EAAUC,YAAY,eAAe,EAAEpC,aAAa,CACvEqP,WAAY7G,CACb,CAAA,EAED,OAAO,IAAIsG,GAAuB5M,EAAK8C,CAAY,CACrD,EA1BEhM,EA4ByBsW,WA1BRC,SAASC,kBACxB,IAAI5e,EACF,sBACAue,GAED,QAAA,EACE9d,qBAAqB,CAAA,CAAI,EACzBC,gBAAgB,CAAE2c,YAAAA,EAAa,CAAA,CAAC,EAGrCjV,EAAiB4J,yDAAoC"}