((e,t)=>{"object"==typeof exports&&"undefined"!=typeof module?t(require("@firebase/app-compat"),require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app-compat","@firebase/app"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).firebase,e.firebase.INTERNAL.modularAPIs)})(this,function(U,F){try{!(function(){function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t,n=e(U);function i(e){try{return(e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e).endsWith(".cloudworkstations.dev")}catch(e){return!1}}let r={};let a=!1;function s(e,t){if("undefined"!=typeof window&&"undefined"!=typeof document&&i(window.location.host)&&r[e]!==t&&!r[e]&&!a){r[e]=t;let l="__firebase__banner";let u=0<(()=>{var e,t={prod:[],emulator:[]};for(e of Object.keys(r))(r[e]?t.emulator:t.prod).push(e);return t})().prod.length;function d(e){return"__firebase__banner__"+e}function p(){var e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{var e;a=!0,(e=document.getElementById(l))&&e.remove()},e}function n(){var e,t,n=(e=>{let t=document.getElementById(e),n=!1;return t||((t=document.createElement("div")).setAttribute("id",e),n=!0),{created:n,element:t}})(l),r=d("text"),i=document.getElementById(r)||document.createElement("span"),a=d("learnmore"),s=document.getElementById(a)||document.createElement("a"),o=d("preprendIcon"),c=document.getElementById(o)||document.createElementNS("http://www.w3.org/2000/svg","svg");n.created&&(n=n.element,(t=n).style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",(t=s).setAttribute("id",a),t.innerText="Learn more",t.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",t.setAttribute("target","__blank"),t.style.paddingLeft="5px",t.style.textDecoration="underline",a=p(),t=o,(e=c).setAttribute("width","24"),e.setAttribute("id",t),e.setAttribute("height","24"),e.setAttribute("viewBox","0 0 24 24"),e.setAttribute("fill","none"),e.style.marginLeft="-6px",n.append(c,i,s,a),document.body.appendChild(n)),u?(i.innerText="Preview backend disconnected.",c.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(c.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,i.innerText="Preview backend running in this workspace."),i.setAttribute("id",r)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",n):n()}}class o extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,o.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,c.prototype.create)}}class c{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){var r,n=t[0]||{},i=this.service+"/"+e,a=this.errors[e],a=a?(r=n,a.replace(l,(e,t)=>{var n=r[t];return null!=n?String(n):`<${t}?>`})):"Error",a=this.serviceName+`: ${a} (${i}).`;return new o(i,a,n)}}let l=/\{\$([^}]+)}/g;function u(e){return e&&e._delegate?e._delegate:e}class d{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}let p="type.googleapis.com/google.protobuf.Int64Value",h="type.googleapis.com/google.protobuf.UInt64Value";function m(e,t){var n,r={};for(n in e)e.hasOwnProperty(n)&&(r[n]=t(e[n]));return r}function f(e){if(null==e)return null;if("number"==typeof(e=e instanceof Number?e.valueOf():e)&&isFinite(e))return e;if(!0===e||!1===e)return e;if("[object String]"===Object.prototype.toString.call(e))return e;if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map(e=>f(e));if("function"==typeof e||"object"==typeof e)return m(e,e=>f(e));throw new Error("Data cannot be encoded in JSON: "+e)}function g(e){if(null==e)return e;if(e["@type"])switch(e["@type"]){case p:case h:var t=Number(e.value);if(isNaN(t))throw new Error("Data cannot be decoded from JSON: "+e);return t;default:throw new Error("Data cannot be decoded from JSON: "+e)}return Array.isArray(e)?e.map(e=>g(e)):"function"==typeof e||"object"==typeof e?m(e,e=>g(e)):e}let v="functions",y={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class w extends o{constructor(e,t,n){super(v+"/"+e,t||""),this.details=n,Object.setPrototypeOf(this,w.prototype)}}function C(e,t){let n=(e=>{if(200<=e&&e<300)return"ok";switch(e){case 0:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 500:return"internal";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"})(e),r=n,i=void 0;try{var a=t&&t.error;if(a){let e=a.status;if("string"==typeof e){if(!y[e])return new w("internal","internal");n=y[e],r=e}var s=a.message;"string"==typeof s&&(r=s),void 0!==(i=a.details)&&(i=g(i))}}catch(e){}return"ok"===n?null:new w(n,r,i)}class b{constructor(e,t,n,r){this.app=e,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,F._isFirebaseServerApp(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.auth=t.getImmediate({optional:!0}),this.messaging=n.getImmediate({optional:!0}),this.auth||t.get().then(e=>this.auth=e,()=>{}),this.messaging||n.get().then(e=>this.messaging=e,()=>{}),this.appCheck||null!=r&&r.get().then(e=>this.appCheck=e,()=>{})}async getAuthToken(){if(this.auth)try{var e=await this.auth.getToken();return null==e?void 0:e.accessToken}catch(e){}}async getMessagingToken(){if(this.messaging&&"Notification"in self&&"granted"===Notification.permission)try{return await this.messaging.getToken()}catch(e){}}async getAppCheckToken(e){var t;return this.serverAppAppCheckToken||(!this.appCheck||(t=e?await this.appCheck.getLimitedUseToken():await this.appCheck.getToken()).error?null:t.token)}async getContext(e){return{authToken:await this.getAuthToken(),messagingToken:await this.getMessagingToken(),appCheckToken:await this.getAppCheckToken(e)}}}let k="us-central1",T=/^data: (.*?)(?:\n|$)/;class E{constructor(e,t,n,r,i=k,a=(...e)=>fetch(...e)){this.app=e,this.fetchImpl=a,this.emulatorOrigin=null,this.contextProvider=new b(e,t,n,r),this.cancelAllRequests=new Promise(e=>{this.deleteService=()=>Promise.resolve(e())});try{var s=new URL(i);this.customDomain=s.origin+("/"===s.pathname?"":s.pathname),this.region=k}catch(e){this.customDomain=null,this.region=i}}_delete(){return this.deleteService()}_url(e){var t=this.app.options.projectId;return null!==this.emulatorOrigin?`${this.emulatorOrigin}/${t}/${this.region}/`+e:null!==this.customDomain?this.customDomain+"/"+e:`https://${this.region}-${t}.cloudfunctions.net/`+e}}function A(e,t,n){var r=i(t);e.emulatorOrigin=`http${r?"s":""}://${t}:`+n,r&&((async e=>(await fetch(e,{credentials:"include"})).ok)(e.emulatorOrigin),s("Functions",!0))}function I(i,a,s){var e=e=>{return e=e,n=s||{},r=(t=i)._url(a),N(t,r,e,n);var t,n,r};return e.stream=(e,t)=>{return e=e,t=t,r=(n=i)._url(a),P(n,r,e,t||{});var n,r},e}async function _(e,t){var n={},r=await e.contextProvider.getContext(t.limitedUseAppCheckTokens);return r.authToken&&(n.Authorization="Bearer "+r.authToken),r.messagingToken&&(n["Firebase-Instance-ID-Token"]=r.messagingToken),null!==r.appCheckToken&&(n["X-Firebase-AppCheck"]=r.appCheckToken),n}async function N(e,t,n,r){var i={data:n=f(n)},a=await _(e,r),s=(n=>{let r=null;return{promise:new Promise((e,t)=>{r=setTimeout(()=>{t(new w("deadline-exceeded","deadline-exceeded"))},n)}),cancel:()=>{r&&clearTimeout(r)}}})(r.timeout||7e4),i=await Promise.race([(async(e,t,n,r)=>{n["Content-Type"]="application/json";let i;try{i=await r(e,{method:"POST",body:JSON.stringify(t),headers:n})}catch(e){return{status:0,json:null}}let a=null;try{a=await i.json()}catch(e){}return{status:i.status,json:a}})(t,i,a,e.fetchImpl),s.promise,e.cancelAllRequests]);if(s.cancel(),!i)throw new w("cancelled","Firebase Functions instance was deleted.");a=C(i.status,i.json);if(a)throw a;if(!i.json)throw new w("internal","Response is not valid JSON object.");let o=i.json.data;if(void 0===(o=void 0===o?i.json.result:o))throw new w("internal","Response is missing data field.");return{data:g(o)}}async function P(e,t,n,r){var i={data:n=f(n)},a=await _(e,r);a["Content-Type"]="application/json",a.Accept="text/event-stream";let s;try{s=await e.fetchImpl(t,{method:"POST",body:JSON.stringify(i),headers:a,signal:null==r?void 0:r.signal})}catch(e){if(e instanceof Error&&"AbortError"===e.name){let e=new w("cancelled","Request was cancelled.");return{data:Promise.reject(e),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(e)}}}}}}let t=C(0,null);return{data:Promise.reject(t),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(t)}}}}}}let o,c;i=new Promise((e,t)=>{o=e,c=t}),null!=(a=null==r?void 0:r.signal)&&a.addEventListener("abort",()=>{var e=new w("cancelled","Request was cancelled.");c(e)}),a=s.body.getReader();let l=((s,a,o,c)=>{let l=(e,t)=>{var n=e.match(T);if(n){n=n[1];try{var r,i=JSON.parse(n);"result"in i?a(g(i.result)):"message"in i?t.enqueue(g(i.message)):"error"in i&&(r=C(0,i),t.error(r),o(r))}catch(e){e instanceof w&&(t.error(e),o(e))}}},u=new TextDecoder;return new ReadableStream({start(i){let a="";return async function n(){if(null!=c&&c.aborted){let e=new w("cancelled","Request was cancelled");return i.error(e),o(e),Promise.resolve()}try{let{value:t,done:e}=await s.read();if(e)a.trim()&&l(a.trim(),i),i.close();else{if(null==c||!c.aborted){let e=(a+=u.decode(t,{stream:!0})).split("\n");a=e.pop()||"";for(var r of e)r.trim()&&l(r.trim(),i);return n()}{let e=new w("cancelled","Request was cancelled");i.error(e),o(e),void await s.cancel()}}}catch(e){let t=e instanceof w?e:C(0,null);i.error(t),o(t)}}()},cancel(){return s.cancel()}})})(a,o,c,null==r?void 0:r.signal);return{stream:{[Symbol.asyncIterator](){let n=l.getReader();return{async next(){var{value:e,done:t}=await n.read();return{value:e,done:t}},async return(){return await n.cancel(),{done:!0,value:void 0}}}}},data:i}}let O="@firebase/functions",S="0.12.9";function x(e,t,n){A(u(e),t,n)}function L(e,t,n){return r=u(e),i=t,a=n,(s=e=>N(r,i,e,a||{})).stream=(e,t)=>P(r,i,e,t||{}),s;var r,i,a,s}F._registerComponent(new d(v,(e,{instanceIdentifier:t})=>{var n=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal"),i=e.getProvider("messaging-internal"),a=e.getProvider("app-check-internal");return new E(n,r,i,a,t)},"PUBLIC").setMultipleInstances(!0)),F.registerVersion(O,S,t),F.registerVersion(O,S,"esm2017");var D;class j{constructor(e,t){this.app=e,this._delegate=t,this._region=this._delegate.region,this._customDomain=this._delegate.customDomain}httpsCallable(e,t){return I(u(this._delegate),e,t)}httpsCallableFromURL(e,t){return L(this._delegate,e,t)}useFunctionsEmulator(e){var t=e.match("[a-zA-Z]+://([a-zA-Z0-9.-]+)(?::([0-9]+))?");if(null==t)throw new o("functions","No origin provided to useFunctionsEmulator()");if(null==t[2])throw new o("functions","Port missing in origin provided to useFunctionsEmulator()");return x(this._delegate,t[1],Number(t[2]))}useEmulator(e,t){return x(this._delegate,e,t)}}let R="us-central1",M=(e,{instanceIdentifier:t})=>{var n=e.getProvider("app-compat").getImmediate(),r=e.getProvider("functions").getImmediate({identifier:null!=t?t:R});return new j(n,r)};D={Functions:j},n.default.INTERNAL.registerComponent(new d("functions-compat",M,"PUBLIC").setServiceProps(D).setMultipleInstances(!0)),n.default.registerVersion("@firebase/functions-compat","0.3.26")}).apply(this,arguments)}catch(e){throw console.error(e),new Error("Cannot instantiate firebase-functions-compat.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-functions-compat.js.map
